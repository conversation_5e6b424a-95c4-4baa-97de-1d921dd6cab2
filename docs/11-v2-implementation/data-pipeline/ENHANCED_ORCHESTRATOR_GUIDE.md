# Enhanced Data Pipeline Orchestrator Guide

## Overview

The Enhanced Data Pipeline Orchestrator adds production-ready features to the Yemen Market Integration data pipeline:

- **State Machine**: Track pipeline execution through well-defined states
- **Checkpoint/Resume**: Save progress and resume from failures
- **Dependency Graph**: Execute processors in optimal order with parallelism
- **Resource Monitoring**: Track CPU, memory, and disk usage
- **Health Checks**: Monitor system health and component status
- **Metrics Export**: Prometheus-compatible metrics for monitoring
- **Alert System**: Configurable alerts for pipeline issues

## Architecture

### State Machine

The pipeline follows a deterministic state machine:

```
PENDING → INITIALIZING → COLLECTING → PROCESSING → INTEGRATING → VALIDATING → BUILDING → COMPLETED
                ↓            ↓            ↓             ↓            ↓           ↓
              PAUSED       PAUSED      PAUSED        PAUSED       PAUSED     PAUSED
                ↓            ↓            ↓             ↓            ↓           ↓
             RESUMING    RESUMING    RESUMING      RESUMING     RESUMING   RESUMING
                             ↓                                                  ↓
                          FAILED ←────────────────────────────────────────── FAILED
```

### Processor Dependency Graph

Processors are organized in a directed acyclic graph (DAG):

```
┌─────────────┐  ┌──────────────┐  ┌──────────────┐
│ WFP         │  │ ACLED        │  │ ACAPS        │
│ Collection  │  │ Collection   │  │ Collection   │
└──────┬──────┘  └──────┬───────┘  └──────┬───────┘
       │                 │                  │
       ▼                 ▼                  ▼
┌─────────────┐  ┌──────────────┐  ┌──────────────┐
│ WFP         │  │ ACLED        │  │ ACAPS        │
│ Processing  │  │ Processing   │  │ Processing   │
└──────┬──────┘  └──────┬───────┘  └──────┬───────┘
       │                 │                  │
       └─────────────────┼──────────────────┘
                         ▼
                 ┌──────────────┐
                 │ Spatial      │
                 │ Integration  │
                 └──────┬───────┘
                        │
                        ▼
                 ┌──────────────┐
                 │ Panel        │
                 │ Building     │
                 └──────┬───────┘
                        │
                        ▼
                 ┌──────────────┐
                 │ Validation   │
                 └──────────────┘
```

## Quick Start

### Basic Usage

```python
from src.application.services.enhanced_data_pipeline_orchestrator import (
    EnhancedDataPipelineOrchestrator
)
from src.application.services.data_pipeline_orchestrator import PipelineConfig
from datetime import datetime, timedelta

# Initialize orchestrator
orchestrator = EnhancedDataPipelineOrchestrator(
    ingestion_service=ingestion_service,
    ingestion_orchestrator=ingestion_orchestrator,
    panel_builder_service=panel_builder_service,
    enable_monitoring=True,
    enable_checkpoints=True
)

# Initialize monitoring
await orchestrator.initialize()

# Create pipeline
pipeline = orchestrator.create_pipeline(
    name="Yemen Market Integration Pipeline",
    description="Full data processing pipeline with monitoring"
)

# Configure execution
config = PipelineConfig(
    start_date=datetime.utcnow() - timedelta(days=365),
    end_date=datetime.utcnow(),
    force_refresh=False,
    include_wfp=True,
    include_acled=True,
    include_acaps=True,
    validate_currency=True,
    enforce_coverage=True,
    create_balanced_panel=True
)

# Execute pipeline
execution = await orchestrator.execute_pipeline(
    pipeline=pipeline,
    config=config,
    progress_callback=lambda status: print(f"Progress: {status.current_stage_progress:.1%}")
)

# Check results
print(f"Pipeline completed: {execution.state == PipelineState.COMPLETED}")
print(f"Duration: {execution.execution_metrics.duration_seconds:.1f}s")
print(f"Records processed: {execution.execution_metrics.records_processed}")

# Shutdown
await orchestrator.shutdown()
```

### Resume from Checkpoint

```python
# If pipeline fails, resume from last checkpoint
try:
    execution = await orchestrator.execute_pipeline(pipeline, config)
except Exception as e:
    print(f"Pipeline failed: {e}")
    
    # Find latest checkpoint
    checkpoint_files = list(orchestrator.checkpoint_dir.glob("checkpoint_*.json"))
    if checkpoint_files:
        latest_checkpoint = max(checkpoint_files, key=lambda p: p.stat().st_mtime)
        checkpoint_id = latest_checkpoint.stem.split("_")[1]
        
        # Resume execution
        execution = await orchestrator.execute_pipeline(
            pipeline=pipeline,
            config=config,
            resume_from_checkpoint=UUID(checkpoint_id)
        )
```

### Custom Pipeline Configuration

```python
# Create custom pipeline with specific processors
processors = [
    "hdx_download",
    "wfp_processing",
    "exchange_rate_extraction",
    "currency_zone_mapping",
    "panel_creation",
    "methodology_validation"
]

dependencies = {
    "wfp_processing": ["hdx_download"],
    "exchange_rate_extraction": ["wfp_processing"],
    "currency_zone_mapping": ["wfp_processing"],
    "panel_creation": ["exchange_rate_extraction", "currency_zone_mapping"],
    "methodology_validation": ["panel_creation"]
}

custom_pipeline = orchestrator.create_pipeline(
    name="Custom Exchange Rate Pipeline",
    processors=processors,
    dependencies=dependencies
)

# Configure parallelism
custom_pipeline.max_parallel_processors = 4  # Run up to 4 processors in parallel
custom_pipeline.enable_checkpoints = True
custom_pipeline.checkpoint_interval_seconds = 300  # Checkpoint every 5 minutes
```

## Monitoring Features

### Resource Monitoring

```python
# Get current resource usage
metrics = orchestrator.resource_monitor.get_current_metrics()
print(f"CPU: {metrics.cpu_percent}%")
print(f"Memory: {metrics.memory_mb:.0f}MB ({metrics.memory_percent}%)")
print(f"Disk I/O: {metrics.disk_io_mb:.1f}MB")

# Get average over last 5 minutes
avg_metrics = orchestrator.resource_monitor.get_average_metrics(window_seconds=300)

# Get peak usage
peak_metrics = orchestrator.resource_monitor.get_peak_metrics()

# Wait for resources before heavy processing
available = await orchestrator.resource_monitor.wait_for_resources(
    max_cpu_percent=80,
    max_memory_mb=8192,
    timeout_seconds=300
)
```

### Health Checks

```python
# Get overall health status
health = orchestrator.get_health_status()
print(f"Overall status: {health['overall_status']}")

# Check specific components
for component, result in health['components'].items():
    if result:
        print(f"{component}: {result['status']} - {result['message']}")

# Register custom health check
async def check_data_freshness():
    # Check if data is recent
    latest_file = max(Path("data/processed").glob("*.parquet"), 
                     key=lambda p: p.stat().st_mtime)
    age_hours = (datetime.now() - datetime.fromtimestamp(latest_file.stat().st_mtime)).total_seconds() / 3600
    
    if age_hours > 24:
        return HealthCheckResult(
            component="data_freshness",
            status=HealthStatus.DEGRADED,
            message=f"Data is {age_hours:.1f} hours old"
        )
    
    return HealthCheckResult(
        component="data_freshness",
        status=HealthStatus.HEALTHY,
        message="Data is fresh"
    )

orchestrator.health_checker.register_component(
    "data_freshness",
    check_data_freshness,
    timeout_seconds=10
)
```

### Metrics Collection

```python
# Access metrics collector
collector = orchestrator.metrics_collector

# Record custom metrics
collector.register_metric(
    "yemen_price_differential",
    MetricType.GAUGE,
    "Price differential between North and South Yemen",
    unit="percent"
)

collector.set_gauge("yemen_price_differential", 35.7)

# Use timer for operations
with collector.timer("expensive_calculation"):
    result = perform_expensive_calculation()

# Get metric summary
summary = collector.get_metric_summary("pipeline_processing_duration_seconds")
print(f"Average processing time: {summary['statistics']['mean']:.1f}s")
```

### Prometheus Export

```python
# Export metrics in Prometheus format
metrics_text = orchestrator.export_metrics()
print(metrics_text)

# Save to file for Prometheus scraping
orchestrator.export_metrics("metrics/pipeline_metrics.prom")

# Example output:
# # Yemen Market Integration Pipeline Metrics
# # Generated at 2025-06-13T10:30:00
# 
# # HELP pipeline_records_processed_total Total records processed
# # TYPE pipeline_records_processed_total counter
# pipeline_records_processed_total{processor="wfp_processing",status="success"} 33926
# pipeline_records_processed_total{processor="acled_processing",status="success"} 15234
# 
# # HELP pipeline_processing_duration_seconds Processing duration in seconds
# # TYPE pipeline_processing_duration_seconds histogram
# pipeline_processing_duration_seconds_bucket{le="0.1",processor="validation"} 0
# pipeline_processing_duration_seconds_bucket{le="0.5",processor="validation"} 12
# pipeline_processing_duration_seconds_bucket{le="1.0",processor="validation"} 45
# pipeline_processing_duration_seconds_bucket{le="+Inf",processor="validation"} 50
# pipeline_processing_duration_seconds_sum{processor="validation"} 35.2
# pipeline_processing_duration_seconds_count{processor="validation"} 50
```

### Alert Configuration

```python
# Add custom alert rule
from src.infrastructure.monitoring import AlertRule, AlertLevel

orchestrator.alert_manager.add_rule(
    AlertRule(
        name="low_coverage",
        condition=lambda: get_data_coverage() < 0.884,
        level=AlertLevel.ERROR,
        message_template="Data coverage {coverage}% below 88.4% target",
        cooldown_minutes=30,
        max_alerts_per_hour=2
    )
)

# Add alert handler
async def send_slack_alert(alert):
    # Send alert to Slack
    await slack_client.send_message(
        channel="#data-pipeline-alerts",
        text=f"🚨 {alert.level.value.upper()}: {alert.message}"
    )

orchestrator.alert_manager.add_handler(AlertLevel.ERROR, send_slack_alert)
orchestrator.alert_manager.add_handler(AlertLevel.CRITICAL, send_slack_alert)

# Get active alerts
active_alerts = orchestrator.alert_manager.get_active_alerts(since_minutes=60)
for alert in active_alerts:
    print(f"[{alert.level.value}] {alert.message} (Rule: {alert.rule_name})")

# Acknowledge alert
orchestrator.alert_manager.acknowledge_alert(alert.alert_id, user="operator")
```

## Advanced Features

### Progress Tracking

```python
class DetailedProgressTracker:
    def __init__(self):
        self.stages = []
        self.processors = {}
    
    async def track_progress(self, status):
        self.stages.append({
            "stage": status.stage.value,
            "progress": status.current_stage_progress,
            "timestamp": datetime.utcnow()
        })
        
        # Log detailed progress
        logger.info(
            f"Pipeline progress: {status.stage.value} "
            f"({status.current_stage_progress:.1%})"
        )
        
        # Update UI/dashboard
        await update_dashboard(status)

tracker = DetailedProgressTracker()
execution = await orchestrator.execute_pipeline(
    pipeline,
    config,
    progress_callback=tracker.track_progress
)
```

### Custom Processor Implementation

```python
# Add custom processor to orchestrator
async def process_exchange_rates(config: PipelineConfig) -> Dict[str, any]:
    """Custom processor for exchange rate extraction."""
    logger.info("Processing exchange rates")
    
    # Load WFP data
    wfp_df = pd.read_parquet("data/processed/wfp_prices.parquet")
    
    # Extract exchange rates
    exchange_rates = []
    for _, row in wfp_df.iterrows():
        if row['price_type'] == 'Retail' and row['commodity'] == 'Exchange rate':
            exchange_rates.append({
                'date': row['date'],
                'market': row['market'],
                'rate': row['price'],
                'source': 'WFP'
            })
    
    # Save results
    rates_df = pd.DataFrame(exchange_rates)
    rates_df.to_parquet("data/processed/exchange_rates.parquet")
    
    return {"records_processed": len(exchange_rates)}

# Register processor
orchestrator._processor_handlers["exchange_rate_extraction"] = process_exchange_rates
```

### Monitoring Dashboard Integration

```python
# Create Grafana dashboard configuration
dashboard_config = orchestrator.prometheus_exporter.create_grafana_dashboard()

# Add custom panels
dashboard_config["dashboard"]["panels"].extend([
    {
        "title": "Exchange Rate Spread",
        "targets": [
            {"expr": "yemen_exchange_rate_north"},
            {"expr": "yemen_exchange_rate_south"}
        ]
    },
    {
        "title": "Data Coverage by Source",
        "targets": [
            {"expr": "pipeline_data_coverage{source='wfp'}"},
            {"expr": "pipeline_data_coverage{source='acled'}"},
            {"expr": "pipeline_data_coverage{source='acaps'}"}
        ]
    }
])

# Save dashboard configuration
with open("grafana/dashboards/pipeline.json", "w") as f:
    json.dump(dashboard_config, f, indent=2)
```

## Production Deployment

### Docker Configuration

```dockerfile
# Dockerfile for pipeline worker
FROM python:3.11-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gdal-bin \
    libgdal-dev \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt .
RUN pip install -r requirements.txt

# Copy application
COPY src/ /app/src/
COPY config/ /app/config/
WORKDIR /app

# Configure monitoring
ENV PROMETHEUS_PORT=9090
ENV HEALTH_CHECK_PORT=8080

# Run enhanced orchestrator
CMD ["python", "-m", "src.application.services.enhanced_orchestrator_worker"]
```

### Kubernetes Deployment

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: pipeline-orchestrator
spec:
  replicas: 1
  selector:
    matchLabels:
      app: pipeline-orchestrator
  template:
    metadata:
      labels:
        app: pipeline-orchestrator
    spec:
      containers:
      - name: orchestrator
        image: yemen-market-integration:latest
        resources:
          requests:
            memory: "4Gi"
            cpu: "2"
          limits:
            memory: "8Gi"
            cpu: "4"
        ports:
        - containerPort: 9090
          name: metrics
        - containerPort: 8080
          name: health
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 10
        volumeMounts:
        - name: checkpoints
          mountPath: /data/checkpoints
        - name: data
          mountPath: /data
      volumes:
      - name: checkpoints
        persistentVolumeClaim:
          claimName: pipeline-checkpoints
      - name: data
        persistentVolumeClaim:
          claimName: pipeline-data
```

### Monitoring Setup

```yaml
# prometheus-config.yaml
global:
  scrape_interval: 30s

scrape_configs:
  - job_name: 'pipeline-orchestrator'
    static_configs:
      - targets: ['pipeline-orchestrator:9090']
    
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']

alerting:
  alertmanagers:
    - static_configs:
        - targets: ['alertmanager:9093']

rule_files:
  - 'alerts/*.yml'
```

## Troubleshooting

### Common Issues

1. **Pipeline Stuck in State**
   ```python
   # Force state transition (use with caution)
   execution = orchestrator._executions[execution_id]
   execution.state = PipelineState.FAILED
   ```

2. **Checkpoint Corruption**
   ```python
   # Validate all checkpoints
   for checkpoint_file in orchestrator.checkpoint_dir.glob("checkpoint_*.json"):
       try:
           with open(checkpoint_file) as f:
               data = json.load(f)
           print(f"✓ {checkpoint_file.name}")
       except Exception as e:
           print(f"✗ {checkpoint_file.name}: {e}")
   ```

3. **Memory Leaks**
   ```python
   # Clear old metrics
   orchestrator.metrics_collector.clear_old_values(older_than_hours=24)
   
   # Limit checkpoint history
   checkpoints = sorted(orchestrator.checkpoint_dir.glob("checkpoint_*.json"))
   if len(checkpoints) > 100:
       for old_checkpoint in checkpoints[:-100]:
           old_checkpoint.unlink()
   ```

4. **Slow Processing**
   ```python
   # Check processor performance
   for name, metric in orchestrator.metrics_collector.get_all_metrics().items():
       if "duration" in name:
           stats = metric.get_statistics()
           if stats and stats['mean'] > 60:  # Over 1 minute average
               print(f"Slow processor: {name} - {stats['mean']:.1f}s average")
   ```

## Best Practices

1. **Always Initialize and Shutdown**
   ```python
   try:
       await orchestrator.initialize()
       # Run pipeline
   finally:
       await orchestrator.shutdown()
   ```

2. **Use Context Managers for Resources**
   ```python
   async with EnhancedOrchestrator(...) as orchestrator:
       await orchestrator.execute_pipeline(pipeline, config)
   ```

3. **Monitor Resource Usage**
   - Set appropriate CPU/memory limits
   - Use wait_for_resources before heavy operations
   - Monitor peak usage patterns

4. **Regular Checkpoint Cleanup**
   - Delete old checkpoints after successful runs
   - Validate checkpoints before resume
   - Store checkpoint metadata separately

5. **Configure Alerts Wisely**
   - Set appropriate cooldown periods
   - Use rate limiting to prevent spam
   - Test alert handlers thoroughly

## Performance Optimization

1. **Optimize Parallelism**
   ```python
   # Adjust based on system resources
   pipeline.max_parallel_processors = min(
       os.cpu_count(),
       available_memory_gb // 2  # 2GB per processor
   )
   ```

2. **Batch Operations**
   ```python
   # Process in chunks to control memory
   async def process_large_dataset(config):
       chunk_size = 10000
       total_processed = 0
       
       for chunk in pd.read_parquet("large_file.parquet", chunksize=chunk_size):
           processed = await process_chunk(chunk)
           total_processed += processed
           
           # Update metrics
           orchestrator.metrics_collector.set_gauge(
               "processing_progress",
               total_processed
           )
       
       return {"records_processed": total_processed}
   ```

3. **Cache Intermediate Results**
   ```python
   # Use checkpoints for expensive computations
   checkpoint_data = {
       "expensive_result": expensive_computation_result,
       "computation_params": params
   }
   execution.last_checkpoint.data_snapshots["expensive_computation"] = checkpoint_data
   ```

## Conclusion

The Enhanced Data Pipeline Orchestrator provides production-ready features for running complex data pipelines with confidence. By leveraging state machines, checkpointing, monitoring, and alerting, you can build resilient data processing workflows that handle failures gracefully and provide visibility into system health and performance.