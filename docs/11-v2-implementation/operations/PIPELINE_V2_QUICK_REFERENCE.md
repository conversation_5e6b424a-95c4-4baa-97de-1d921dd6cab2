# Data Pipeline V2 - Quick Reference Card

## 🚀 Essential Commands

### Pipeline Operations
```bash
# Run full pipeline
uv run python src/cli.py pipeline run

# Run with date range
uv run python src/cli.py pipeline run --start-date 2024-01-01 --end-date 2024-12-31

# Dry run (show plan)
uv run python src/cli.py pipeline run --dry-run

# Run specific sources
uv run python src/cli.py pipeline run --sources prices exchange_rates conflict
```

### Status & Monitoring
```bash
# Check latest status
uv run python src/cli.py pipeline status

# Check specific pipeline
uv run python src/cli.py pipeline status --pipeline-id pipeline_20250611_143022

# List all runs
uv run python src/cli.py pipeline status --show-all
```

### Validation
```bash
# Validate output
uv run python src/cli.py pipeline validate data/processed/panel.parquet

# Strict validation
uv run python src/cli.py pipeline validate <file> --level strict

# Save validation report
uv run python src/cli.py pipeline validate <file> --report-path validation_report.json
```

### Data Sources
```bash
# List all sources
uv run python src/cli.py pipeline sources --list-all

# Test source connection
uv run python src/cli.py pipeline sources --test wfp

# Get source info
uv run python src/cli.py pipeline sources --info conflict
```

## 📁 Key Directories

| Directory | Purpose |
|-----------|---------|
| `data/processed/integrated_panel/` | Final output panels |
| `data/interim/` | Intermediate processed data |
| `data/raw/` | Original downloaded data |
| `data/.cache/` | Cached data (can delete if needed) |
| `logs/pipeline/` | Pipeline execution logs |
| `logs/errors/` | Error logs with stack traces |
| `data/pipeline_status/` | Status tracking files |

## 🔧 Common Troubleshooting

### Memory Issues
```bash
# Reduce memory usage
uv run python src/cli.py pipeline run --no-parallel --chunk-size 10000
```

### Connection Issues
```bash
# Use cache fallback
uv run python src/cli.py pipeline run --use-cache-fallback

# Force refresh specific source
uv run python src/cli.py data update-data wfp --force
```

### Validation Failures
```bash
# Check exchange rates
uv run python src/cli.py pipeline validate data/interim/exchange_rates.parquet

# Re-run with fixes
uv run python src/cli.py pipeline run --sources exchange_rates prices
```

## 📊 Key Metrics

| Metric | Target | Warning | Critical |
|--------|--------|---------|----------|
| Coverage | ≥88.4% | <85% | <80% |
| Processing Time | <30 min | >45 min | >60 min |
| Memory Usage | <8GB | >10GB | >14GB |
| Validation Pass | 100% | <100% | Any failure |

## 🚨 Emergency Commands

```bash
# Kill hung pipeline
ps aux | grep pipeline | grep -v grep | awk '{print $2}' | xargs kill -INT

# Clear locks
rm -f data/.locks/pipeline_*.lock

# Clear corrupted cache
rm -rf data/.cache/*

# Restore from backup
cp data/backup/latest/*.parquet data/processed/
```

## ⚙️ Configuration

### Quick Config Changes
```bash
# Show current config
uv run python src/cli.py pipeline config --show

# Use custom config
uv run python src/cli.py pipeline run --config-file my_config.json
```

### Config Template
```json
{
  "start_date": "2019-01-01",
  "end_date": "2025-03-01",
  "sources": ["all"],
  "validation_level": "standard",
  "parallel": true,
  "use_cache": true
}
```

## 📈 Performance Tips

1. **Use caching** (default on)
2. **Run in parallel** (default on)
3. **Process date ranges incrementally** for large periods
4. **Monitor with `htop`** in separate terminal
5. **Check disk space** before running

## 🔍 Log Analysis

```bash
# Check for errors
grep -i "error" logs/pipeline/latest.log

# Check for warnings
grep -i "warning" logs/pipeline/latest.log

# Get processing times
grep "completed in" logs/pipeline/latest.log

# Monitor in real-time
tail -f logs/pipeline/current.log
```

## 📝 Output Files

### Main Output
- `yemen_integrated_balanced_panel.parquet` - Final panel
- `validation_report.json` - Validation results
- `processing_summary.json` - Pipeline metrics

### File Naming Convention
```
{pipeline_id}_{component}_{timestamp}.{format}
Example: pipeline_20250611_143022_panel_final.parquet
```

## 🆘 Getting Help

1. Check operator guide: `docs/11-v2-implementation/operations/PIPELINE_V2_OPERATOR_GUIDE.md`
2. Review logs in `logs/pipeline/` and `logs/errors/`
3. Run with debug: `LOG_LEVEL=DEBUG uv run python src/cli.py pipeline run`

## 🎯 Success Checklist

- [ ] Pipeline completes without errors
- [ ] Coverage ≥88.4%
- [ ] All validations pass
- [ ] Processing time <30 minutes
- [ ] Output file created
- [ ] No critical warnings in logs

---

*Keep this card handy during pipeline operations!*