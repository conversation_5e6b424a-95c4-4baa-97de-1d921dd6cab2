# Data Pipeline V2 Operator's Guide

## Overview

The Data Pipeline V2 is a zero-error production system designed to integrate 10+ heterogeneous data sources for Yemen market analysis. This guide provides operational procedures for running, monitoring, and troubleshooting the pipeline.

## Table of Contents

1. [Quick Start](#quick-start)
2. [Pipeline Architecture](#pipeline-architecture)
3. [Running the Pipeline](#running-the-pipeline)
4. [Monitoring and Status](#monitoring-and-status)
5. [Data Validation](#data-validation)
6. [Troubleshooting](#troubleshooting)
7. [Configuration Management](#configuration-management)
8. [Migration from Old Pipeline](#migration-from-old-pipeline)
9. [Performance Optimization](#performance-optimization)
10. [Emergency Procedures](#emergency-procedures)

## Quick Start

### Basic Pipeline Run

```bash
# Run full pipeline with default settings
uv run python src/cli.py pipeline run

# Run with specific date range
uv run python src/cli.py pipeline run \
    --start-date 2024-01-01 \
    --end-date 2024-12-31

# Dry run to see execution plan
uv run python src/cli.py pipeline run --dry-run
```

### Check Pipeline Status

```bash
# Check latest pipeline run
uv run python src/cli.py pipeline status

# Check specific pipeline
uv run python src/cli.py pipeline status --pipeline-id pipeline_20250611_143022
```

### Validate Output

```bash
# Validate pipeline output
uv run python src/cli.py pipeline validate data/processed/integrated_panel/latest.parquet

# Run strict validation
uv run python src/cli.py pipeline validate data/processed/panel.parquet --level strict
```

## Pipeline Architecture

### Components

1. **Data Sources** (10+ types)
   - WFP Market Prices
   - ACLED Conflict Events
   - OCHA Aid Distribution
   - Climate Data (CHIRPS, MODIS)
   - Population (WorldPop, IOM DTM)
   - Infrastructure (OSM)
   - Control Zones (ACAPS)
   - Global Prices (FAO GIEWS)
   - IPC Food Security
   - Exchange Rates

2. **Processing Stages**
   - Collection: Download/load raw data
   - Processing: Clean and standardize
   - Integration: Spatial/temporal joining
   - Validation: Quality checks
   - Panel Building: Create analysis-ready dataset

3. **Output**
   - Integrated panel dataset (Parquet format)
   - Validation reports
   - Processing logs
   - Quality metrics

### Data Flow

```
Raw Sources → Processors → Validation → Panel Builder → Derived Variables → Final Panel
     ↓             ↓            ↓             ↓                ↓              ↓
   Cache      Retry/Error   Quality      Integration      Calculation    Output
  Manager      Handler      Reports       Metrics         Reports       Files
```

## Running the Pipeline

### Full Pipeline Run

```bash
# Standard run with all sources
uv run python src/cli.py pipeline run \
    --start-date 2019-01-01 \
    --end-date 2025-03-01 \
    --output-dir data/processed/v2

# Run specific sources only
uv run python src/cli.py pipeline run \
    --sources prices exchange_rates conflict \
    --output-dir data/processed/partial
```

### Configuration Options

```bash
# Use configuration file
uv run python src/cli.py pipeline run --config-file config/pipeline_v2.json

# Disable caching (force fresh data)
uv run python src/cli.py pipeline run --no-cache

# Run sequentially (no parallel processing)
uv run python src/cli.py pipeline run --no-parallel
```

### Advanced Options

```bash
# Run with custom validation level
uv run python src/cli.py pipeline run --validation-level basic

# Save validation report
uv run python src/cli.py pipeline run --save-validation-report

# Use specific processors
uv run python src/cli.py pipeline run \
    --processor-config config/processors.yaml
```

## Monitoring and Status

### Real-time Monitoring

During pipeline execution:
- Progress bars show completion for each stage
- Resource usage (CPU, memory) displayed
- Current processing stage highlighted
- Estimated time remaining

### Status Commands

```bash
# List all recent pipeline runs
uv run python src/cli.py pipeline status --show-all

# Get detailed status for specific run
uv run python src/cli.py pipeline status \
    --pipeline-id pipeline_20250611_143022 \
    --detailed
```

### Log Files

Logs are stored in:
- `logs/pipeline/`: Main pipeline logs
- `logs/processors/`: Individual processor logs
- `logs/validation/`: Validation reports
- `logs/errors/`: Error logs with stack traces

### Metrics Dashboard

Key metrics to monitor:
- **Coverage**: Should be ≥88.4%
- **Processing Time**: Typically 15-30 minutes
- **Memory Usage**: Peak ~4-8GB
- **Error Rate**: Should be <1%
- **Validation Pass Rate**: Should be 100%

## Data Validation

### Validation Levels

1. **BASIC**: Only catastrophic errors stop processing
2. **STANDARD**: Critical errors stop, warnings logged
3. **STRICT**: Any validation failure stops processing

### Running Validation

```bash
# Validate existing data
uv run python src/cli.py pipeline validate \
    data/processed/panel.parquet \
    --level strict

# Validate with specific source type
uv run python src/cli.py pipeline validate \
    data/raw/wfp_prices.csv \
    --source-type prices \
    --level standard

# Generate validation report
uv run python src/cli.py pipeline validate \
    data/processed/panel.parquet \
    --report-path reports/validation_$(date +%Y%m%d).json
```

### Common Validation Checks

1. **Schema Validation**
   - Required columns present
   - Data types correct
   - Index uniqueness

2. **Constraint Validation**
   - Price ranges (0.01 - 1,000,000)
   - Exchange rates (100 - 3,000 YER/USD)
   - Coordinates within Yemen bounds

3. **Statistical Validation**
   - Outlier detection
   - Sudden price jumps (>200%)
   - Missing data patterns

4. **Business Logic Validation**
   - Currency zones assigned
   - USD prices calculated
   - Temporal consistency

## Troubleshooting

### Common Issues and Solutions

#### 1. Pipeline Fails at Collection Stage

**Symptoms**: "Connection timeout" or "API error"

**Solutions**:
```bash
# Check data source connectivity
uv run python src/cli.py pipeline sources --test wfp

# Use cached data if available
uv run python src/cli.py pipeline run --use-cache-fallback

# Run specific source separately
uv run python src/cli.py data update-data wfp --force
```

#### 2. Memory Errors

**Symptoms**: "MemoryError" or system slowdown

**Solutions**:
```bash
# Reduce parallel processing
uv run python src/cli.py pipeline run --no-parallel

# Process in chunks
uv run python src/cli.py pipeline run \
    --chunk-size 10000 \
    --memory-limit 4G
```

#### 3. Validation Failures

**Symptoms**: "Validation failed: Missing USD prices"

**Solutions**:
```bash
# Check exchange rate data
uv run python src/cli.py pipeline validate \
    data/interim/exchange_rates.parquet \
    --source-type exchange_rates

# Re-run with exchange rate fix
uv run python src/cli.py pipeline run \
    --sources exchange_rates prices \
    --force-exchange-rate-update
```

#### 4. Output File Issues

**Symptoms**: "Permission denied" or "No space left"

**Solutions**:
```bash
# Check disk space
df -h data/processed/

# Use alternative output directory
uv run python src/cli.py pipeline run \
    --output-dir /tmp/pipeline_output

# Clean old outputs
find data/processed -name "*.parquet" -mtime +30 -delete
```

### Debug Mode

```bash
# Run with debug logging
LOG_LEVEL=DEBUG uv run python src/cli.py pipeline run

# Save debug information
uv run python src/cli.py pipeline run \
    --debug \
    --debug-output debug_$(date +%Y%m%d_%H%M%S).tar.gz
```

## Configuration Management

### Configuration File Format

```json
{
  "pipeline": {
    "start_date": "2019-01-01",
    "end_date": "2025-03-01",
    "sources": ["prices", "exchange_rates", "conflict"],
    "output_dir": "data/processed/v2",
    "validation_level": "standard"
  },
  "processors": {
    "prices": {
      "chunk_size": 50000,
      "retry_attempts": 3
    },
    "conflict": {
      "buffer_distance_km": 25,
      "temporal_lag_days": 30
    }
  },
  "performance": {
    "parallel": true,
    "max_workers": 4,
    "memory_limit_gb": 8
  }
}
```

### Managing Configurations

```bash
# Show current configuration
uv run python src/cli.py pipeline config --show

# Import configuration
uv run python src/cli.py pipeline config \
    --import-from config/production.json

# Export configuration
uv run python src/cli.py pipeline config \
    --export config/backup_$(date +%Y%m%d).json
```

## Migration from Old Pipeline

### Migrating Existing Data

```bash
# Migrate old format data
uv run python scripts/migrate_to_v2.py \
    --input-dir data/raw/old_format \
    --output-dir data/raw/v2_format \
    --source-type wfp_prices

# Validate migrated data
uv run python src/cli.py pipeline validate \
    data/raw/v2_format/*.parquet \
    --source-type prices
```

### Compatibility Check

```bash
# Check if old data is compatible
uv run python scripts/check_compatibility.py \
    --old-data data/processed/old_panel.csv \
    --new-data data/processed/v2_panel.parquet
```

## Performance Optimization

### Optimizing Pipeline Performance

1. **Use Caching**
   ```bash
   # Warm up cache
   uv run python src/cli.py pipeline run --cache-only
   
   # Check cache status
   uv run python src/cli.py pipeline cache --status
   ```

2. **Parallel Processing**
   ```bash
   # Optimize worker count
   uv run python src/cli.py pipeline run --max-workers $(nproc)
   ```

3. **Memory Management**
   ```bash
   # Use memory profiling
   uv run python src/cli.py pipeline run --profile-memory
   ```

### Performance Benchmarks

Typical performance on 8-core, 16GB RAM system:
- Full pipeline: 15-30 minutes
- Prices only: 3-5 minutes
- With caching: 5-10 minutes
- Memory peak: 4-6GB

## Emergency Procedures

### Pipeline Hung/Frozen

1. Check process status:
   ```bash
   ps aux | grep pipeline
   ```

2. Send interrupt signal:
   ```bash
   kill -INT <pid>
   ```

3. Force kill if needed:
   ```bash
   kill -9 <pid>
   ```

4. Clean up locks:
   ```bash
   rm -f data/.locks/pipeline_*.lock
   ```

### Data Corruption

1. Validate all data:
   ```bash
   uv run python src/cli.py pipeline validate data/processed/ --recursive
   ```

2. Restore from backup:
   ```bash
   cp data/backup/latest/*.parquet data/processed/
   ```

3. Re-run affected stages:
   ```bash
   uv run python src/cli.py pipeline run --stages integration validation panel
   ```

### Recovery Checklist

- [ ] Stop running processes
- [ ] Check system resources (disk, memory)
- [ ] Review error logs
- [ ] Clear cache if corrupted
- [ ] Restore from backup if needed
- [ ] Run validation on recovered data
- [ ] Document incident

## Best Practices

1. **Always validate before analysis**
   ```bash
   uv run python src/cli.py pipeline validate output.parquet
   ```

2. **Keep backups of successful runs**
   ```bash
   cp data/processed/panel.parquet data/backup/panel_$(date +%Y%m%d).parquet
   ```

3. **Monitor resource usage**
   ```bash
   htop  # In another terminal during pipeline run
   ```

4. **Use configuration files for reproducibility**
   ```bash
   uv run python src/cli.py pipeline run --config-file config/standard_run.json
   ```

5. **Check logs for warnings**
   ```bash
   grep -i "warning" logs/pipeline/latest.log
   ```

## Quick Reference Card

### Essential Commands

```bash
# Run pipeline
uv run python src/cli.py pipeline run

# Check status
uv run python src/cli.py pipeline status

# Validate data
uv run python src/cli.py pipeline validate <file>

# List sources
uv run python src/cli.py pipeline sources --list-all

# Test source
uv run python src/cli.py pipeline sources --test <source>

# Show config
uv run python src/cli.py pipeline config --show

# Get help
uv run python src/cli.py pipeline --help
```

### Key File Locations

- Pipeline output: `data/processed/integrated_panel/`
- Logs: `logs/pipeline/`
- Cache: `data/.cache/`
- Config: `~/.yemen_market/pipeline_v2_config.json`
- Status: `data/pipeline_status/`

### Support

For issues not covered in this guide:
1. Check extended documentation in `docs/`
2. Review source code in `src/infrastructure/processors/`
3. Contact development team with:
   - Pipeline ID
   - Error logs
   - Configuration used

---

*Remember: The pipeline is designed for zero-error operation. Any errors indicate a problem that needs investigation.*