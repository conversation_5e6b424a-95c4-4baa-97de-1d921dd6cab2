"""Example of running the data pipeline with resilience and monitoring."""

import asyncio
import logging
from datetime import datetime
from pathlib import Path
import sys

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from src.application.services.data_pipeline_orchestrator import (
    DataPipelineOrchestrator,
    PipelineConfig
)
from src.infrastructure.monitoring.metrics_collector import MetricsCollector
from src.infrastructure.monitoring.health_checker import HealthChecker
from src.infrastructure.queues import DeadLetterQueue
from src.infrastructure.resilience import circuit_registry
from src.infrastructure.dashboards import PipelineDashboard, DataQualityDashboard
from src.infrastructure.processors.resilient_processor_wrapper import (
    ResilientProcessorFactory
)
from src.core.config import Config
from src.infrastructure.processors import ProcessorFactory
from src.infrastructure.external_services import HDXClient
from src.infrastructure.monitoring.prometheus_exporter import PrometheusExporter

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def setup_resilient_pipeline():
    """Setup pipeline with all resilience and monitoring components."""
    
    # Load configuration
    config = Config()
    
    # Initialize core components
    hdx_client = HDXClient(api_key=config.hdx_api_key)
    processor_factory = ProcessorFactory(hdx_client=hdx_client)
    
    # Initialize monitoring
    metrics_collector = MetricsCollector(
        storage_path=Path("./metrics_data"),
        retention_days=7
    )
    
    health_checker = HealthChecker()
    prometheus_exporter = PrometheusExporter(port=9090)
    
    # Initialize resilience components
    dlq = DeadLetterQueue(
        storage_path=Path("./dlq_storage"),
        max_queue_size=10000
    )
    await dlq.start()
    
    resilient_factory = ResilientProcessorFactory(dlq)
    
    # Register DLQ retry handlers
    async def retry_wfp_data(data):
        """Retry handler for WFP data."""
        processor = processor_factory.create_processor("wfp")
        return await processor.process(data)
        
    async def retry_conflict_data(data):
        """Retry handler for conflict data."""
        processor = processor_factory.create_processor("conflict")
        return await processor.process(data)
        
    dlq.register_retry_handler("WFPProcessor", retry_wfp_data)
    dlq.register_retry_handler("ConflictProcessor", retry_conflict_data)
    
    # Create pipeline configuration
    pipeline_config = PipelineConfig(
        name="yemen_resilient_pipeline",
        processors=[
            "wfp", "conflict", "climate", "population",
            "infrastructure", "aid", "global_prices", "ipc"
        ],
        parallel_execution=True,
        max_workers=4,
        validation_level="strict",
        cache_enabled=True,
        monitoring_enabled=True
    )
    
    # Create orchestrator with resilience
    orchestrator = DataPipelineOrchestrator(
        config=pipeline_config,
        processor_factory=processor_factory,
        metrics_collector=metrics_collector,
        health_checker=health_checker
    )
    
    # Wrap processors with resilience
    for processor_name in pipeline_config.processors:
        processor = processor_factory.create_processor(processor_name)
        resilient_processor = resilient_factory.wrap_processor(processor)
        # Replace in orchestrator (this would need orchestrator modification)
        
    return orchestrator, metrics_collector, health_checker, dlq, prometheus_exporter


async def run_with_monitoring():
    """Run pipeline with monitoring dashboards."""
    
    # Setup components
    orchestrator, metrics, health, dlq, prometheus = await setup_resilient_pipeline()
    
    # Start Prometheus exporter
    await prometheus.start()
    
    # Create dashboards
    pipeline_dashboard = PipelineDashboard(
        orchestrator=orchestrator,
        metrics_collector=metrics,
        health_checker=health,
        dlq=dlq
    )
    
    quality_dashboard = DataQualityDashboard(
        storage_path="./quality_metrics"
    )
    
    # Start dashboards in background
    dashboard_task = asyncio.create_task(
        asyncio.to_thread(pipeline_dashboard.run, debug=False, port=8050)
    )
    quality_task = asyncio.create_task(
        asyncio.to_thread(quality_dashboard.run, debug=False, port=8051)
    )
    
    logger.info("Dashboards started:")
    logger.info("- Pipeline Dashboard: http://localhost:8050")
    logger.info("- Data Quality Dashboard: http://localhost:8051")
    logger.info("- Prometheus Metrics: http://localhost:9090/metrics")
    
    # Run pipeline
    try:
        logger.info("Starting resilient pipeline execution...")
        
        # Execute pipeline
        await orchestrator.initialize()
        results = await orchestrator.execute()
        
        logger.info(f"Pipeline completed successfully!")
        logger.info(f"Processed {results.get('total_records', 0)} records")
        
        # Monitor for a while
        logger.info("Monitoring pipeline... Press Ctrl+C to stop")
        
        while True:
            # Print status every 30 seconds
            await asyncio.sleep(30)
            
            # Get current metrics
            pipeline_metrics = await orchestrator.get_metrics()
            dlq_stats = dlq.get_stats()
            circuit_stats = circuit_registry.get_all_stats()
            
            logger.info("=== Status Update ===")
            logger.info(f"Pipeline State: {pipeline_metrics['current_state']}")
            logger.info(f"DLQ Size: {dlq_stats['queue_size']}")
            
            # Check circuit breakers
            open_circuits = [
                name for name, stats in circuit_stats.items()
                if stats['state'] == 'open'
            ]
            if open_circuits:
                logger.warning(f"Open Circuits: {', '.join(open_circuits)}")
            else:
                logger.info("All circuits healthy")
                
    except KeyboardInterrupt:
        logger.info("Shutting down...")
    except Exception as e:
        logger.error(f"Pipeline failed: {e}", exc_info=True)
    finally:
        # Cleanup
        await orchestrator.shutdown()
        await dlq.stop()
        await prometheus.stop()
        dashboard_task.cancel()
        quality_task.cancel()


async def demonstrate_failure_recovery():
    """Demonstrate failure recovery scenarios."""
    logger.info("=== Demonstrating Failure Recovery ===")
    
    # Simulate various failure scenarios
    
    # 1. Circuit Breaker Demo
    logger.info("\n1. Circuit Breaker Protection:")
    hdx_circuit = circuit_registry.register("hdx_api")
    
    # Simulate failures
    for i in range(6):
        try:
            # This would be actual API call
            if i < 5:
                raise ConnectionError("API unavailable")
        except Exception as e:
            await hdx_circuit._record_failure(e)
            
    logger.info(f"Circuit State: {hdx_circuit.state.value}")
    logger.info(f"Circuit will reset in {hdx_circuit.config.recovery_timeout} seconds")
    
    # 2. Retry Strategy Demo
    logger.info("\n2. Retry with Exponential Backoff:")
    from src.infrastructure.resilience import ExponentialBackoffStrategy, RetryConfig
    
    strategy = ExponentialBackoffStrategy(
        RetryConfig(
            max_attempts=4,
            initial_delay=1,
            exponential_base=2
        )
    )
    
    for attempt in range(1, 5):
        delay = strategy.calculate_delay(attempt)
        logger.info(f"Attempt {attempt}: Delay = {delay}s")
        
    # 3. Dead Letter Queue Demo
    logger.info("\n3. Dead Letter Queue Recovery:")
    
    # Setup DLQ
    dlq = DeadLetterQueue(storage_path=Path("./dlq_demo"))
    await dlq.start()
    
    # Add failed record
    record_id = await dlq.enqueue(
        source="demo_processor",
        record_type="price_data",
        data={"market": "Sana'a", "price": 1000},
        error=ValueError("Invalid currency zone")
    )
    
    logger.info(f"Added to DLQ: {record_id}")
    logger.info(f"DLQ Stats: {dlq.get_stats()}")
    
    await dlq.stop()


async def main():
    """Main entry point."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Run resilient data pipeline")
    parser.add_argument(
        "--mode",
        choices=["run", "demo"],
        default="run",
        help="Run mode: 'run' for full pipeline, 'demo' for failure scenarios"
    )
    
    args = parser.parse_args()
    
    if args.mode == "run":
        await run_with_monitoring()
    else:
        await demonstrate_failure_recovery()


if __name__ == "__main__":
    asyncio.run(main())