"""Tests for enhanced data pipeline orchestrator."""

import asyncio
import json
import pytest
from datetime import datetime
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch
from uuid import uuid4

from src.application.services.enhanced_data_pipeline_orchestrator import (
    EnhancedDataPipelineOrchestrator
)
from src.application.services.data_pipeline_orchestrator import PipelineConfig
from src.core.domain.pipeline.entities import (
    Pipeline, PipelineExecution, PipelineCheckpoint, ProcessorNode
)
from src.core.domain.pipeline.value_objects import (
    PipelineState, ProcessorStatus, DependencyType
)


@pytest.fixture
def mock_services():
    """Create mock services."""
    ingestion_service = Mock()
    ingestion_orchestrator = Mock()
    panel_builder_service = Mock()
    
    return ingestion_service, ingestion_orchestrator, panel_builder_service


@pytest.fixture
async def orchestrator(mock_services, tmp_path):
    """Create enhanced orchestrator instance."""
    ingestion_service, ingestion_orchestrator, panel_builder_service = mock_services
    
    orch = EnhancedDataPipelineOrchestrator(
        ingestion_service=ingestion_service,
        ingestion_orchestrator=ingestion_orchestrator,
        panel_builder_service=panel_builder_service,
        checkpoint_dir=tmp_path / "checkpoints",
        enable_monitoring=True,
        enable_checkpoints=True
    )
    
    await orch.initialize()
    yield orch
    await orch.shutdown()


class TestPipelineCreation:
    """Test pipeline creation and configuration."""
    
    @pytest.mark.asyncio
    async def test_create_default_pipeline(self, orchestrator):
        """Test creating pipeline with default configuration."""
        pipeline = orchestrator.create_pipeline(
            name="Test Pipeline",
            description="Test pipeline for unit tests"
        )
        
        assert pipeline.name == "Test Pipeline"
        assert len(pipeline.processors) == 9  # Default processors
        assert "wfp_collection" in pipeline.processors
        assert "validation" in pipeline.processors
        
        # Check dependencies
        assert "wfp_processing" in pipeline.dependencies
        assert "wfp_collection" in pipeline.dependencies["wfp_processing"]
    
    @pytest.mark.asyncio
    async def test_create_custom_pipeline(self, orchestrator):
        """Test creating pipeline with custom configuration."""
        processors = ["proc1", "proc2", "proc3"]
        dependencies = {
            "proc2": ["proc1"],
            "proc3": ["proc1", "proc2"]
        }
        
        pipeline = orchestrator.create_pipeline(
            name="Custom Pipeline",
            processors=processors,
            dependencies=dependencies
        )
        
        assert pipeline.processors == processors
        assert pipeline.dependencies == dependencies
    
    @pytest.mark.asyncio
    async def test_pipeline_execution_creation(self, orchestrator):
        """Test pipeline execution creation."""
        pipeline = orchestrator.create_pipeline("Test")
        execution = pipeline.create_execution()
        
        assert execution.pipeline_id == pipeline.pipeline_id
        assert execution.state == PipelineState.PENDING
        assert len(execution.processor_graph) == len(pipeline.processors)
        
        # Check processor nodes
        for proc_id in pipeline.processors:
            assert proc_id in execution.processor_graph
            node = execution.processor_graph[proc_id]
            assert isinstance(node, ProcessorNode)
            assert node.status == ProcessorStatus.PENDING
    
    @pytest.mark.asyncio
    async def test_execution_order_calculation(self, orchestrator):
        """Test topological sort for execution order."""
        processors = ["a", "b", "c", "d", "e"]
        dependencies = {
            "b": ["a"],
            "c": ["a"],
            "d": ["b", "c"],
            "e": ["d"]
        }
        
        pipeline = orchestrator.create_pipeline(
            "Test",
            processors=processors,
            dependencies=dependencies
        )
        execution = pipeline.create_execution()
        
        # Check execution order
        assert len(execution.execution_order) > 0
        
        # First group should contain only 'a'
        assert "a" in execution.execution_order[0]
        
        # 'e' should be in the last group
        assert "e" in execution.execution_order[-1]


class TestStateMachine:
    """Test pipeline state machine functionality."""
    
    @pytest.mark.asyncio
    async def test_state_transitions(self, orchestrator):
        """Test valid state transitions."""
        pipeline = orchestrator.create_pipeline("Test")
        execution = pipeline.create_execution()
        
        # Test valid transitions
        assert execution.state == PipelineState.PENDING
        assert execution.transition_to(PipelineState.INITIALIZING)
        assert execution.state == PipelineState.INITIALIZING
        
        assert execution.transition_to(PipelineState.COLLECTING)
        assert execution.transition_to(PipelineState.PROCESSING)
        assert execution.transition_to(PipelineState.INTEGRATING)
        assert execution.transition_to(PipelineState.VALIDATING)
        assert execution.transition_to(PipelineState.BUILDING)
        assert execution.transition_to(PipelineState.COMPLETED)
        
        # Cannot transition from terminal state
        assert not execution.transition_to(PipelineState.COLLECTING)
    
    @pytest.mark.asyncio
    async def test_pause_resume_transitions(self, orchestrator):
        """Test pause and resume transitions."""
        pipeline = orchestrator.create_pipeline("Test")
        execution = pipeline.create_execution()
        
        # Move to processing
        execution.transition_to(PipelineState.INITIALIZING)
        execution.transition_to(PipelineState.COLLECTING)
        execution.transition_to(PipelineState.PROCESSING)
        
        # Pause
        assert execution.transition_to(PipelineState.PAUSED)
        
        # Can only resume or cancel from paused
        assert not execution.transition_to(PipelineState.PROCESSING)
        assert execution.transition_to(PipelineState.RESUMING)
    
    @pytest.mark.asyncio
    async def test_failed_state_handling(self, orchestrator):
        """Test failed state handling."""
        pipeline = orchestrator.create_pipeline("Test")
        config = PipelineConfig(
            start_date=datetime.utcnow(),
            end_date=datetime.utcnow()
        )
        
        # Mock a processor to fail
        with patch.object(orchestrator, '_process_wfp_collection') as mock_proc:
            mock_proc.side_effect = Exception("Test failure")
            
            with pytest.raises(Exception):
                await orchestrator.execute_pipeline(pipeline, config)
    
    @pytest.mark.asyncio
    async def test_state_machine_execution(self, orchestrator):
        """Test full state machine execution."""
        pipeline = orchestrator.create_pipeline("Test")
        config = PipelineConfig(
            start_date=datetime.utcnow(),
            end_date=datetime.utcnow()
        )
        
        # Mock all processor handlers to succeed
        for proc_id in pipeline.processors:
            handler_name = f'_process_{proc_id.replace("_", "_")}'
            if hasattr(orchestrator, handler_name):
                mock_handler = AsyncMock(return_value={"records_processed": 100})
                setattr(orchestrator, handler_name, mock_handler)
        
        # Execute pipeline
        execution = await orchestrator.execute_pipeline(pipeline, config)
        
        assert execution.state == PipelineState.COMPLETED
        assert execution.completed_at is not None
        assert len(execution.errors) == 0


class TestCheckpointResume:
    """Test checkpoint and resume functionality."""
    
    @pytest.mark.asyncio
    async def test_checkpoint_creation(self, orchestrator, tmp_path):
        """Test creating checkpoints."""
        pipeline = orchestrator.create_pipeline("Test")
        execution = pipeline.create_execution()
        
        # Mark some processors as completed
        execution.processor_graph["wfp_collection"].status = ProcessorStatus.COMPLETED
        execution.processor_graph["acled_collection"].status = ProcessorStatus.COMPLETED
        
        # Create checkpoint
        checkpoint = execution.create_checkpoint()
        
        assert checkpoint.pipeline_id == pipeline.pipeline_id
        assert checkpoint.state == execution.state
        assert "wfp_collection" in checkpoint.completed_processors
        assert "acled_collection" in checkpoint.completed_processors
        assert len(checkpoint.completed_processors) == 2
    
    @pytest.mark.asyncio
    async def test_checkpoint_save_load(self, orchestrator):
        """Test saving and loading checkpoints."""
        pipeline = orchestrator.create_pipeline("Test")
        execution = pipeline.create_execution()
        execution.state = PipelineState.PROCESSING
        
        # Save checkpoint
        await orchestrator._create_checkpoint(execution)
        
        # Verify checkpoint file exists
        checkpoint_files = list(orchestrator.checkpoint_dir.glob("checkpoint_*.json"))
        assert len(checkpoint_files) == 1
        
        # Load checkpoint data
        with open(checkpoint_files[0], 'r') as f:
            checkpoint_data = json.load(f)
        
        assert checkpoint_data["state"] == "processing"
        assert checkpoint_data["pipeline_id"] == str(pipeline.pipeline_id)
    
    @pytest.mark.asyncio
    async def test_resume_from_checkpoint(self, orchestrator):
        """Test resuming from checkpoint."""
        # Create and execute partial pipeline
        pipeline = orchestrator.create_pipeline("Test")
        execution = pipeline.create_execution()
        
        # Simulate partial completion
        execution.state = PipelineState.PROCESSING
        execution.processor_graph["wfp_collection"].status = ProcessorStatus.COMPLETED
        execution.processor_graph["acled_collection"].status = ProcessorStatus.COMPLETED
        
        # Create checkpoint
        checkpoint = execution.create_checkpoint()
        await orchestrator._create_checkpoint(execution)
        
        # Create new execution from checkpoint
        restored = await orchestrator._restore_execution(checkpoint.checkpoint_id)
        
        assert restored.state == PipelineState.PROCESSING
        assert restored.processor_graph["wfp_collection"].status == ProcessorStatus.COMPLETED
        assert restored.processor_graph["wfp_processing"].status == ProcessorStatus.PENDING
    
    @pytest.mark.asyncio
    async def test_checkpoint_validation(self, orchestrator):
        """Test checkpoint validation."""
        pipeline = orchestrator.create_pipeline("Test")
        execution = pipeline.create_execution()
        
        # Create checkpoint with data snapshots
        checkpoint = PipelineCheckpoint(
            pipeline_id=pipeline.pipeline_id,
            state=PipelineState.PROCESSING,
            data_snapshots={
                "wfp_data": "/nonexistent/path.parquet"
            }
        )
        
        # Should be invalid due to missing file
        assert not checkpoint.is_valid()


class TestDependencyGraph:
    """Test dependency graph execution."""
    
    @pytest.mark.asyncio
    async def test_dependency_resolution(self, orchestrator):
        """Test processor dependency resolution."""
        processors = ["a", "b", "c", "d"]
        dependencies = {
            "b": ["a"],
            "c": ["a"],
            "d": ["b", "c"]
        }
        
        pipeline = orchestrator.create_pipeline(
            "Test",
            processors=processors,
            dependencies=dependencies
        )
        execution = pipeline.create_execution()
        
        # Initially only 'a' can run
        runnable = execution.get_runnable_processors()
        assert len(runnable) == 1
        assert runnable[0].processor_id == "a"
        
        # After 'a' completes, 'b' and 'c' can run
        execution.processor_graph["a"].status = ProcessorStatus.COMPLETED
        runnable = execution.get_runnable_processors()
        assert len(runnable) == 2
        assert set(p.processor_id for p in runnable) == {"b", "c"}
        
        # After 'b' and 'c' complete, 'd' can run
        execution.processor_graph["b"].status = ProcessorStatus.COMPLETED
        execution.processor_graph["c"].status = ProcessorStatus.COMPLETED
        runnable = execution.get_runnable_processors()
        assert len(runnable) == 1
        assert runnable[0].processor_id == "d"
    
    @pytest.mark.asyncio
    async def test_parallel_execution(self, orchestrator):
        """Test parallel processor execution."""
        # Create pipeline with parallel processors
        processors = ["collect1", "collect2", "collect3", "process"]
        dependencies = {
            "process": ["collect1", "collect2", "collect3"]
        }
        
        pipeline = orchestrator.create_pipeline(
            "Test",
            processors=processors,
            dependencies=dependencies
        )
        pipeline.max_parallel_processors = 2  # Limit parallelism
        
        config = PipelineConfig(
            start_date=datetime.utcnow(),
            end_date=datetime.utcnow()
        )
        
        # Track execution order
        execution_order = []
        
        async def mock_processor(proc_id):
            execution_order.append(f"{proc_id}_start")
            await asyncio.sleep(0.1)  # Simulate work
            execution_order.append(f"{proc_id}_end")
            return {"records_processed": 100}
        
        # Mock processor handlers
        for proc_id in processors:
            handler = AsyncMock(side_effect=lambda c, p=proc_id: mock_processor(p))
            setattr(orchestrator, f"_process_{proc_id}", handler)
        
        # Execute (this would normally run the full pipeline)
        # For testing, we'll just run the processor group logic
        execution = pipeline.create_execution()
        collect_processors = [
            execution.processor_graph[p] for p in ["collect1", "collect2", "collect3"]
        ]
        
        await orchestrator._run_processor_group(
            execution, collect_processors, config, None
        )
        
        # Verify parallelism was limited
        # With max_parallel=2, collect3 should start after one of the first two finishes
        assert execution_order.index("collect3_start") > min(
            execution_order.index("collect1_end"),
            execution_order.index("collect2_end")
        )
    
    @pytest.mark.asyncio
    async def test_processor_retry(self, orchestrator):
        """Test processor retry logic."""
        pipeline = orchestrator.create_pipeline("Test")
        execution = pipeline.create_execution()
        config = PipelineConfig(
            start_date=datetime.utcnow(),
            end_date=datetime.utcnow()
        )
        
        # Create a processor that fails twice then succeeds
        fail_count = 0
        
        async def flaky_processor(config):
            nonlocal fail_count
            fail_count += 1
            if fail_count < 3:
                raise Exception(f"Failure {fail_count}")
            return {"records_processed": 100}
        
        orchestrator._process_wfp_collection = flaky_processor
        
        # Run the processor
        processor = execution.processor_graph["wfp_collection"]
        processor.max_retries = 3
        
        # First attempt - should fail and retry
        try:
            await orchestrator._run_processor(processor, execution, config)
        except:
            pass
        
        assert processor.retry_count == 0  # Not incremented yet
        assert processor.should_retry()
        
        # Simulate retry logic
        processor.retry_count = 1
        try:
            await orchestrator._run_processor(processor, execution, config)
        except:
            pass
        
        # Third attempt should succeed
        processor.retry_count = 2
        await orchestrator._run_processor(processor, execution, config)
        
        assert processor.status == ProcessorStatus.COMPLETED
        assert processor.metrics.records_processed == 100


class TestMonitoringIntegration:
    """Test monitoring integration."""
    
    @pytest.mark.asyncio
    async def test_resource_monitoring(self, orchestrator):
        """Test resource monitoring during execution."""
        assert orchestrator.resource_monitor is not None
        assert orchestrator.resource_monitor._monitoring
        
        # Get current metrics
        metrics = orchestrator.resource_monitor.get_current_metrics()
        # May be None if not collected yet
        
        # Force a collection
        new_metrics = orchestrator.resource_monitor._collect_metrics()
        assert new_metrics.cpu_percent >= 0
        assert new_metrics.memory_mb > 0
    
    @pytest.mark.asyncio
    async def test_health_checks(self, orchestrator):
        """Test health check integration."""
        # Run health checks
        results = await orchestrator.health_checker.check_all_components()
        
        # Should have default checks
        assert "data_directory" in results
        assert "disk_space" in results
        assert "memory" in results
        
        # Check overall status
        status = orchestrator.get_health_status()
        assert "overall_status" in status
        assert "components" in status
    
    @pytest.mark.asyncio
    async def test_metrics_export(self, orchestrator):
        """Test metrics export."""
        # Run some operations to generate metrics
        pipeline = orchestrator.create_pipeline("Test")
        
        # Export metrics
        metrics_text = orchestrator.export_metrics()
        
        assert "# Yemen Market Integration Pipeline Metrics" in metrics_text
        assert "pipeline_active_executions" in metrics_text
        assert "pipeline_records_processed_total" in metrics_text
    
    @pytest.mark.asyncio
    async def test_alert_system(self, orchestrator):
        """Test alert system integration."""
        # Check alerts are being monitored
        assert orchestrator.alert_manager is not None
        
        # Get alert summary
        summary = orchestrator.alert_manager.get_alert_summary()
        assert "total_alerts" in summary
        assert "alerts_last_hour" in summary
    
    @pytest.mark.asyncio
    async def test_progress_callback(self, orchestrator):
        """Test progress callback functionality."""
        pipeline = orchestrator.create_pipeline("Test")
        config = PipelineConfig(
            start_date=datetime.utcnow(),
            end_date=datetime.utcnow()
        )
        
        progress_updates = []
        
        async def progress_callback(status):
            progress_updates.append({
                "stage": status.stage.value,
                "progress": status.current_stage_progress
            })
        
        # Mock processor handlers
        for proc_id in pipeline.processors:
            handler = AsyncMock(return_value={"records_processed": 100})
            setattr(orchestrator, f"_process_{proc_id.replace('_', '_')}", handler)
        
        # Execute with callback
        await orchestrator.execute_pipeline(
            pipeline, config, progress_callback=progress_callback
        )
        
        # Should have received progress updates
        assert len(progress_updates) > 0
        
        # Final update should show completion
        final_update = progress_updates[-1]
        assert final_update["stage"] in ["completed", "panel_building"]