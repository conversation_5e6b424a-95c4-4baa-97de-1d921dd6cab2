"""
Integration tests for spatial and temporal services.

Tests the services work correctly with real geographic and time series data.
"""
import asyncio
from datetime import datetime, timedelta
import pytest
import pandas as pd
import geopandas as gpd
import numpy as np
from shapely.geometry import Point, Polygon, LineString
import pyproj

from src.infrastructure.spatial.spatial_integration_service import SpatialIntegrationService
from src.infrastructure.temporal.temporal_alignment_service import TemporalAlignmentService
from src.core.domain.market.entities import Market
from src.core.domain.geography.entities import Governorate, District


@pytest.fixture
def spatial_service():
    """Create spatial integration service."""
    return SpatialIntegrationService()


@pytest.fixture
def temporal_service():
    """Create temporal alignment service."""
    return TemporalAlignmentService()


@pytest.fixture
def yemen_markets_gdf():
    """Create sample Yemen markets as GeoDataFrame."""
    markets = [
        {'market_id': 'sanaa_main', 'name': 'Sana\'a Main', 
         'lat': 15.3694, 'lon': 44.1910, 'governorate': 'Sana\'a'},
        {'market_id': 'aden_port', 'name': 'Aden Port', 
         'lat': 12.7855, 'lon': 45.0187, 'governorate': 'Aden'},
        {'market_id': 'taiz_central', 'name': 'Taiz Central', 
         'lat': 13.5795, 'lon': 44.0212, 'governorate': 'Taiz'},
        {'market_id': 'hodeidah_fish', 'name': 'Hodeidah Fish', 
         'lat': 14.7978, 'lon': 42.9545, 'governorate': 'Al Hudaydah'},
        {'market_id': 'ibb_main', 'name': 'Ibb Main', 
         'lat': 13.9667, 'lon': 44.1833, 'governorate': 'Ibb'}
    ]
    
    geometry = [Point(m['lon'], m['lat']) for m in markets]
    return gpd.GeoDataFrame(markets, geometry=geometry, crs='EPSG:4326')


@pytest.fixture
def yemen_governorates_gdf():
    """Create simplified Yemen governorate boundaries."""
    # Simplified polygons for testing
    governorates = [
        {
            'governorate': 'Sana\'a',
            'geometry': Polygon([
                (43.5, 14.8), (44.8, 14.8), (44.8, 16.0), (43.5, 16.0), (43.5, 14.8)
            ])
        },
        {
            'governorate': 'Aden',
            'geometry': Polygon([
                (44.5, 12.5), (45.5, 12.5), (45.5, 13.0), (44.5, 13.0), (44.5, 12.5)
            ])
        },
        {
            'governorate': 'Taiz',
            'geometry': Polygon([
                (43.5, 13.2), (44.5, 13.2), (44.5, 14.0), (43.5, 14.0), (43.5, 13.2)
            ])
        }
    ]
    
    return gpd.GeoDataFrame(governorates, crs='EPSG:4326')


class TestSpatialIntegrationService:
    """Test spatial integration service functionality."""
    
    @pytest.mark.asyncio
    async def test_buffer_creation(self, spatial_service, yemen_markets_gdf):
        """Test creating buffers around market points."""
        # Test different buffer distances
        buffer_distances = [5, 10, 25, 50]  # km
        
        for distance in buffer_distances:
            # Create buffer for first market
            market_point = yemen_markets_gdf.iloc[0].geometry
            buffer = await spatial_service.create_buffer(market_point, distance)
            
            # Verify buffer is a polygon
            assert buffer.geom_type == 'Polygon'
            
            # Verify buffer contains original point
            assert buffer.contains(market_point)
            
            # Approximate area check (circle area = πr²)
            # Convert km to degrees (approximate)
            expected_area = np.pi * (distance / 111) ** 2  # 1 degree ≈ 111 km
            actual_area = buffer.area
            
            # Allow 20% tolerance due to projection differences
            assert abs(actual_area - expected_area) / expected_area < 0.2
    
    @pytest.mark.asyncio
    async def test_spatial_join(self, spatial_service, yemen_markets_gdf, yemen_governorates_gdf):
        """Test spatial join between markets and governorates."""
        # Perform spatial join
        joined = await spatial_service.spatial_join(
            yemen_markets_gdf,
            yemen_governorates_gdf,
            how='left',
            predicate='within'
        )
        
        # Verify join results
        assert len(joined) == len(yemen_markets_gdf)
        
        # Check Sana'a market is in Sana'a governorate
        sanaa_market = joined[joined['market_id'] == 'sanaa_main'].iloc[0]
        assert sanaa_market['governorate_right'] == 'Sana\'a'
        
        # Check markets outside polygons have NaN
        # (Hodeidah and Ibb are outside our simplified polygons)
        outside_markets = joined[joined['market_id'].isin(['hodeidah_fish', 'ibb_main'])]
        assert outside_markets['governorate_right'].isna().all()
    
    @pytest.mark.asyncio
    async def test_nearest_neighbor(self, spatial_service, yemen_markets_gdf):
        """Test finding nearest markets."""
        # Find nearest market to Sana'a
        sanaa_idx = 0
        sanaa_point = yemen_markets_gdf.iloc[sanaa_idx].geometry
        
        # Get 3 nearest neighbors (excluding self)
        nearest = await spatial_service.find_nearest_neighbors(
            sanaa_point,
            yemen_markets_gdf[yemen_markets_gdf.index != sanaa_idx],
            k=3
        )
        
        # Verify we got 3 results
        assert len(nearest) == 3
        
        # Verify Ibb is likely the nearest (geographically close)
        nearest_names = nearest['name'].tolist()
        assert 'Ibb Main' in nearest_names
        
        # Verify distances are calculated
        assert 'distance' in nearest.columns
        assert (nearest['distance'] > 0).all()
        
        # Verify distances are sorted
        distances = nearest['distance'].tolist()
        assert distances == sorted(distances)
    
    @pytest.mark.asyncio
    async def test_distance_calculation(self, spatial_service, yemen_markets_gdf):
        """Test distance calculations between markets."""
        # Calculate distance from Sana'a to all other markets
        sanaa = yemen_markets_gdf.iloc[0]
        
        for idx, market in yemen_markets_gdf.iterrows():
            if idx == 0:
                continue
            
            distance = await spatial_service.calculate_distance(
                sanaa.geometry,
                market.geometry,
                unit='km'
            )
            
            # Verify distance is positive
            assert distance > 0
            
            # Verify approximate distances (rough estimates)
            if market['market_id'] == 'aden_port':
                # Sana'a to Aden is roughly 300-400 km
                assert 200 < distance < 500
            elif market['market_id'] == 'hodeidah_fish':
                # Sana'a to Hodeidah is roughly 200-300 km
                assert 100 < distance < 400
    
    @pytest.mark.asyncio
    async def test_crs_transformation(self, spatial_service, yemen_markets_gdf):
        """Test coordinate reference system transformations."""
        # Transform to UTM zone 38N (appropriate for Yemen)
        utm_gdf = await spatial_service.transform_crs(
            yemen_markets_gdf,
            'EPSG:32638'
        )
        
        # Verify CRS changed
        assert utm_gdf.crs.to_string() == 'EPSG:32638'
        
        # Verify points are transformed (UTM coords are much larger)
        original_x = yemen_markets_gdf.iloc[0].geometry.x
        transformed_x = utm_gdf.iloc[0].geometry.x
        
        assert transformed_x > 100000  # UTM coordinates
        assert original_x < 180  # Geographic coordinates
        
        # Transform back and verify
        back_transformed = await spatial_service.transform_crs(
            utm_gdf,
            'EPSG:4326'
        )
        
        # Check coordinates match (within tolerance)
        for idx in range(len(yemen_markets_gdf)):
            orig = yemen_markets_gdf.iloc[idx].geometry
            back = back_transformed.iloc[idx].geometry
            
            assert abs(orig.x - back.x) < 0.0001
            assert abs(orig.y - back.y) < 0.0001
    
    @pytest.mark.asyncio
    async def test_spatial_index(self, spatial_service, yemen_markets_gdf):
        """Test spatial index creation for performance."""
        # Create spatial index
        indexed_gdf = await spatial_service.create_spatial_index(yemen_markets_gdf)
        
        # Verify index exists
        assert hasattr(indexed_gdf, '_rtree_index') or hasattr(indexed_gdf, 'sindex')
        
        # Test spatial query performance
        # Create a bounding box around Sana'a
        bounds = (43.5, 14.5, 44.5, 15.5)
        
        # Query using spatial index
        results = await spatial_service.query_by_bounds(indexed_gdf, bounds)
        
        # Verify Sana'a market is in results
        assert len(results) > 0
        assert 'sanaa_main' in results['market_id'].values


class TestTemporalAlignmentService:
    """Test temporal alignment service functionality."""
    
    @pytest.fixture
    def price_time_series(self):
        """Create sample price time series data."""
        dates = pd.date_range('2024-01-01', '2024-03-31', freq='D')
        markets = ['sanaa_main', 'aden_port', 'taiz_central']
        
        data = []
        for market in markets:
            for date in dates:
                # Add some randomness and trends
                base_price = 1000 if market == 'sanaa_main' else 1500
                trend = (date - dates[0]).days * 2
                noise = np.random.normal(0, 50)
                
                # Add some missing data
                if np.random.random() > 0.9:
                    continue
                
                data.append({
                    'date': date,
                    'market_id': market,
                    'commodity': 'wheat',
                    'price': base_price + trend + noise
                })
        
        return pd.DataFrame(data)
    
    @pytest.fixture
    def conflict_events(self):
        """Create sample conflict event data."""
        events = []
        
        # Random events throughout the period
        for i in range(20):
            date = datetime(2024, 1, 1) + timedelta(days=np.random.randint(0, 90))
            events.append({
                'event_date': date,
                'event_type': np.random.choice(['Battles', 'Protests', 'Explosions']),
                'fatalities': np.random.randint(0, 10),
                'location': np.random.choice(['Sana\'a', 'Aden', 'Taiz'])
            })
        
        return pd.DataFrame(events)
    
    @pytest.mark.asyncio
    async def test_align_to_monthly(self, temporal_service, price_time_series):
        """Test aligning daily data to monthly frequency."""
        # Align to monthly
        monthly_data = await temporal_service.align_to_frequency(
            price_time_series,
            target_frequency='MS',  # Month start
            date_column='date',
            aggregation_rules={'price': 'mean'}
        )
        
        # Verify monthly frequency
        assert len(monthly_data) <= 3 * 3  # 3 months * 3 markets
        
        # Verify dates are month starts
        dates = monthly_data['date'].unique()
        assert all(date.day == 1 for date in dates)
        
        # Verify aggregation
        jan_sanaa = monthly_data[
            (monthly_data['date'] == '2024-01-01') & 
            (monthly_data['market_id'] == 'sanaa_main')
        ]
        
        if not jan_sanaa.empty:
            # Price should be average of January daily prices
            jan_daily = price_time_series[
                (price_time_series['date'].dt.month == 1) &
                (price_time_series['market_id'] == 'sanaa_main')
            ]
            
            if not jan_daily.empty:
                expected_avg = jan_daily['price'].mean()
                actual_avg = jan_sanaa.iloc[0]['price']
                assert abs(expected_avg - actual_avg) < 0.01
    
    @pytest.mark.asyncio
    async def test_fill_missing_dates(self, temporal_service, price_time_series):
        """Test filling missing dates in time series."""
        # Remove some dates to create gaps
        price_with_gaps = price_time_series[price_time_series['date'].dt.day % 3 != 0].copy()
        
        # Fill missing dates
        filled_data = await temporal_service.fill_missing_dates(
            price_with_gaps,
            date_column='date',
            group_columns=['market_id', 'commodity'],
            frequency='D',
            method='forward_fill',
            limit=2
        )
        
        # Verify all dates are present
        expected_dates = pd.date_range('2024-01-01', '2024-03-31', freq='D')
        
        for market in filled_data['market_id'].unique():
            market_data = filled_data[filled_data['market_id'] == market]
            market_dates = pd.to_datetime(market_data['date'].unique())
            
            # Should have most dates (some might still be missing due to limit=2)
            coverage = len(market_dates) / len(expected_dates)
            assert coverage > 0.9
    
    @pytest.mark.asyncio
    async def test_lag_variables(self, temporal_service, price_time_series):
        """Test creating lagged variables."""
        # Sort by date first
        sorted_data = price_time_series.sort_values(['market_id', 'date'])
        
        # Create lagged prices
        lagged_data = await temporal_service.create_lagged_variables(
            sorted_data,
            variables=['price'],
            lags=[1, 7, 30],
            group_columns=['market_id', 'commodity']
        )
        
        # Verify lag columns exist
        assert 'price_lag_1' in lagged_data.columns
        assert 'price_lag_7' in lagged_data.columns
        assert 'price_lag_30' in lagged_data.columns
        
        # Verify lag values
        for market in lagged_data['market_id'].unique():
            market_data = lagged_data[lagged_data['market_id'] == market].sort_values('date')
            
            # Check 1-day lag
            for i in range(1, len(market_data)):
                if pd.notna(market_data.iloc[i]['price_lag_1']):
                    # Find previous day's price
                    prev_date = market_data.iloc[i]['date'] - timedelta(days=1)
                    prev_data = market_data[market_data['date'] == prev_date]
                    
                    if not prev_data.empty:
                        assert market_data.iloc[i]['price_lag_1'] == prev_data.iloc[0]['price']
    
    @pytest.mark.asyncio
    async def test_merge_temporal_data(self, temporal_service, price_time_series, conflict_events):
        """Test merging data with different temporal granularities."""
        # Aggregate conflict events to monthly
        monthly_conflict = await temporal_service.align_to_frequency(
            conflict_events,
            target_frequency='MS',
            date_column='event_date',
            aggregation_rules={
                'fatalities': 'sum',
                'event_type': 'count'
            }
        )
        
        # Rename count column
        monthly_conflict.rename(columns={'event_type': 'event_count'}, inplace=True)
        
        # Aggregate prices to monthly
        monthly_prices = await temporal_service.align_to_frequency(
            price_time_series,
            target_frequency='MS',
            date_column='date',
            aggregation_rules={'price': 'mean'}
        )
        
        # Map location to market_id for merging
        location_to_market = {
            'Sana\'a': 'sanaa_main',
            'Aden': 'aden_port',
            'Taiz': 'taiz_central'
        }
        monthly_conflict['market_id'] = monthly_conflict['location'].map(location_to_market)
        
        # Merge temporal data
        merged = await temporal_service.merge_temporal_data(
            [monthly_prices, monthly_conflict],
            on=['market_id', 'date'],
            how='left',
            fill_value=0
        )
        
        # Verify merge
        assert len(merged) >= len(monthly_prices)
        assert 'price' in merged.columns
        assert 'fatalities' in merged.columns
        assert 'event_count' in merged.columns
        
        # Check that conflict data was merged correctly
        sanaa_jan = merged[
            (merged['market_id'] == 'sanaa_main') & 
            (merged['date'] == '2024-01-01')
        ]
        
        if not sanaa_jan.empty:
            # Fatalities should be 0 if no events, or sum of January events
            assert sanaa_jan.iloc[0]['fatalities'] >= 0
    
    @pytest.mark.asyncio
    async def test_seasonal_decomposition(self, temporal_service):
        """Test seasonal decomposition of time series."""
        # Create data with clear seasonal pattern
        dates = pd.date_range('2023-01-01', '2024-12-31', freq='D')
        
        # Create seasonal pattern (higher prices in summer)
        data = []
        for date in dates:
            base_price = 1000
            trend = (date - dates[0]).days * 0.5
            seasonal = 200 * np.sin(2 * np.pi * date.dayofyear / 365)
            noise = np.random.normal(0, 20)
            
            data.append({
                'date': date,
                'price': base_price + trend + seasonal + noise
            })
        
        df = pd.DataFrame(data)
        
        # Perform seasonal decomposition
        decomposed = await temporal_service.seasonal_decomposition(
            df,
            value_column='price',
            date_column='date',
            period=365,  # Annual seasonality
            model='additive'
        )
        
        # Verify components
        assert 'trend' in decomposed.columns
        assert 'seasonal' in decomposed.columns
        assert 'residual' in decomposed.columns
        
        # Verify seasonal component has annual pattern
        # Check that summer months have higher seasonal component
        summer_seasonal = decomposed[decomposed['date'].dt.month.isin([6, 7, 8])]['seasonal'].mean()
        winter_seasonal = decomposed[decomposed['date'].dt.month.isin([12, 1, 2])]['seasonal'].mean()
        
        assert summer_seasonal > winter_seasonal  # Summer prices higher


@pytest.mark.asyncio
async def test_spatial_temporal_integration(
    spatial_service, temporal_service, yemen_markets_gdf, price_time_series
):
    """Test integration of spatial and temporal services."""
    # Create buffers around markets
    market_buffers = {}
    for idx, market in yemen_markets_gdf.iterrows():
        buffer_10km = await spatial_service.create_buffer(market.geometry, 10)
        market_buffers[market['market_id']] = buffer_10km
    
    # Generate random conflict events
    n_events = 50
    conflict_events = []
    
    for i in range(n_events):
        # Random location in Yemen
        lat = np.random.uniform(12.5, 16.0)
        lon = np.random.uniform(42.5, 45.5)
        date = datetime(2024, 1, 1) + timedelta(days=np.random.randint(0, 90))
        
        event = {
            'event_id': f'EVT{i:03d}',
            'date': date,
            'latitude': lat,
            'longitude': lon,
            'geometry': Point(lon, lat),
            'fatalities': np.random.randint(0, 10)
        }
        conflict_events.append(event)
    
    conflict_gdf = gpd.GeoDataFrame(conflict_events, geometry='geometry', crs='EPSG:4326')
    
    # Find events within market buffers
    market_events = {}
    for market_id, buffer in market_buffers.items():
        # Find events within this market's buffer
        events_in_buffer = conflict_gdf[conflict_gdf.geometry.within(buffer)]
        market_events[market_id] = events_in_buffer
    
    # Aggregate conflict events by market and month
    monthly_conflict_by_market = []
    
    for market_id, events in market_events.items():
        if not events.empty:
            # Group by month
            events['month'] = pd.to_datetime(events['date']).dt.to_period('M')
            monthly = events.groupby('month').agg({
                'fatalities': 'sum',
                'event_id': 'count'
            }).reset_index()
            monthly.rename(columns={'event_id': 'event_count'}, inplace=True)
            monthly['market_id'] = market_id
            monthly['date'] = monthly['month'].dt.to_timestamp()
            monthly_conflict_by_market.append(monthly)
    
    if monthly_conflict_by_market:
        conflict_df = pd.concat(monthly_conflict_by_market, ignore_index=True)
        
        # Merge with price data
        monthly_prices = await temporal_service.align_to_frequency(
            price_time_series,
            target_frequency='MS',
            date_column='date',
            aggregation_rules={'price': 'mean'}
        )
        
        # Merge spatial conflict data with temporal price data
        integrated_data = await temporal_service.merge_temporal_data(
            [monthly_prices, conflict_df],
            on=['market_id', 'date'],
            how='left',
            fill_value=0
        )
        
        # Verify integration
        assert not integrated_data.empty
        assert 'price' in integrated_data.columns
        assert 'fatalities' in integrated_data.columns
        assert 'event_count' in integrated_data.columns
        
        # Markets with more conflict should be identifiable
        high_conflict_markets = integrated_data.groupby('market_id')['event_count'].sum()
        assert len(high_conflict_markets) > 0