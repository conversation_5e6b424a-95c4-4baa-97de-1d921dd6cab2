"""
Integration test for enhanced panel builder with all data sources.

Tests the complete integration of 10+ data sources into a unified panel.
"""

import asyncio
from datetime import datetime, timedelta
import pytest
import pandas as pd
import numpy as np
from pathlib import Path

from src.infrastructure.processors.panel_builder import PanelBuilder
from src.infrastructure.processors.derived_variables_calculator import DerivedVariablesCalculator
from src.core.domain.market.entities import Market, PriceObservation
from src.core.domain.market.value_objects import (
    MarketId, MarketType, Commodity, Price, Currency, Unit
)
from src.core.domain.geography.value_objects import Coordinates


@pytest.fixture
def panel_builder():
    """Create panel builder instance."""
    return PanelBuilder(
        frequency='M',
        min_observations_per_series=6,
        balance_requirement=0.7
    )


@pytest.fixture
def derived_calculator():
    """Create derived variables calculator."""
    return DerivedVariablesCalculator()


@pytest.fixture
def sample_markets():
    """Create sample markets for testing."""
    markets = [
        Market(
            market_id=MarketId("sanaa_main"),
            name="Sana'a Main Market",
            governorate="Sana'a",
            district="Old City",
            market_type=MarketType.RETAIL,
            coordinates=Coordinates(15.3694, 44.1910),
            active_since=datetime(2019, 1, 1)
        ),
        Market(
            market_id=MarketId("aden_port"),
            name="Aden Port Market",
            governorate="Aden",
            district="Crater",
            market_type=MarketType.WHOLESALE,
            coordinates=Coordinates(12.7855, 45.0187),
            active_since=datetime(2019, 1, 1)
        ),
        Market(
            market_id=MarketId("taiz_central"),
            name="Taiz Central Market",
            governorate="Taiz",
            district="Al Qahirah",
            market_type=MarketType.RETAIL,
            coordinates=Coordinates(13.5795, 44.0212),
            active_since=datetime(2019, 1, 1)
        )
    ]
    return markets


@pytest.fixture
def sample_price_observations(sample_markets):
    """Create sample price observations."""
    observations = []
    
    # Generate monthly prices for 2024
    dates = pd.date_range('2024-01-01', '2024-06-30', freq='D')
    commodities = [
        Commodity("wheat", "cereals", "Wheat"),
        Commodity("rice", "cereals", "Rice"),
        Commodity("sugar", "other", "Sugar")
    ]
    
    for market in sample_markets:
        for date in dates:
            for commodity in commodities:
                # Base price varies by market and commodity
                base_price = {
                    'sanaa_main': {'wheat': 1000, 'rice': 1500, 'sugar': 800},
                    'aden_port': {'wheat': 2800, 'rice': 4200, 'sugar': 2200},
                    'taiz_central': {'wheat': 1800, 'rice': 2700, 'sugar': 1500}
                }
                
                price_val = base_price[market.market_id.value][commodity.code]
                
                # Add some variation
                price_val += np.random.normal(0, price_val * 0.05)
                
                # Add trend
                days_since_start = (date - dates[0]).days
                price_val += days_since_start * 2
                
                obs = PriceObservation(
                    market_id=market.market_id,
                    commodity=commodity,
                    observed_date=date,
                    price=Price(
                        amount=price_val,
                        currency=Currency.YER,
                        unit=Unit.PER_KG
                    ),
                    source="test",
                    quality="standard"
                )
                observations.append(obs)
    
    return observations


@pytest.fixture
def sample_exchange_rates():
    """Create sample exchange rate data."""
    dates = pd.date_range('2024-01-01', '2024-06-30', freq='D')
    
    data = []
    for date in dates:
        # Northern zones (Houthi)
        data.append({
            'governorate': "Sana'a",
            'date': date,
            'exchange_rate': 530 + np.random.normal(0, 10),
            'currency_zone': 'HOUTHI'
        })
        
        # Southern zones (Government)
        data.append({
            'governorate': "Aden",
            'date': date,
            'exchange_rate': 1950 + np.random.normal(0, 50),
            'currency_zone': 'GOVERNMENT'
        })
        
        # Contested zones
        data.append({
            'governorate': "Taiz",
            'date': date,
            'exchange_rate': 1200 + np.random.normal(0, 30),
            'currency_zone': 'CONTESTED'
        })
    
    return pd.DataFrame(data)


@pytest.fixture
def sample_aid_data():
    """Create sample aid distribution data."""
    dates = pd.date_range('2024-01-01', '2024-06-30', freq='MS')
    
    data = []
    for date in dates:
        for gov in ["Sana'a", "Aden", "Taiz"]:
            data.append({
                'date': date,
                'governorate': gov,
                'organization': 'WFP',
                'cluster': 'Food Security',
                'beneficiaries': np.random.randint(5000, 20000),
                'modality': np.random.choice(['in-kind', 'cash', 'voucher'])
            })
            
            if np.random.random() > 0.5:
                data.append({
                    'date': date,
                    'governorate': gov,
                    'organization': 'UNICEF',
                    'cluster': 'WASH',
                    'beneficiaries': np.random.randint(3000, 10000),
                    'modality': 'in-kind'
                })
    
    return pd.DataFrame(data)


@pytest.fixture
def sample_climate_data(sample_markets):
    """Create sample climate data."""
    dates = pd.date_range('2024-01-01', '2024-06-30', freq='D')
    
    data = []
    for date in dates:
        for market in sample_markets:
            # Seasonal pattern for rainfall
            day_of_year = date.dayofyear
            rainfall = max(0, 50 * np.sin(2 * np.pi * day_of_year / 365) + 
                          np.random.normal(0, 10))
            
            # Temperature pattern
            temp_base = 25 + 10 * np.sin(2 * np.pi * day_of_year / 365)
            
            data.append({
                'date': date,
                'market_id': market.market_id.value,
                'precipitation': rainfall,
                'temperature': temp_base + np.random.normal(0, 2),
                'ndvi': 0.3 + 0.2 * np.sin(2 * np.pi * (day_of_year - 60) / 365)
            })
    
    return pd.DataFrame(data)


@pytest.fixture
def sample_population_data(sample_markets):
    """Create sample population data."""
    data = []
    
    population_by_market = {
        'sanaa_main': {'5km': 250000, '10km': 800000, '25km': 2000000},
        'aden_port': {'5km': 150000, '10km': 400000, '25km': 800000},
        'taiz_central': {'5km': 180000, '10km': 500000, '25km': 1200000}
    }
    
    for market in sample_markets:
        market_id = market.market_id.value
        
        for radius in [5, 10, 25]:
            pop = population_by_market[market_id][f'{radius}km']
            area = np.pi * radius ** 2
            
            data.append({
                'market_id': market_id,
                f'population_{radius}km': pop,
                f'density_{radius}km': pop / area
            })
    
    # Flatten to single row per market
    df = pd.DataFrame(data)
    return df.pivot_table(
        index='market_id',
        values=[col for col in df.columns if col != 'market_id'],
        aggfunc='first'
    ).reset_index()


@pytest.fixture
def sample_infrastructure_data(sample_markets):
    """Create sample infrastructure data."""
    data = []
    
    infra_by_market = {
        'sanaa_main': {
            'distance_to_port': 226,  # km to Hodeidah
            'distance_to_border': 120,  # km to Saudi border
            'distance_to_capital': 0,
            'road_density': 0.8,
            'has_airport': 1,
            'electricity_access': 0.6
        },
        'aden_port': {
            'distance_to_port': 0,
            'distance_to_border': 180,
            'distance_to_capital': 346,
            'road_density': 0.9,
            'has_airport': 1,
            'electricity_access': 0.7
        },
        'taiz_central': {
            'distance_to_port': 150,
            'distance_to_border': 200,
            'distance_to_capital': 256,
            'road_density': 0.6,
            'has_airport': 0,
            'electricity_access': 0.4
        }
    }
    
    for market in sample_markets:
        row = {'market_id': market.market_id.value}
        row.update(infra_by_market[market.market_id.value])
        data.append(row)
    
    return pd.DataFrame(data)


class TestPanelBuilderIntegration:
    """Test enhanced panel builder with all data sources."""
    
    @pytest.mark.asyncio
    async def test_full_panel_integration(
        self,
        panel_builder,
        sample_markets,
        sample_price_observations,
        sample_exchange_rates,
        sample_aid_data,
        sample_climate_data,
        sample_population_data,
        sample_infrastructure_data
    ):
        """Test integrating all data sources into panel."""
        
        # Build integrated panel
        panel = await panel_builder.process(
            markets=sample_markets,
            price_observations=sample_price_observations,
            exchange_rates=sample_exchange_rates,
            aid_distributions=sample_aid_data,
            climate_observations=sample_climate_data,
            population_data=sample_population_data,
            infrastructure_metrics=sample_infrastructure_data,
            start_date=datetime(2024, 1, 1),
            end_date=datetime(2024, 6, 30)
        )
        
        # Basic checks
        assert not panel.empty
        assert len(panel) > 0
        
        # Check all markets present
        assert set(panel['market_id'].unique()) == {m.market_id.value for m in sample_markets}
        
        # Check price data integrated
        assert 'price' in panel.columns
        assert 'usdprice' in panel.columns
        assert 'log_price' in panel.columns
        
        # Check exchange rates integrated
        assert 'exchange_rate_used' in panel.columns
        assert 'currency_zone' in panel.columns
        assert panel['exchange_rate_used'].notna().all()
        
        # Check aid data integrated
        assert 'beneficiaries' in panel.columns
        assert 'n_aid_organizations' in panel.columns
        assert 'aid_food_security' in panel.columns
        
        # Check climate data integrated
        assert 'rainfall_avg' in panel.columns
        assert 'temperature_avg' in panel.columns
        assert 'vegetation_index' in panel.columns
        
        # Check population data integrated
        assert 'population_5km' in panel.columns
        assert 'density_25km' in panel.columns
        assert 'is_urban' in panel.columns
        
        # Check infrastructure data integrated
        assert 'distance_to_port' in panel.columns
        assert 'road_density' in panel.columns
        assert 'infrastructure_accessibility' in panel.columns
        
        # Check derived features
        assert 'price_change' in panel.columns
        assert 'log_conflict' in panel.columns
        assert 'year' in panel.columns
        assert 'month' in panel.columns
    
    @pytest.mark.asyncio
    async def test_balanced_panel_creation(
        self,
        panel_builder,
        sample_markets,
        sample_price_observations
    ):
        """Test creating balanced panel."""
        
        # Create balanced panel
        balanced = await panel_builder.create_balanced_panel(
            markets=sample_markets,
            price_observations=sample_price_observations,
            commodities=['wheat', 'rice'],
            start_date=datetime(2024, 1, 1),
            end_date=datetime(2024, 6, 30)
        )
        
        # Check balance
        assert not balanced.empty
        
        # Check all market-commodity combinations
        expected_series = len(sample_markets) * 2  # 2 commodities
        actual_series = balanced.groupby(['market_id', 'commodity']).size().count()
        
        assert actual_series <= expected_series
        
        # Check time completeness for remaining series
        for (market, commodity), group in balanced.groupby(['market_id', 'commodity']):
            n_periods = group['time_period'].nunique()
            assert n_periods >= 6  # At least 6 months
    
    @pytest.mark.asyncio
    async def test_derived_variables(
        self,
        panel_builder,
        derived_calculator,
        sample_markets,
        sample_price_observations,
        sample_exchange_rates,
        sample_population_data,
        sample_infrastructure_data
    ):
        """Test derived variable calculation."""
        
        # Build basic panel
        panel = await panel_builder.process(
            markets=sample_markets,
            price_observations=sample_price_observations,
            exchange_rates=sample_exchange_rates,
            population_data=sample_population_data,
            infrastructure_metrics=sample_infrastructure_data
        )
        
        # Calculate derived variables
        panel_with_derived = await derived_calculator.calculate_all_variables(panel)
        
        # Check derived variables exist
        derived_cols = [col for col in panel_with_derived.columns if col.startswith('derived_')]
        assert len(derived_cols) > 0
        
        # Check specific derived variables
        expected_derived = [
            'derived_market_access_vulnerability',
            'derived_food_affordability',
            'derived_currency_divergence'
        ]
        
        for var in expected_derived:
            if var in panel_with_derived.columns:
                assert panel_with_derived[var].notna().any()
        
        # Generate summary report
        report = derived_calculator.create_summary_report(panel_with_derived)
        
        assert 'n_derived_variables' in report
        assert report['n_derived_variables'] > 0
        assert 'categories' in report
        assert 'summary_statistics' in report
    
    @pytest.mark.asyncio
    async def test_currency_zone_integration(
        self,
        panel_builder,
        sample_markets,
        sample_price_observations,
        sample_exchange_rates
    ):
        """Test currency zone specific calculations."""
        
        panel = await panel_builder.process(
            markets=sample_markets,
            price_observations=sample_price_observations,
            exchange_rates=sample_exchange_rates
        )
        
        # Check zone-specific exchange rates
        for zone in ['HOUTHI', 'GOVERNMENT', 'CONTESTED']:
            zone_data = panel[panel['currency_zone'] == zone]
            if not zone_data.empty:
                avg_rate = zone_data['exchange_rate_used'].mean()
                
                if zone == 'HOUTHI':
                    assert 500 < avg_rate < 600
                elif zone == 'GOVERNMENT':
                    assert 1800 < avg_rate < 2100
                elif zone == 'CONTESTED':
                    assert 1000 < avg_rate < 1400
        
        # Check USD price corrections
        assert 'usdprice' in panel.columns
        assert 'usd_price_corrected' in panel.columns
        assert panel['usd_price_corrected'].all()
        
        # Verify exchange rate deviations calculated
        if 'exchange_rate_deviation' in panel.columns:
            assert panel['exchange_rate_deviation'].notna().any()
    
    @pytest.mark.asyncio
    async def test_temporal_alignment(
        self,
        panel_builder,
        sample_markets,
        sample_price_observations,
        sample_climate_data
    ):
        """Test temporal alignment of different frequency data."""
        
        # Climate data is daily, should be aggregated to monthly
        panel = await panel_builder.process(
            markets=sample_markets,
            price_observations=sample_price_observations,
            climate_observations=sample_climate_data
        )
        
        # Check monthly aggregation
        assert panel['time_period'].dtype.name == 'period[M]'
        
        # Check climate variables are aggregated properly
        assert 'rainfall_total' in panel.columns  # Sum
        assert 'temperature_avg' in panel.columns  # Mean
        
        # Check lagged variables
        lag_cols = [col for col in panel.columns if '_lag' in col]
        assert len(lag_cols) > 0
        
        # Verify lag values
        for lag_col in lag_cols:
            # Some non-null lagged values should exist
            assert panel[lag_col].notna().any()
    
    @pytest.mark.asyncio
    async def test_missing_data_handling(
        self,
        panel_builder,
        sample_markets,
        sample_price_observations
    ):
        """Test handling of missing data sources."""
        
        # Build panel with only price data
        panel = await panel_builder.process(
            markets=sample_markets,
            price_observations=sample_price_observations,
            # All other sources are None
            exchange_rates=None,
            aid_distributions=None,
            climate_observations=None
        )
        
        # Should still produce valid panel
        assert not panel.empty
        assert 'price' in panel.columns
        assert 'market_id' in panel.columns
        
        # Missing data columns should not cause errors
        # Exchange rate columns might be missing or filled with defaults
        if 'exchange_rate_used' not in panel.columns:
            # That's OK - no exchange rate data provided
            pass
    
    @pytest.mark.asyncio
    async def test_panel_quality_metrics(
        self,
        panel_builder,
        sample_markets,
        sample_price_observations
    ):
        """Test panel quality and completeness metrics."""
        
        panel = await panel_builder.process(
            markets=sample_markets,
            price_observations=sample_price_observations
        )
        
        # Calculate completeness metrics
        total_expected = len(sample_markets) * panel['commodity'].nunique() * panel['time_period'].nunique()
        total_actual = len(panel)
        completeness = total_actual / total_expected if total_expected > 0 else 0
        
        # Should have reasonable completeness
        assert completeness > 0.5
        
        # Check for duplicate entries
        duplicates = panel.duplicated(['market_id', 'commodity', 'time_period']).sum()
        assert duplicates == 0
        
        # Check data types
        assert panel['price'].dtype in [np.float64, np.float32]
        if 'usdprice' in panel.columns:
            assert panel['usdprice'].dtype in [np.float64, np.float32]