"""Integration tests for IPC processor."""

import pytest
import asyncio
from datetime import datetime, timedelta
from pathlib import Path
import pandas as pd
import numpy as np

from src.infrastructure.processors.ipc_processor import (
    IPCProcessor, IPCData
)
from src.infrastructure.processors.base_processor import SourceConfig
from src.infrastructure.data_quality.validation_framework import ValidationLevel
from src.core.domain.food_security.entities import IPCClassification
from src.core.domain.food_security.value_objects import IPCPhase, DriverCategory


@pytest.fixture
def processor():
    """Create IPC processor instance."""
    config = SourceConfig(
        source_id="ipc_test",
        name="IPC Test",
        description="Test processor",
        base_url="https://test.example.com",
        validation_level=ValidationLevel.STANDARD
    )
    
    return IPCProcessor(
        source_config=config,
        cache_manager=None,
        validator=None,
        hdx_client=None,
        admin_boundaries=None
    )


@pytest.fixture
def sample_ipc_data():
    """Create sample IPC data."""
    data = []
    
    # Create data for multiple districts
    locations = [
        ("Sa'ada", "Sa'ada City", 4, 200000),
        ("Sa'ada", "Majz", 4, 150000),
        ("Aden", "Crater", 2, 180000),
        ("Aden", "Al Mualla", 3, 220000),
        ("Taiz", "Taiz City", 3, 350000)
    ]
    
    analysis_date = datetime(2024, 3, 1)
    projection_start = datetime(2024, 4, 1)
    projection_end = datetime(2024, 7, 31)
    
    for gov, district, phase, total_pop in locations:
        # Create population distribution based on phase
        if phase >= 4:
            pop_dist = {1: 0.05, 2: 0.15, 3: 0.30, 4: 0.35, 5: 0.15}
        elif phase == 3:
            pop_dist = {1: 0.10, 2: 0.25, 3: 0.40, 4: 0.20, 5: 0.05}
        else:
            pop_dist = {1: 0.30, 2: 0.40, 3: 0.20, 4: 0.08, 5: 0.02}
        
        pop_by_phase = {
            p: int(total_pop * pct) for p, pct in pop_dist.items()
        }
        
        # Adjust to match total
        diff = total_pop - sum(pop_by_phase.values())
        pop_by_phase[2] += diff
        
        ipc = IPCData(
            governorate=gov,
            district=district,
            analysis_date=analysis_date,
            projection_period_start=projection_start,
            projection_period_end=projection_end,
            phase=phase,
            population_phase=pop_by_phase,
            total_population=total_pop,
            drivers=['Conflict', 'High prices', 'Currency depreciation'],
            confidence_level='Medium',
            source='Test'
        )
        
        data.append(ipc)
    
    return data


@pytest.mark.asyncio
async def test_download_creates_synthetic_data(processor):
    """Test that download creates synthetic data when no HDX client."""
    result = await processor.download()
    
    assert isinstance(result, list)
    assert len(result) > 0
    
    # Check data structure
    first_ipc = result[0]
    assert isinstance(first_ipc, IPCData)
    assert first_ipc.phase in range(1, 6)
    assert isinstance(first_ipc.population_phase, dict)
    assert first_ipc.total_population > 0


@pytest.mark.asyncio
async def test_validate_specific(processor, sample_ipc_data):
    """Test validation of IPC data."""
    report = await processor.validate_specific(sample_ipc_data)
    
    assert report.is_valid or len(report.warnings) > 0  # May have warnings
    
    # Test with invalid phase
    invalid_data = sample_ipc_data.copy()
    invalid_data[0].phase = 6  # Invalid phase
    
    report_invalid = await processor.validate_specific(invalid_data)
    assert len(report_invalid.errors) > 0


@pytest.mark.asyncio
async def test_transform_to_entities(processor, sample_ipc_data):
    """Test transformation to IPCClassification entities."""
    entities = await processor.transform(sample_ipc_data)
    
    assert len(entities) == len(sample_ipc_data)
    assert all(isinstance(e, IPCClassification) for e in entities)
    
    # Check first entity
    first = entities[0]
    assert first.area_name == "Sa'ada - Sa'ada City"
    assert first.phase == IPCPhase.Phase4
    assert first.total_population == 200000
    assert len(first.drivers) > 0
    
    # Check calculated properties
    assert first.population_in_need > 0
    assert first.percent_in_need > 0
    assert first.severity_score > 0


@pytest.mark.asyncio
async def test_driver_mapping(processor, sample_ipc_data):
    """Test driver category mapping."""
    entities = await processor.transform(sample_ipc_data)
    
    # Check drivers are properly categorized
    for entity in entities:
        assert all(isinstance(d, DriverCategory) for d in entity.drivers)
        
        # Should have conflict and economic drivers based on sample data
        driver_types = set(entity.drivers)
        assert DriverCategory.CONFLICT in driver_types or DriverCategory.ECONOMIC in driver_types


@pytest.mark.asyncio
async def test_aggregate_ipc_data(processor, sample_ipc_data):
    """Test aggregation of IPC classifications."""
    entities = await processor.transform(sample_ipc_data)
    df = await processor.aggregate(entities)
    
    assert isinstance(df, pd.DataFrame)
    assert not df.empty
    
    # Check required columns
    expected_columns = [
        'area_code', 'governorate', 'district',
        'area_phase', 'total_population',
        'phase_3_plus_population', 'phase_3_plus_percent',
        'severity_score', 'temporal_key'
    ]
    
    for col in expected_columns:
        assert col in df.columns
    
    # Check phase columns
    for phase in range(1, 6):
        assert f'phase_{phase}_population' in df.columns
    
    # Check calculations
    assert all(df['phase_3_plus_percent'] >= 0)
    assert all(df['phase_3_plus_percent'] <= 100)
    assert all(df['severity_score'] > 0)


@pytest.mark.asyncio
async def test_calculate_metrics(processor, sample_ipc_data):
    """Test food security metrics calculation."""
    entities = await processor.transform(sample_ipc_data)
    df = await processor.aggregate(entities)
    
    from src.core.domain.food_security.entities import FoodSecurityMetrics
    metrics = await processor.calculate_metrics(df)
    
    assert isinstance(metrics, FoodSecurityMetrics)
    
    # Check population metrics
    assert metrics.total_population_analyzed > 0
    assert metrics.population_phase_3_plus > 0
    assert metrics.percent_phase_3_plus > 0
    
    # Check area distribution
    assert metrics.areas_by_phase is not None
    assert len(metrics.areas_by_phase) > 0
    
    # Check most affected areas
    assert metrics.most_affected_areas is not None
    assert len(metrics.most_affected_areas) > 0
    
    # Check governorate summary
    assert metrics.governorate_summary is not None
    assert len(metrics.governorate_summary) > 0


@pytest.mark.asyncio
async def test_severity_score_calculation(processor):
    """Test severity score calculation."""
    # Create test data with known distribution
    ipc = IPCData(
        governorate="Test",
        district="Test District",
        analysis_date=datetime.now(),
        projection_period_start=datetime.now(),
        projection_period_end=datetime.now() + timedelta(days=120),
        phase=3,
        population_phase={
            1: 10000,  # 10%
            2: 20000,  # 20%
            3: 40000,  # 40%
            4: 20000,  # 20%
            5: 10000   # 10%
        },
        total_population=100000,
        drivers=['Test'],
        confidence_level='High'
    )
    
    entities = await processor.transform([ipc])
    df = await processor.aggregate(entities)
    
    # Check severity score
    severity = df['severity_score'].iloc[0]
    assert severity > 0
    
    # Higher phase populations should result in higher severity
    # With exponential weights: 1*1 + 2*2 + 3*4 + 4*8 + 5*16 = 1+4+12+32+80 = 129
    # Per capita: 129/10 = 12.9
    expected = (10000*1 + 20000*2 + 40000*4 + 20000*8 + 10000*16) / 100000
    assert abs(severity - expected) < 0.1


@pytest.mark.asyncio
async def test_ipc_phase_properties():
    """Test IPC phase value object properties."""
    phase3 = IPCPhase.Phase3
    
    assert phase3.numeric_value == 3
    assert phase3.is_crisis_or_worse
    assert not phase3.is_emergency_or_worse
    assert phase3.get_color_code() == "#FF9800"  # Orange
    
    phase5 = IPCPhase.Phase5
    assert phase5.numeric_value == 5
    assert phase5.is_crisis_or_worse
    assert phase5.is_emergency_or_worse


@pytest.mark.asyncio
async def test_temporal_aggregation(processor):
    """Test aggregation across multiple time periods."""
    # Create data for multiple quarters
    ipc_data = []
    locations = [("Aden", "Crater", 2, 180000)]
    
    for quarter in range(4):
        analysis_date = datetime(2024, 1 + quarter * 3, 1)
        projection_start = analysis_date + timedelta(days=1)
        projection_end = projection_start + timedelta(days=120)
        
        for gov, district, phase, total_pop in locations:
            # Vary phase by quarter
            quarter_phase = min(5, phase + quarter // 2)
            
            ipc = IPCData(
                governorate=gov,
                district=district,
                analysis_date=analysis_date,
                projection_period_start=projection_start,
                projection_period_end=projection_end,
                phase=quarter_phase,
                population_phase={1: 50000, 2: 80000, 3: 40000, 4: 10000, 5: 0},
                total_population=total_pop,
                drivers=['Test'],
                confidence_level='High'
            )
            ipc_data.append(ipc)
    
    entities = await processor.transform(ipc_data)
    df = await processor.aggregate(entities)
    
    # Check we have multiple temporal keys
    assert df['temporal_key'].nunique() == 4
    assert all(df['temporal_key'].str.match(r'\d{4}-Q\d'))