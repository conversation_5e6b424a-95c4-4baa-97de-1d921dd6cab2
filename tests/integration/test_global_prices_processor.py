"""Integration tests for global prices processor."""

import pytest
import asyncio
from datetime import datetime, timedelta
from pathlib import Path
import pandas as pd

from src.infrastructure.processors.global_prices_processor import (
    GlobalPricesProcessor, GlobalPriceData
)
from src.infrastructure.processors.base_processor import SourceConfig
from src.infrastructure.data_quality.validation_framework import ValidationLevel
from src.core.domain.market.entities import Price


@pytest.fixture
def processor():
    """Create global prices processor instance."""
    config = SourceConfig(
        source_id="global_prices_test",
        name="Global Prices Test",
        description="Test processor",
        base_url="https://test.example.com",
        validation_level=ValidationLevel.STANDARD
    )
    
    return GlobalPricesProcessor(
        source_config=config,
        cache_manager=None,
        validator=None,
        hdx_client=None,
        data_source='giews'
    )


@pytest.fixture
def sample_global_prices():
    """Create sample global price data."""
    prices = {}
    
    # Generate prices for each commodity
    commodities = ['wheat', 'rice', 'sugar']
    base_date = datetime(2024, 1, 1)
    
    for commodity in commodities:
        commodity_prices = []
        for i in range(12):  # 12 months
            date = base_date + timedelta(days=30 * i)
            price = GlobalPriceData(
                commodity=commodity,
                date=date,
                price_usd=250 + i * 5,  # Increasing trend
                unit='USD/MT',
                source='Test Source',
                metadata={'test': True}
            )
            commodity_prices.append(price)
        
        prices[commodity] = commodity_prices
    
    return prices


@pytest.mark.asyncio
async def test_download_creates_synthetic_data(processor):
    """Test that download creates synthetic data when no API available."""
    # Download should fall back to synthetic data
    result = await processor.download()
    
    assert isinstance(result, dict)
    assert len(result) > 0
    
    # Check tracked commodities are present
    for commodity in ['wheat', 'rice', 'sugar']:
        assert commodity in result
        assert len(result[commodity]) > 0
        
        # Check data structure
        first_price = result[commodity][0]
        assert isinstance(first_price, GlobalPriceData)
        assert first_price.commodity == commodity
        assert first_price.price_usd > 0
        assert first_price.unit in ['USD/MT', 'USD/bbl']


@pytest.mark.asyncio
async def test_validate_specific(processor, sample_global_prices):
    """Test validation of global price data."""
    report = await processor.validate_specific(sample_global_prices)
    
    assert report.is_valid
    assert len(report.errors) == 0
    
    # Test with missing data
    empty_data = {}
    report_empty = await processor.validate_specific(empty_data)
    
    assert not report_empty.is_valid
    assert len(report_empty.errors) > 0


@pytest.mark.asyncio
async def test_transform_to_price_entities(processor, sample_global_prices):
    """Test transformation to Price entities with import parity."""
    entities = await processor.transform(sample_global_prices)
    
    assert len(entities) > 0
    assert all(isinstance(e, Price) for e in entities)
    
    # Check we have both international and port prices
    market_ids = set(e.market_id for e in entities)
    assert 'international' in market_ids
    assert any('port' in m for m in market_ids)
    
    # Check import parity calculation
    intl_prices = [e for e in entities if e.market_id == 'international']
    port_prices = [e for e in entities if 'port' in e.market_id]
    
    assert len(port_prices) > 0
    
    # Port prices should be higher than international (due to import costs)
    for port_price in port_prices[:5]:  # Check first few
        # Find corresponding international price
        intl_price = next(
            (p for p in intl_prices 
             if p.commodity.id == port_price.commodity.id and p.date == port_price.date),
            None
        )
        
        if intl_price:
            assert port_price.price > intl_price.price


@pytest.mark.asyncio
async def test_aggregate_monthly(processor, sample_global_prices):
    """Test monthly aggregation of global prices."""
    # Transform first
    entities = await processor.transform(sample_global_prices)
    
    # Aggregate
    df = await processor.aggregate(entities)
    
    assert isinstance(df, pd.DataFrame)
    assert not df.empty
    
    # Check required columns
    expected_columns = [
        'temporal_key', 'commodity', 'market_id',
        'avg_price_usd', 'price_volatility', 'price_index'
    ]
    for col in expected_columns:
        assert col in df.columns
    
    # Check temporal keys
    assert all(df['temporal_key'].str.match(r'\d{4}-\d{2}'))
    
    # Check price index calculation
    assert 'price_index' in df.columns
    assert df['price_index'].notna().all()


@pytest.mark.asyncio
async def test_calculate_metrics(processor, sample_global_prices):
    """Test metrics calculation."""
    # Process data
    entities = await processor.transform(sample_global_prices)
    df = await processor.aggregate(entities)
    
    # Calculate metrics
    metrics = await processor.calculate_metrics(df)
    
    assert isinstance(metrics, dict)
    assert 'n_commodities' in metrics
    assert 'avg_volatility' in metrics
    assert 'by_commodity' in metrics
    
    # Check commodity-specific metrics
    assert len(metrics['by_commodity']) > 0
    for commodity, comm_metrics in metrics['by_commodity'].items():
        assert 'current_price' in comm_metrics
        assert 'avg_price' in comm_metrics
        assert 'volatility' in comm_metrics


@pytest.mark.asyncio
async def test_import_parity_calculation(processor):
    """Test import parity price calculation."""
    # Test specific calculation
    intl_price = 250  # USD/MT
    
    import_parity = processor._calculate_import_parity(
        intl_price,
        'wheat',
        'hodeidah'
    )
    
    # Import parity should include all costs
    assert import_parity > intl_price / 1000  # Converted to per kg
    
    # Test different ports
    hodeidah_parity = processor._calculate_import_parity(intl_price, 'wheat', 'hodeidah')
    aden_parity = processor._calculate_import_parity(intl_price, 'wheat', 'aden')
    
    # Hodeidah should be more expensive (higher transport cost)
    assert hodeidah_parity > aden_parity


@pytest.mark.asyncio
async def test_commodity_category_mapping(processor):
    """Test commodity category mapping."""
    assert processor._get_commodity_category('wheat') == 'cereals'
    assert processor._get_commodity_category('rice') == 'cereals'
    assert processor._get_commodity_category('sugar') == 'food'
    assert processor._get_commodity_category('oil_crude') == 'fuel'
    assert processor._get_commodity_category('unknown') == 'other'


@pytest.mark.asyncio
async def test_unit_conversion(processor):
    """Test price unit conversions."""
    from src.core.domain.market.entities import Commodity
    from src.core.domain.market.value_objects import CommodityUnit
    
    # Test MT to kg conversion
    wheat = Commodity(
        id='wheat',
        name='Wheat',
        category='cereals',
        unit=CommodityUnit.KILOGRAM
    )
    
    price_per_kg = processor._convert_to_base_unit(250, 'USD/MT', wheat)
    assert price_per_kg == 0.25  # 250 USD/MT = 0.25 USD/kg
    
    # Test barrel to liter conversion
    oil = Commodity(
        id='oil',
        name='Oil',
        category='fuel',
        unit=CommodityUnit.LITER
    )
    
    price_per_liter = processor._convert_to_base_unit(80, 'USD/bbl', oil)
    assert abs(price_per_liter - 0.503) < 0.01  # 80 USD/barrel ≈ 0.503 USD/liter