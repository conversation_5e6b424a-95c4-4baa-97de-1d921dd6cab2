"""Integration tests for Pipeline V2 CLI commands."""

import pytest
from typer.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from pathlib import Path
import json
import tempfile
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from src.interfaces.cli.app import app
from src.interfaces.cli.pipeline_v2 import (
    PipelineV2Orchestrator, _show_execution_plan, 
    _display_validation_results, _display_pipeline_status
)
from src.infrastructure.data_quality.validation_framework import (
    ValidationResult, ValidationLevel
)


runner = CliRunner()


class TestPipelineV2CLI:
    """Test Pipeline V2 CLI commands."""
    
    def test_pipeline_run_dry_run(self):
        """Test dry run mode."""
        result = runner.invoke(app, ["pipeline", "run", "--dry-run"])
        
        assert result.exit_code == 0
        assert "Execution Plan" in result.stdout
        assert "Data Collection" in result.stdout
        assert "Panel Integration" in result.stdout
        assert "Resource Estimates" in result.stdout
    
    def test_pipeline_run_help(self):
        """Test help command."""
        result = runner.invoke(app, ["pipeline", "run", "--help"])
        
        assert result.exit_code == 0
        assert "Run the enhanced data pipeline V2" in result.stdout
        assert "--start-date" in result.stdout
        assert "--sources" in result.stdout
    
    def test_pipeline_status_command(self):
        """Test status command."""
        # Create mock status file
        with tempfile.TemporaryDirectory() as tmpdir:
            status_dir = Path(tmpdir) / "data" / "pipeline_status"
            status_dir.mkdir(parents=True)
            
            status_data = {
                "pipeline_id": "pipeline_test_123",
                "status": "completed",
                "started_at": "2025-06-11T14:30:00",
                "duration_seconds": 1234.5,
                "coverage": 0.891,
                "stages": {
                    "collection": 1.0,
                    "processing": 1.0,
                    "integration": 0.8
                }
            }
            
            with open(status_dir / "pipeline_test_123_status.json", "w") as f:
                json.dump(status_data, f)
            
            # Mock the status directory location
            import src.interfaces.cli.pipeline_v2 as pipeline_v2
            original_path = Path("data/pipeline_status")
            pipeline_v2.Path = lambda x: status_dir if x == "data/pipeline_status" else Path(x)
            
            result = runner.invoke(app, ["pipeline", "status"])
            
            assert result.exit_code == 0
            assert "pipeline_test_123" in result.stdout
            
            # Restore
            pipeline_v2.Path = Path
    
    def test_pipeline_validate_missing_file(self):
        """Test validation with missing file."""
        result = runner.invoke(app, ["pipeline", "validate", "nonexistent.parquet"])
        
        assert result.exit_code == 1
        assert "File not found" in result.stdout
    
    def test_pipeline_validate_with_file(self):
        """Test validation with actual file."""
        with tempfile.NamedTemporaryFile(suffix=".parquet", delete=False) as tmp:
            # Create test data
            df = pd.DataFrame({
                'market_id': ['market1', 'market2'] * 10,
                'commodity': ['wheat', 'rice'] * 10,
                'time_period': pd.date_range('2024-01-01', periods=20, freq='D'),
                'price': np.random.uniform(100, 1000, 20),
                'usdprice': np.random.uniform(0.1, 1.0, 20),
                'exchange_rate_used': np.random.uniform(500, 2000, 20),
                'currency_zone': ['HOUTHI', 'GOVERNMENT'] * 10
            })
            
            df.to_parquet(tmp.name)
            
            result = runner.invoke(app, [
                "pipeline", "validate", tmp.name,
                "--level", "basic",
                "--source-type", "panel"
            ])
            
            # Clean up
            Path(tmp.name).unlink()
            
            assert result.exit_code == 0
            assert "Validation" in result.stdout
    
    def test_pipeline_sources_list(self):
        """Test sources list command."""
        result = runner.invoke(app, ["pipeline", "sources", "--list-all"])
        
        assert result.exit_code == 0
        assert "Available Data Sources" in result.stdout
        assert "conflict" in result.stdout
        assert "ACLED" in result.stdout
        assert "prices" in result.stdout
        assert "WFP" in result.stdout
    
    def test_pipeline_sources_info(self):
        """Test sources info command."""
        result = runner.invoke(app, ["pipeline", "sources", "--info", "conflict"])
        
        assert result.exit_code == 0
        assert "ACLED Conflict Data" in result.stdout
        assert "API" in result.stdout
        assert "Daily" in result.stdout
    
    def test_pipeline_config_show(self):
        """Test config show command."""
        result = runner.invoke(app, ["pipeline", "config", "--show"])
        
        # May not have config file, but should handle gracefully
        assert result.exit_code == 0
    
    def test_pipeline_config_export_import(self):
        """Test config export and import."""
        with tempfile.TemporaryDirectory() as tmpdir:
            config_path = Path(tmpdir) / "test_config.json"
            
            # Create test config
            test_config = {
                "start_date": "2024-01-01",
                "end_date": "2024-12-31",
                "sources": ["prices", "conflict"],
                "validation_level": "standard"
            }
            
            with open(config_path, "w") as f:
                json.dump(test_config, f)
            
            # Test import
            result = runner.invoke(app, [
                "pipeline", "config",
                "--import-from", str(config_path)
            ])
            
            assert "imported successfully" in result.stdout or result.exit_code == 0


class TestPipelineV2Functions:
    """Test Pipeline V2 helper functions."""
    
    def test_show_execution_plan(self, capsys):
        """Test execution plan display."""
        from rich.console import Console
        console = Console()
        
        config = {
            'sources': ['conflict', 'prices'],
            'start_date': datetime.now(),
            'end_date': datetime.now() + timedelta(days=30)
        }
        
        _show_execution_plan(config)
        
        captured = capsys.readouterr()
        assert "Execution Plan" in captured.out
        assert "Data Collection" in captured.out
        assert "conflict" in captured.out
        assert "prices" in captured.out
    
    def test_display_validation_results(self, capsys):
        """Test validation results display."""
        result = ValidationResult(
            is_valid=False,
            errors=["Missing USD prices", "Invalid exchange rates"],
            warnings=["Low coverage", "High price variance"],
            summary={
                "total_records": 1000,
                "valid_records": 800,
                "coverage": 0.80
            }
        )
        
        _display_validation_results(result)
        
        captured = capsys.readouterr()
        assert "FAILED" in captured.out
        assert "Missing USD prices" in captured.out
        assert "Low coverage" in captured.out
        assert "total_records" in captured.out
    
    def test_display_pipeline_status(self, capsys):
        """Test pipeline status display."""
        status = {
            'pipeline_id': 'test_pipeline_123',
            'status': 'running',
            'stages': {
                'collection': 1.0,
                'processing': 0.5,
                'integration': 0.0
            },
            'duration_seconds': 300
        }
        
        _display_pipeline_status(status)
        
        captured = capsys.readouterr()
        assert "test_pipeline_123" in captured.out
        assert "Progress" in captured.out


class TestPipelineV2Orchestrator:
    """Test Pipeline V2 Orchestrator."""
    
    @pytest.fixture
    def mock_components(self, mocker):
        """Mock pipeline components."""
        mock_factory = mocker.Mock()
        mock_panel_builder = mocker.Mock()
        mock_derived_calc = mocker.Mock()
        mock_validation = mocker.Mock()
        mock_cache = mocker.Mock()
        
        return {
            'processor_factory': mock_factory,
            'panel_builder': mock_panel_builder,
            'derived_calculator': mock_derived_calc,
            'validation_framework': mock_validation,
            'cache_manager': mock_cache
        }
    
    @pytest.mark.asyncio
    async def test_orchestrator_initialization(self, mock_components):
        """Test orchestrator initialization."""
        orchestrator = PipelineV2Orchestrator(**mock_components)
        
        assert orchestrator.processor_factory is not None
        assert orchestrator.panel_builder is not None
        assert orchestrator.progress_tracker is not None
    
    @pytest.mark.asyncio
    async def test_orchestrator_run_pipeline(self, mock_components, mocker):
        """Test running pipeline through orchestrator."""
        orchestrator = PipelineV2Orchestrator(**mock_components)
        
        # Mock processor results
        mock_result = mocker.Mock()
        mock_result.success = True
        mock_result.markets = []
        mock_result.observations = []
        
        mock_processor = mocker.Mock()
        mock_processor.process = mocker.AsyncMock(return_value=mock_result)
        
        mock_components['processor_factory'].create_processor.return_value = mock_processor
        
        # Mock panel builder
        mock_panel = pd.DataFrame({'test': [1, 2, 3]})
        mock_components['panel_builder'].process = mocker.AsyncMock(
            return_value=mock_panel
        )
        
        # Mock derived calculator
        mock_components['derived_calculator'].calculate_all_variables = mocker.AsyncMock(
            return_value=mock_panel
        )
        
        # Mock validation
        mock_validation_result = mocker.Mock()
        mock_validation_result.is_valid = True
        mock_validation_result.errors = []
        mock_components['validation_framework'].validate_dataframe = mocker.AsyncMock(
            return_value=mock_validation_result
        )
        
        # Run pipeline
        config = {
            'start_date': datetime.now(),
            'end_date': datetime.now() + timedelta(days=30),
            'output_dir': Path('/tmp')
        }
        
        with tempfile.TemporaryDirectory() as tmpdir:
            config['output_dir'] = tmpdir
            result = await orchestrator.run_full_pipeline(config)
        
        assert result.success
        assert result.metadata['validation_status'] == 'passed'


@pytest.mark.parametrize("level,expected_errors", [
    (ValidationLevel.BASIC, 1),  # Only missing columns
    (ValidationLevel.STANDARD, 2),  # Missing + critical
    (ValidationLevel.STRICT, 3),  # All errors
])
def test_validation_level_filtering(level, expected_errors):
    """Test validation level filtering logic."""
    from src.infrastructure.data_quality.validation_framework import ValidationFramework
    
    framework = ValidationFramework()
    
    # Create test dataframe missing required columns
    df = pd.DataFrame({
        'price': [100, 200, 300],
        'market_id': ['m1', 'm2', 'm3']
    })
    
    # This will fail because it's missing required panel columns
    import asyncio
    result = asyncio.run(framework.validate_dataframe(
        df, 
        source_type='panel',
        level=level
    ))
    
    # Basic level might have fewer errors due to filtering
    assert len(result.errors) >= 0  # Adjust based on actual implementation