"""
Integration tests for all data processors working together.

Tests the complete data pipeline from raw sources to integrated panel.
"""
import asyncio
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
import pytest
import pandas as pd
import geopandas as gpd
import numpy as np
from shapely.geometry import Point
from unittest.mock import Mock, AsyncMock, patch

from src.infrastructure.processors.processor_factory import ProcessorFactory
from src.infrastructure.processors.base_processor import SourceConfig
from src.infrastructure.caching.cache_manager import CacheManager
from src.infrastructure.data_quality.validation_framework import (
    ValidationFramework, ValidationLevel
)
from src.infrastructure.external_services.hdx_client import HDXClient
from src.infrastructure.spatial.spatial_integration_service import SpatialIntegrationService
from src.infrastructure.temporal.temporal_alignment_service import TemporalAlignmentService
from src.core.domain.market.entities import Market
from src.core.domain.geography.entities import Governorate, District


@pytest.fixture
def cache_manager(tmp_path):
    """Create cache manager with temp directory."""
    return CacheManager(cache_dir=tmp_path / "cache")


@pytest.fixture
def validation_framework():
    """Create validation framework."""
    return ValidationFramework()


@pytest.fixture
def hdx_client(cache_manager):
    """Create HDX client."""
    return HDXClient(cache_manager=cache_manager)


@pytest.fixture
def spatial_service():
    """Create spatial integration service."""
    return SpatialIntegrationService()


@pytest.fixture
def temporal_service():
    """Create temporal alignment service."""
    return TemporalAlignmentService()


@pytest.fixture
def processor_factory(cache_manager, validation_framework):
    """Create processor factory."""
    return ProcessorFactory(
        cache_manager=cache_manager,
        validator=validation_framework
    )


@pytest.fixture
def sample_markets():
    """Create sample market locations."""
    markets = [
        {
            'market_id': 'sanaa_main',
            'name': 'Sana\'a Main Market',
            'governorate': 'Sana\'a',
            'district': 'Old City',
            'latitude': 15.3694,
            'longitude': 44.1910,
            'currency_zone': 'HOUTHI'
        },
        {
            'market_id': 'aden_port',
            'name': 'Aden Port Market',
            'governorate': 'Aden',
            'district': 'Crater',
            'latitude': 12.7855,
            'longitude': 45.0187,
            'currency_zone': 'GOVERNMENT'
        },
        {
            'market_id': 'taiz_central',
            'name': 'Taiz Central Market',
            'governorate': 'Taiz',
            'district': 'Al Qahirah',
            'latitude': 13.5795,
            'longitude': 44.0212,
            'currency_zone': 'CONTESTED'
        }
    ]
    
    # Create GeoDataFrame
    geometry = [Point(m['longitude'], m['latitude']) for m in markets]
    gdf = gpd.GeoDataFrame(markets, geometry=geometry, crs='EPSG:4326')
    
    return gdf


class TestProcessorIntegration:
    """Test all processors working together."""
    
    @pytest.mark.asyncio
    async def test_conflict_processor_integration(
        self, processor_factory, sample_markets, spatial_service
    ):
        """Test conflict processor with spatial buffer calculations."""
        # Create conflict processor
        config = SourceConfig(
            source_id='acled',
            source_type='conflict',
            base_url='https://api.acleddata.com/acled/read',
            update_frequency='weekly'
        )
        
        processor = processor_factory.create_processor(
            processor_type='conflict',
            source_config=config,
            spatial_service=spatial_service,
            buffer_distances=[10, 25, 50]
        )
        
        # Mock ACLED data download
        mock_events = pd.DataFrame({
            'event_id': ['EVT001', 'EVT002', 'EVT003'],
            'event_date': ['2024-01-15', '2024-01-20', '2024-01-25'],
            'latitude': [15.35, 12.79, 13.58],
            'longitude': [44.20, 45.02, 44.02],
            'event_type': ['Battles', 'Protests', 'Battles'],
            'fatalities': [5, 0, 3],
            'actor1': ['Houthis', 'Civilians', 'Government Forces'],
            'notes': ['Clashes in Sana\'a', 'Protest in Aden', 'Fighting in Taiz']
        })
        
        with patch.object(processor, 'download', return_value=mock_events):
            # Process data
            events, metrics = await processor.process(
                data_source=mock_events,
                markets=sample_markets,
                start_date=datetime(2024, 1, 1),
                end_date=datetime(2024, 1, 31)
            )
        
        # Verify results
        assert len(events) > 0
        assert metrics.total_events == 3
        assert metrics.total_fatalities == 8
        
        # Check spatial buffers were calculated
        sanaa_events = [e for e in events if e.metadata.get('nearest_market') == 'sanaa_main']
        assert len(sanaa_events) > 0
        
        # Verify buffer distances
        for event in sanaa_events:
            assert 'buffer_10km' in event.metadata
            assert 'buffer_25km' in event.metadata
            assert 'buffer_50km' in event.metadata
    
    @pytest.mark.asyncio
    async def test_climate_processor_integration(
        self, processor_factory, sample_markets, cache_manager
    ):
        """Test climate processor with market value extraction."""
        # Create climate processor
        config = SourceConfig(
            source_id='chirps',
            source_type='climate',
            base_url='https://data.chc.ucsb.edu/products/CHIRPS-2.0/',
            update_frequency='daily'
        )
        
        # Import required classes
        from src.infrastructure.processors.geo_data_processor import GeoDataProcessor
        geo_processor = GeoDataProcessor(config, cache_manager, ValidationFramework())
        
        processor = processor_factory.create_processor(
            processor_type='climate',
            source_config=config,
            geo_processor=geo_processor,
            climate_source='chirps',
            variable='precipitation',
            market_locations=sample_markets
        )
        
        # Process will create synthetic data
        observations = await processor.process(data_source=None)
        
        # Verify results
        assert len(observations) > 0
        
        # Check we have data for each market
        market_ids = sample_markets['market_id'].unique()
        obs_markets = set(obs.metadata.get('market_id') for obs in observations)
        assert len(obs_markets.intersection(market_ids)) == len(market_ids)
        
        # Verify climate values are reasonable
        for obs in observations:
            assert 0 <= obs.value <= 500  # Reasonable rainfall range
            assert obs.unit == 'mm'
    
    @pytest.mark.asyncio
    async def test_population_processor_integration(
        self, processor_factory, sample_markets
    ):
        """Test population processor with market buffer calculations."""
        # Create population processor
        config = SourceConfig(
            source_id='worldpop',
            source_type='population',
            base_url='https://www.worldpop.org/',
            update_frequency='annual'
        )
        
        processor = processor_factory.create_processor(
            processor_type='population',
            source_config=config,
            data_source='worldpop',
            market_locations=sample_markets
        )
        
        # Process will create synthetic data
        observations = await processor.process(data_source=None)
        
        # Verify results
        assert len(observations) > 0
        
        # Check population density calculations
        for obs in observations:
            assert obs.population_total > 0
            assert obs.population_density > 0
            
            # Urban areas should have higher density
            if obs.metadata.get('market_id') == 'sanaa_main':
                assert obs.population_density > 1000  # Urban threshold
    
    @pytest.mark.asyncio
    async def test_aid_processor_integration(
        self, processor_factory, sample_markets
    ):
        """Test aid distribution processor."""
        # Create aid processor
        config = SourceConfig(
            source_id='ocha_3w',
            source_type='aid',
            base_url='https://data.humdata.org/',
            update_frequency='monthly'
        )
        
        processor = processor_factory.create_processor(
            processor_type='aid',
            source_config=config
        )
        
        # Mock aid data
        mock_aid = pd.DataFrame({
            'organization': ['WFP', 'UNICEF', 'WHO'],
            'governorate': ['Sana\'a', 'Aden', 'Taiz'],
            'district': ['Old City', 'Crater', 'Al Qahirah'],
            'cluster': ['Food Security', 'WASH', 'Health'],
            'beneficiaries': [5000, 3000, 2000],
            'modality': ['In-kind', 'Cash', 'Service'],
            'start_date': ['2024-01-01', '2024-01-01', '2024-01-15'],
            'end_date': ['2024-01-31', '2024-01-31', '2024-01-31']
        })
        
        with patch.object(processor, 'download', return_value=mock_aid):
            # Process data
            distributions, metrics = await processor.process(
                data_source=mock_aid,
                start_date=datetime(2024, 1, 1),
                end_date=datetime(2024, 1, 31)
            )
        
        # Verify results
        assert len(distributions) == 3
        assert metrics.total_beneficiaries == 10000
        assert len(metrics.organizations) == 3
        assert 'Food Security' in metrics.clusters
    
    @pytest.mark.asyncio
    async def test_infrastructure_processor_integration(
        self, processor_factory, sample_markets
    ):
        """Test infrastructure processor for market accessibility."""
        # Create infrastructure processor
        config = SourceConfig(
            source_id='osm',
            source_type='infrastructure',
            base_url='https://overpass-api.de/api/',
            update_frequency='monthly'
        )
        
        processor = processor_factory.create_processor(
            processor_type='infrastructure',
            source_config=config,
            market_locations=sample_markets
        )
        
        # Process will calculate accessibility metrics
        infrastructure = await processor.process(data_source=None)
        
        # Verify results
        assert len(infrastructure) > 0
        
        # Check accessibility metrics
        for infra in infrastructure:
            market_id = infra.metadata.get('market_id')
            assert market_id in sample_markets['market_id'].values
            
            # Verify calculated metrics
            assert 'distance_to_port' in infra.metadata
            assert 'distance_to_border' in infra.metadata
            assert 'road_density' in infra.metadata
    
    @pytest.mark.asyncio
    async def test_multi_processor_pipeline(
        self, processor_factory, sample_markets, spatial_service,
        temporal_service, cache_manager
    ):
        """Test multiple processors working together in a pipeline."""
        # Configuration for all processors
        configs = {
            'conflict': SourceConfig(
                source_id='acled',
                source_type='conflict',
                base_url='https://api.acleddata.com/acled/read',
                update_frequency='weekly'
            ),
            'climate': SourceConfig(
                source_id='chirps',
                source_type='climate',
                base_url='https://data.chc.ucsb.edu/',
                update_frequency='daily'
            ),
            'population': SourceConfig(
                source_id='worldpop',
                source_type='population',
                base_url='https://www.worldpop.org/',
                update_frequency='annual'
            )
        }
        
        # Create processors
        processors = {}
        for ptype, config in configs.items():
            kwargs = {'source_config': config}
            
            if ptype == 'conflict':
                kwargs.update({
                    'spatial_service': spatial_service,
                    'buffer_distances': [10, 25]
                })
            elif ptype == 'climate':
                from src.infrastructure.processors.geo_data_processor import GeoDataProcessor
                geo_processor = GeoDataProcessor(config, cache_manager, ValidationFramework())
                kwargs.update({
                    'geo_processor': geo_processor,
                    'climate_source': 'chirps',
                    'variable': 'precipitation',
                    'market_locations': sample_markets
                })
            elif ptype == 'population':
                kwargs.update({
                    'data_source': 'worldpop',
                    'market_locations': sample_markets
                })
            
            processors[ptype] = processor_factory.create_processor(
                processor_type=ptype,
                **kwargs
            )
        
        # Process data from all sources
        results = {}
        
        # Mock conflict data
        mock_conflict = pd.DataFrame({
            'event_id': ['EVT001'],
            'event_date': ['2024-01-15'],
            'latitude': [15.35],
            'longitude': [44.20],
            'event_type': ['Battles'],
            'fatalities': [5],
            'actor1': ['Houthis']
        })
        
        with patch.object(processors['conflict'], 'download', return_value=mock_conflict):
            conflict_events, conflict_metrics = await processors['conflict'].process(
                data_source=mock_conflict,
                markets=sample_markets,
                start_date=datetime(2024, 1, 1),
                end_date=datetime(2024, 1, 31)
            )
            results['conflict'] = (conflict_events, conflict_metrics)
        
        # Process climate data (synthetic)
        climate_obs = await processors['climate'].process(data_source=None)
        results['climate'] = climate_obs
        
        # Process population data (synthetic)
        pop_obs = await processors['population'].process(data_source=None)
        results['population'] = pop_obs
        
        # Verify all processors produced results
        assert len(results['conflict'][0]) > 0
        assert len(results['climate']) > 0
        assert len(results['population']) > 0
        
        # Test temporal alignment
        # Convert all to monthly observations
        monthly_data = {}
        
        # Aggregate conflict to monthly
        conflict_df = pd.DataFrame([
            {
                'date': datetime(2024, 1, 1),
                'market_id': e.metadata.get('nearest_market'),
                'events': 1,
                'fatalities': e.fatalities
            }
            for e in results['conflict'][0]
        ])
        monthly_data['conflict'] = conflict_df.groupby(['date', 'market_id']).sum()
        
        # Climate is already temporal
        climate_df = pd.DataFrame([
            {
                'date': obs.observation_date,
                'market_id': obs.metadata.get('market_id'),
                'rainfall': obs.value
            }
            for obs in results['climate']
        ])
        monthly_data['climate'] = climate_df.groupby(['date', 'market_id']).mean()
        
        # Population is static for the year
        pop_df = pd.DataFrame([
            {
                'market_id': obs.metadata.get('market_id'),
                'population': obs.population_total,
                'density': obs.population_density
            }
            for obs in results['population']
        ])
        
        # Verify we can join all data
        assert len(monthly_data['conflict']) > 0
        assert len(monthly_data['climate']) > 0
        assert len(pop_df) > 0
        
        # All processors should have data for our sample markets
        markets_with_data = set()
        for df in monthly_data.values():
            if 'market_id' in df.index.names:
                markets_with_data.update(df.index.get_level_values('market_id'))
            elif 'market_id' in df.columns:
                markets_with_data.update(df['market_id'].unique())
        
        assert len(markets_with_data) >= 2  # At least 2 markets have data