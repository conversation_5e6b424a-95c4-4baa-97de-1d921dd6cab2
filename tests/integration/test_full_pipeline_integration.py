"""Full pipeline integration test for all processors working together."""

import pytest
import asyncio
from datetime import datetime, timedelta
from pathlib import Path
import pandas as pd
import numpy as np
from unittest.mock import Mock, AsyncMock, patch
import tempfile
import json

from src.infrastructure.processors import (
    ProcessorFactory, ProcessorConfig, ProcessorType,
    WFPProcessor, ACLEDProcessor, ACAPSProcessor,
    ConflictProcessor, ClimateProcessor, PopulationProcessor,
    InfrastructureProcessor, AidProcessor, GlobalPricesProcessor,
    IPCProcessor, PanelBuilder
)
from src.infrastructure.processors.base_processor import SourceConfig
from src.infrastructure.data_quality.validation_framework import (
    ValidationFramework, ValidationLevel
)
from src.infrastructure.caching.cache_manager import CacheManager
from src.application.services.data_pipeline_orchestrator import (
    DataPipelineOrchestrator, PipelineConfig
)
from src.infrastructure.services.progress_tracking import PipelineProgressTracker


class TestFullPipelineIntegration:
    """Test all processors working together in a complete pipeline."""
    
    @pytest.fixture
    async def cache_manager(self):
        """Create cache manager with temp directory."""
        with tempfile.TemporaryDirectory() as tmpdir:
            cache = CacheManager(cache_dir=Path(tmpdir))
            yield cache
    
    @pytest.fixture
    def validation_framework(self):
        """Create validation framework."""
        return ValidationFramework()
    
    @pytest.fixture
    def mock_hdx_client(self):
        """Create mock HDX client."""
        client = AsyncMock()
        client.search_datasets = AsyncMock(return_value=[])
        client.download_dataset = AsyncMock(return_value=Path("/tmp/test.csv"))
        return client
    
    @pytest.fixture
    def pipeline_config(self):
        """Create pipeline configuration."""
        return PipelineConfig(
            start_date=datetime(2023, 1, 1),
            end_date=datetime(2023, 12, 31),
            data_sources={
                'wfp': {'enabled': True, 'priority': 1},
                'acled': {'enabled': True, 'priority': 2},
                'acaps': {'enabled': True, 'priority': 3},
                'climate': {'enabled': True, 'priority': 4},
                'population': {'enabled': True, 'priority': 5},
                'infrastructure': {'enabled': True, 'priority': 6},
                'aid': {'enabled': True, 'priority': 7},
                'global_prices': {'enabled': True, 'priority': 8},
                'ipc': {'enabled': True, 'priority': 9}
            },
            output_format='parquet',
            validation_level=ValidationLevel.STANDARD,
            enable_caching=True,
            parallel_downloads=3
        )
    
    @pytest.mark.asyncio
    async def test_all_processors_initialization(self, cache_manager, validation_framework):
        """Test that all processors can be initialized properly."""
        factory = ProcessorFactory(
            cache_manager=cache_manager,
            validator=validation_framework
        )
        
        # Test each processor type
        processor_types = [
            ProcessorType.PRICE,
            ProcessorType.CONFLICT,
            ProcessorType.CLIMATE,
            ProcessorType.POPULATION,
            ProcessorType.INFRASTRUCTURE,
            ProcessorType.AID,
            ProcessorType.CONTROL_ZONES,
            ProcessorType.GLOBAL_PRICES,
            ProcessorType.FOOD_SECURITY
        ]
        
        for proc_type in processor_types:
            config = ProcessorConfig(
                processor_type=proc_type,
                source_config=SourceConfig(
                    source_id=f"{proc_type.value}_test",
                    name=f"{proc_type.value} Test",
                    description="Test processor"
                )
            )
            
            processor = factory.create_processor(config)
            assert processor is not None
            assert hasattr(processor, 'process')
    
    @pytest.mark.asyncio
    async def test_data_flow_between_processors(self, cache_manager, validation_framework, mock_hdx_client):
        """Test data flows correctly between processors."""
        # Create processors
        wfp_processor = WFPProcessor(
            source_config=SourceConfig(
                source_id="wfp",
                name="WFP",
                description="WFP processor"
            ),
            cache_manager=cache_manager,
            validator=validation_framework
        )
        
        panel_builder = PanelBuilder(
            source_config=SourceConfig(
                source_id="panel",
                name="Panel Builder",
                description="Panel builder"
            ),
            cache_manager=cache_manager,
            validator=validation_framework
        )
        
        # Mock WFP data
        mock_wfp_data = pd.DataFrame({
            'market': ['Sana\'a', 'Aden', 'Taiz'],
            'commodity': ['Wheat', 'Wheat', 'Wheat'],
            'date': pd.date_range('2023-01-01', periods=3, freq='D'),
            'price': [1000, 1500, 1200],
            'currency': ['YER', 'YER', 'YER'],
            'unit': ['kg', 'kg', 'kg']
        })
        
        with patch.object(wfp_processor, 'download', return_value=mock_wfp_data):
            # Process WFP data
            result = await wfp_processor.process()
            assert result.success
            assert result.data is not None
            
            # Use in panel builder
            panel = await panel_builder.create_integrated_panel(
                price_observations=result.data,
                start_date=datetime(2023, 1, 1),
                end_date=datetime(2023, 1, 31)
            )
            
            assert isinstance(panel, pd.DataFrame)
            assert not panel.empty
    
    @pytest.mark.asyncio
    async def test_pipeline_orchestrator_integration(
        self, cache_manager, validation_framework, mock_hdx_client, pipeline_config
    ):
        """Test full pipeline orchestration."""
        # Create orchestrator
        orchestrator = DataPipelineOrchestrator(
            config=pipeline_config,
            cache_manager=cache_manager,
            hdx_client=mock_hdx_client
        )
        
        # Mock processor outputs
        with patch.multiple(
            orchestrator,
            _process_wfp_data=AsyncMock(return_value=pd.DataFrame({
                'market': ['Sana\'a'],
                'price': [1000]
            })),
            _process_conflict_data=AsyncMock(return_value=pd.DataFrame({
                'location': ['Sana\'a'],
                'events': [5]
            })),
            _process_climate_data=AsyncMock(return_value=pd.DataFrame({
                'location': ['Sana\'a'],
                'rainfall': [25.5]
            })),
            _build_integrated_panel=AsyncMock(return_value=pd.DataFrame({
                'market': ['Sana\'a'],
                'integrated': [True]
            }))
        ):
            # Run pipeline
            result = await orchestrator.run_pipeline()
            
            assert result is not None
            assert 'panel' in result
            assert result['success'] is True
    
    @pytest.mark.asyncio
    async def test_processor_error_handling(self, cache_manager, validation_framework):
        """Test error handling across processors."""
        # Create processor that will fail
        conflict_processor = ConflictProcessor(
            source_config=SourceConfig(
                source_id="conflict",
                name="Conflict",
                description="Conflict processor"
            ),
            cache_manager=cache_manager,
            validator=validation_framework,
            acled_client=None  # Will cause download to fail
        )
        
        # Should handle error gracefully
        result = await conflict_processor.process()
        assert not result.success
        assert result.errors is not None
        assert len(result.errors) > 0
    
    @pytest.mark.asyncio
    async def test_parallel_processor_execution(self, cache_manager, validation_framework):
        """Test multiple processors can run in parallel."""
        factory = ProcessorFactory(
            cache_manager=cache_manager,
            validator=validation_framework
        )
        
        # Create multiple processors
        processors = []
        for proc_type in [ProcessorType.CLIMATE, ProcessorType.POPULATION, ProcessorType.INFRASTRUCTURE]:
            config = ProcessorConfig(
                processor_type=proc_type,
                source_config=SourceConfig(
                    source_id=f"{proc_type.value}_test",
                    name=f"{proc_type.value} Test",
                    description="Test processor"
                )
            )
            processors.append(factory.create_processor(config))
        
        # Mock their process methods to track timing
        start_times = []
        end_times = []
        
        async def mock_process(self):
            start_times.append(asyncio.get_event_loop().time())
            await asyncio.sleep(0.1)  # Simulate work
            end_times.append(asyncio.get_event_loop().time())
            return Mock(success=True, data=pd.DataFrame())
        
        for processor in processors:
            processor.process = mock_process.__get__(processor, type(processor))
        
        # Run in parallel
        results = await asyncio.gather(*[p.process() for p in processors])
        
        assert len(results) == 3
        assert all(r.success for r in results)
        
        # Check they ran concurrently (not sequentially)
        # If sequential, would take ~0.3s, if parallel ~0.1s
        total_time = max(end_times) - min(start_times)
        assert total_time < 0.2  # Allow some overhead
    
    @pytest.mark.asyncio
    async def test_validation_propagation(self, cache_manager):
        """Test validation errors propagate through pipeline."""
        # Create strict validation
        strict_validator = ValidationFramework()
        
        # Create processor with strict validation
        wfp_processor = WFPProcessor(
            source_config=SourceConfig(
                source_id="wfp",
                name="WFP",
                description="WFP processor",
                validation_level=ValidationLevel.STRICT
            ),
            cache_manager=cache_manager,
            validator=strict_validator
        )
        
        # Mock invalid data
        invalid_data = pd.DataFrame({
            'market': ['Sana\'a', None, 'Taiz'],  # Missing market
            'price': [1000, -500, 1200],  # Negative price
            'date': pd.to_datetime(['2023-01-01', '2023-01-02', 'invalid'])  # Invalid date
        })
        
        with patch.object(wfp_processor, 'download', return_value=invalid_data):
            result = await wfp_processor.process()
            
            # Should fail validation
            assert not result.success
            assert result.validation_report is not None
            assert len(result.validation_report.errors) > 0
    
    @pytest.mark.asyncio
    async def test_panel_builder_integration_all_sources(self, cache_manager, validation_framework):
        """Test panel builder integrates all data sources."""
        panel_builder = PanelBuilder(
            source_config=SourceConfig(
                source_id="panel",
                name="Panel Builder",
                description="Panel builder"
            ),
            cache_manager=cache_manager,
            validator=validation_framework
        )
        
        # Create mock data for each source
        price_data = pd.DataFrame({
            'market_id': ['M1', 'M2'],
            'date': pd.to_datetime(['2023-01-01', '2023-01-01']),
            'price_usd': [10.5, 12.3],
            'commodity': ['wheat', 'wheat']
        })
        
        climate_data = pd.DataFrame({
            'location': ['Sana\'a', 'Aden'],
            'date': pd.to_datetime(['2023-01-01', '2023-01-01']),
            'rainfall': [25.5, 10.2],
            'temperature': [22.3, 28.5]
        })
        
        conflict_data = pd.DataFrame({
            'location': ['Sana\'a', 'Aden'],
            'date': pd.to_datetime(['2023-01-01', '2023-01-01']),
            'events': [2, 0],
            'fatalities': [5, 0]
        })
        
        # Test integration
        with patch.multiple(
            panel_builder,
            _integrate_climate_data=Mock(return_value=None),
            _integrate_conflict_data=Mock(return_value=None),
            _integrate_aid_data=Mock(return_value=None)
        ):
            panel = await panel_builder.create_integrated_panel(
                price_observations=price_data,
                climate_data=climate_data,
                conflict_data=conflict_data,
                start_date=datetime(2023, 1, 1),
                end_date=datetime(2023, 1, 31)
            )
            
            # Verify integration methods were called
            panel_builder._integrate_climate_data.assert_called_once()
            panel_builder._integrate_conflict_data.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_progress_tracking_integration(self, cache_manager, validation_framework):
        """Test progress tracking across pipeline."""
        progress_tracker = PipelineProgressTracker(
            total_stages=5,
            stage_names=['Download', 'Validate', 'Transform', 'Aggregate', 'Save']
        )
        
        # Create processor with progress tracking
        climate_processor = ClimateProcessor(
            source_config=SourceConfig(
                source_id="climate",
                name="Climate",
                description="Climate processor"
            ),
            cache_manager=cache_manager,
            validator=validation_framework,
            progress_tracker=progress_tracker
        )
        
        # Track progress updates
        progress_updates = []
        
        def on_progress(stage, progress, message):
            progress_updates.append({
                'stage': stage,
                'progress': progress,
                'message': message
            })
        
        progress_tracker.on_progress = on_progress
        
        # Run processor (will use synthetic data)
        result = await climate_processor.process()
        
        # Check progress was tracked
        assert len(progress_updates) > 0
        assert any(u['stage'] == 'Download' for u in progress_updates)
        assert any(u['stage'] == 'Validate' for u in progress_updates)
    
    @pytest.mark.asyncio
    async def test_cache_effectiveness(self, cache_manager, validation_framework):
        """Test caching improves performance."""
        processor = PopulationProcessor(
            source_config=SourceConfig(
                source_id="population",
                name="Population",
                description="Population processor"
            ),
            cache_manager=cache_manager,
            validator=validation_framework
        )
        
        # First run - should cache
        start1 = asyncio.get_event_loop().time()
        result1 = await processor.process()
        time1 = asyncio.get_event_loop().time() - start1
        
        assert result1.success
        
        # Second run - should use cache
        start2 = asyncio.get_event_loop().time()
        result2 = await processor.process()
        time2 = asyncio.get_event_loop().time() - start2
        
        assert result2.success
        
        # Second run should be much faster
        assert time2 < time1 * 0.5  # At least 2x faster


@pytest.mark.asyncio
async def test_end_to_end_pipeline_scenario():
    """Test complete end-to-end pipeline scenario."""
    with tempfile.TemporaryDirectory() as tmpdir:
        # Setup
        cache_manager = CacheManager(cache_dir=Path(tmpdir))
        validator = ValidationFramework()
        
        # Create minimal pipeline config
        config = PipelineConfig(
            start_date=datetime(2023, 1, 1),
            end_date=datetime(2023, 3, 31),
            data_sources={
                'wfp': {'enabled': True},
                'climate': {'enabled': True},
                'conflict': {'enabled': True}
            },
            output_format='parquet',
            validation_level=ValidationLevel.STANDARD
        )
        
        # Create processors
        factory = ProcessorFactory(
            cache_manager=cache_manager,
            validator=validator
        )
        
        processors = {}
        for source in ['wfp', 'climate', 'conflict']:
            proc_config = ProcessorConfig(
                processor_type=ProcessorType.PRICE if source == 'wfp' else ProcessorType[source.upper()],
                source_config=SourceConfig(
                    source_id=source,
                    name=source.upper(),
                    description=f"{source} processor"
                )
            )
            processors[source] = factory.create_processor(proc_config)
        
        # Run processors
        results = {}
        for name, processor in processors.items():
            result = await processor.process()
            results[name] = result
            
            # Basic assertions
            assert result is not None
            if result.success:
                assert result.data is not None or result.errors is not None
        
        # Create panel if we have price data
        if results['wfp'].success and results['wfp'].data is not None:
            panel_builder = PanelBuilder(
                source_config=SourceConfig(
                    source_id="panel",
                    name="Panel Builder",
                    description="Panel builder"
                ),
                cache_manager=cache_manager,
                validator=validator
            )
            
            # Build panel
            panel = await panel_builder.create_integrated_panel(
                price_observations=results['wfp'].data,
                start_date=config.start_date,
                end_date=config.end_date
            )
            
            assert panel is not None
            
            # Save output
            output_path = Path(tmpdir) / "integrated_panel.parquet"
            if isinstance(panel, pd.DataFrame) and not panel.empty:
                panel.to_parquet(output_path)
                assert output_path.exists()