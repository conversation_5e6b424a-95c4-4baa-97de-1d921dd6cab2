"""Tests for monitoring infrastructure."""

import asyncio
import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, AsyncMock

from src.infrastructure.monitoring import (
    MetricsCollector, MetricType, get_metrics_collector,
    ResourceMonitor, 
    HealthChecker, HealthStatus, HealthCheckResult,
    AlertManager, AlertLevel, Alert,
    PrometheusExporter
)
from src.core.domain.pipeline.value_objects import ResourceMetrics


class TestMetricsCollector:
    """Test the metrics collection system."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.collector = MetricsCollector()
    
    def test_register_metric(self):
        """Test metric registration."""
        metric = self.collector.register_metric(
            "test_counter",
            MetricType.COUNTER,
            "Test counter metric"
        )
        
        assert metric.name == "test_counter"
        assert metric.metric_type == MetricType.COUNTER
        assert metric.description == "Test counter metric"
    
    def test_counter_increment(self):
        """Test counter incrementing."""
        self.collector.register_metric(
            "test_counter",
            MetricType.COUNTER,
            "Test counter"
        )
        
        # Initial value
        assert self.collector.get_metric("test_counter").get_current_value() is None
        
        # Increment
        self.collector.increment_counter("test_counter")
        assert self.collector.get_metric("test_counter").get_current_value() == 1.0
        
        # Increment with custom value
        self.collector.increment_counter("test_counter", 5.0)
        assert self.collector.get_metric("test_counter").get_current_value() == 6.0
    
    def test_counter_validation(self):
        """Test counter cannot decrease."""
        self.collector.register_metric(
            "test_counter",
            MetricType.COUNTER,
            "Test counter"
        )
        
        self.collector.record_value("test_counter", 10.0)
        # Try to decrease - should be rejected
        self.collector.record_value("test_counter", 5.0)
        
        assert self.collector.get_metric("test_counter").get_current_value() == 10.0
    
    def test_gauge_metric(self):
        """Test gauge metrics."""
        self.collector.register_metric(
            "test_gauge",
            MetricType.GAUGE,
            "Test gauge"
        )
        
        # Gauge can go up and down
        self.collector.set_gauge("test_gauge", 50.0)
        assert self.collector.get_metric("test_gauge").get_current_value() == 50.0
        
        self.collector.set_gauge("test_gauge", 30.0)
        assert self.collector.get_metric("test_gauge").get_current_value() == 30.0
    
    def test_histogram_metric(self):
        """Test histogram metrics."""
        self.collector.register_metric(
            "test_histogram",
            MetricType.HISTOGRAM,
            "Test histogram"
        )
        
        # Observe values
        values = [0.1, 0.5, 1.0, 2.0, 5.0]
        for v in values:
            self.collector.observe_histogram("test_histogram", v)
        
        # Check statistics
        stats = self.collector.get_metric("test_histogram").get_statistics()
        assert stats["count"] == 5
        assert stats["mean"] == pytest.approx(1.72, rel=0.01)
        assert stats["min"] == 0.1
        assert stats["max"] == 5.0
    
    def test_timer_context_manager(self):
        """Test timer context manager."""
        self.collector.register_metric(
            "test_timer",
            MetricType.HISTOGRAM,
            "Test timer"
        )
        
        with self.collector.timer("test_timer"):
            # Simulate some work
            import time
            time.sleep(0.1)
        
        metric = self.collector.get_metric("test_timer")
        assert metric.get_current_value() > 0.09  # Should be at least 0.1s
    
    def test_metric_labels(self):
        """Test metrics with labels."""
        self.collector.register_metric(
            "test_labeled",
            MetricType.COUNTER,
            "Test with labels",
            labels=["source", "status"]
        )
        
        # Record with different labels
        self.collector.increment_counter(
            "test_labeled",
            labels={"source": "wfp", "status": "success"}
        )
        self.collector.increment_counter(
            "test_labeled",
            labels={"source": "acled", "status": "success"}
        )
        
        metric = self.collector.get_metric("test_labeled")
        assert len(metric.values) == 2
    
    def test_metric_summary(self):
        """Test metric summary generation."""
        self.collector.register_metric(
            "test_metric",
            MetricType.GAUGE,
            "Test metric"
        )
        
        for i in range(10):
            self.collector.set_gauge("test_metric", float(i))
        
        summary = self.collector.get_metric_summary("test_metric")
        assert summary["name"] == "test_metric"
        assert summary["type"] == "gauge"
        assert summary["current_value"] == 9.0
        assert "statistics" in summary
    
    def test_global_collector(self):
        """Test global metrics collector singleton."""
        collector1 = get_metrics_collector()
        collector2 = get_metrics_collector()
        
        assert collector1 is collector2


class TestResourceMonitor:
    """Test resource monitoring."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.monitor = ResourceMonitor(check_interval_seconds=1)
    
    def test_collect_metrics(self):
        """Test resource metrics collection."""
        metrics = self.monitor._collect_metrics()
        
        assert isinstance(metrics, ResourceMetrics)
        assert metrics.cpu_percent >= 0
        assert metrics.memory_mb > 0
        assert metrics.memory_percent >= 0
        assert metrics.disk_io_mb >= 0
        assert metrics.network_io_mb >= 0
    
    @patch('psutil.Process')
    def test_monitoring_lifecycle(self, mock_process):
        """Test start/stop monitoring."""
        # Mock process metrics
        mock_process.return_value.cpu_percent.return_value = 50.0
        mock_process.return_value.memory_info.return_value.rss = 1024 * 1024 * 1024
        mock_process.return_value.memory_percent.return_value = 25.0
        
        self.monitor.start_monitoring()
        assert self.monitor._monitoring is True
        
        # Let it collect some metrics
        import time
        time.sleep(0.1)
        
        self.monitor.stop_monitoring()
        assert self.monitor._monitoring is False
    
    def test_callback_system(self):
        """Test resource monitoring callbacks."""
        callback_called = False
        callback_metrics = None
        
        def test_callback(metrics):
            nonlocal callback_called, callback_metrics
            callback_called = True
            callback_metrics = metrics
        
        self.monitor.add_callback(test_callback)
        
        # Manually trigger a collection
        metrics = self.monitor._collect_metrics()
        for callback in self.monitor._callbacks:
            callback(metrics)
        
        assert callback_called
        assert callback_metrics == metrics
    
    def test_average_metrics(self):
        """Test average metrics calculation."""
        # Add some metrics to history
        for i in range(5):
            metrics = ResourceMetrics(
                cpu_percent=float(i * 10),
                memory_mb=float(1000 + i * 100),
                memory_percent=float(20 + i * 5),
                disk_io_mb=float(i * 50),
                network_io_mb=float(i * 25),
                timestamp=datetime.utcnow()
            )
            self.monitor.metrics_history.append(metrics)
        
        avg = self.monitor.get_average_metrics(window_seconds=3600)
        assert avg.cpu_percent == 20.0  # (0+10+20+30+40)/5
        assert avg.memory_mb == 1200.0   # (1000+1100+1200+1300+1400)/5
    
    def test_peak_metrics(self):
        """Test peak metrics calculation."""
        # Add some metrics to history
        for i in range(5):
            metrics = ResourceMetrics(
                cpu_percent=float(i * 10),
                memory_mb=float(1000 + i * 100),
                memory_percent=float(20 + i * 5),
                disk_io_mb=float(i * 50),
                network_io_mb=float(i * 25),
                timestamp=datetime.utcnow()
            )
            self.monitor.metrics_history.append(metrics)
        
        peak = self.monitor.get_peak_metrics()
        assert peak.cpu_percent == 40.0
        assert peak.memory_mb == 1400.0
    
    def test_resource_limits_check(self):
        """Test resource limit checking."""
        metrics = ResourceMetrics(
            cpu_percent=85.0,
            memory_mb=9000.0,
            memory_percent=70.0,
            disk_io_mb=100.0,
            network_io_mb=50.0,
            timestamp=datetime.utcnow()
        )
        self.monitor.metrics_history.append(metrics)
        
        limits = self.monitor.check_resource_limits(
            max_cpu_percent=90,
            max_memory_mb=8192
        )
        
        assert limits["cpu_ok"] is True
        assert limits["memory_ok"] is False
        assert limits["within_limits"] is False
    
    @pytest.mark.asyncio
    async def test_wait_for_resources(self):
        """Test waiting for resources."""
        # Add high resource usage
        high_metrics = ResourceMetrics(
            cpu_percent=95.0,
            memory_mb=9000.0,
            memory_percent=90.0,
            disk_io_mb=100.0,
            network_io_mb=50.0,
            timestamp=datetime.utcnow()
        )
        self.monitor.metrics_history.append(high_metrics)
        
        # Should timeout quickly
        result = await self.monitor.wait_for_resources(
            max_cpu_percent=80,
            max_memory_mb=8192,
            timeout_seconds=1
        )
        
        assert result is False


class TestHealthChecker:
    """Test health checking system."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.checker = HealthChecker(check_interval_seconds=1)
    
    @pytest.mark.asyncio
    async def test_component_registration(self):
        """Test health check component registration."""
        async def test_check():
            return HealthCheckResult(
                component="test",
                status=HealthStatus.HEALTHY,
                message="Test is healthy"
            )
        
        self.checker.register_component(
            "test",
            test_check,
            timeout_seconds=5
        )
        
        assert "test" in self.checker.components
        assert self.checker.components["test"].name == "test"
    
    @pytest.mark.asyncio
    async def test_health_check_execution(self):
        """Test health check execution."""
        check_called = False
        
        async def test_check():
            nonlocal check_called
            check_called = True
            return HealthCheckResult(
                component="test",
                status=HealthStatus.HEALTHY,
                message="All good"
            )
        
        self.checker.register_component("test", test_check)
        result = await self.checker.check_component("test")
        
        assert check_called
        assert result.status == HealthStatus.HEALTHY
        assert result.message == "All good"
        assert result.response_time_ms > 0
    
    @pytest.mark.asyncio
    async def test_health_check_timeout(self):
        """Test health check timeout handling."""
        async def slow_check():
            await asyncio.sleep(10)  # Longer than timeout
            return HealthCheckResult(
                component="slow",
                status=HealthStatus.HEALTHY
            )
        
        self.checker.register_component(
            "slow",
            slow_check,
            timeout_seconds=1
        )
        
        result = await self.checker.check_component("slow")
        assert result.status == HealthStatus.UNHEALTHY
        assert "timeout" in result.message.lower()
    
    @pytest.mark.asyncio
    async def test_health_check_failure(self):
        """Test health check failure handling."""
        async def failing_check():
            raise Exception("Test failure")
        
        self.checker.register_component("failing", failing_check)
        result = await self.checker.check_component("failing")
        
        assert result.status == HealthStatus.UNHEALTHY
        assert "Test failure" in result.message
    
    @pytest.mark.asyncio
    async def test_consecutive_failures(self):
        """Test consecutive failure tracking."""
        fail_count = 0
        
        async def flaky_check():
            nonlocal fail_count
            fail_count += 1
            if fail_count < 3:
                raise Exception("Still failing")
            return HealthCheckResult(
                component="flaky",
                status=HealthStatus.HEALTHY
            )
        
        self.checker.register_component(
            "flaky",
            flaky_check,
            failure_threshold=3
        )
        
        # First two checks fail
        for _ in range(2):
            await self.checker.check_component("flaky")
        
        component = self.checker.components["flaky"]
        assert component.consecutive_failures == 2
        assert not component.is_critical
        
        # Third check fails - now critical
        await self.checker.check_component("flaky")
        assert component.is_critical
        
        # Fourth check succeeds - resets counter
        await self.checker.check_component("flaky")
        assert component.consecutive_failures == 0
    
    @pytest.mark.asyncio
    async def test_overall_status(self):
        """Test overall health status calculation."""
        # All healthy
        async def healthy_check():
            return HealthCheckResult(
                component="healthy",
                status=HealthStatus.HEALTHY
            )
        
        self.checker.register_component("healthy1", healthy_check)
        self.checker.register_component("healthy2", healthy_check)
        
        await self.checker.check_all_components()
        assert self.checker.get_overall_status() == HealthStatus.HEALTHY
        
        # Add unhealthy component
        async def unhealthy_check():
            return HealthCheckResult(
                component="unhealthy",
                status=HealthStatus.UNHEALTHY
            )
        
        self.checker.register_component("unhealthy", unhealthy_check)
        await self.checker.check_all_components()
        assert self.checker.get_overall_status() == HealthStatus.UNHEALTHY
    
    @pytest.mark.asyncio
    async def test_default_health_checks(self):
        """Test default health checks."""
        # Test data directory check
        result = await self.checker._check_data_directory()
        # May be healthy or unhealthy depending on test environment
        assert result.component == "data_directory"
        
        # Test disk space check
        result = await self.checker._check_disk_space()
        assert result.component == "disk_space"
        assert "free" in result.details
        
        # Test memory check
        result = await self.checker._check_memory()
        assert result.component == "memory"
        assert "percent_used" in result.details


class TestAlertManager:
    """Test alert management system."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.manager = AlertManager(check_interval_seconds=1)
    
    def test_alert_rule_creation(self):
        """Test alert rule creation."""
        from src.infrastructure.monitoring.alert_manager import AlertRule
        
        rule = AlertRule(
            name="test_rule",
            condition=lambda: True,
            level=AlertLevel.WARNING,
            message_template="Test alert: {value}",
            cooldown_minutes=5
        )
        
        self.manager.add_rule(rule)
        assert "test_rule" in self.manager.rules
    
    def test_alert_triggering(self):
        """Test alert triggering."""
        from src.infrastructure.monitoring.alert_manager import AlertRule
        
        triggered = False
        
        def condition():
            return triggered
        
        rule = AlertRule(
            name="test",
            condition=condition,
            level=AlertLevel.WARNING,
            message_template="Test triggered"
        )
        
        self.manager.add_rule(rule)
        
        # Should not trigger
        asyncio.run(self.manager.check_all_rules())
        assert len(self.manager.alerts) == 0
        
        # Now trigger
        triggered = True
        asyncio.run(self.manager.check_all_rules())
        assert len(self.manager.alerts) == 1
        assert self.manager.alerts[0].message == "Test triggered"
    
    def test_alert_cooldown(self):
        """Test alert cooldown mechanism."""
        from src.infrastructure.monitoring.alert_manager import AlertRule
        
        rule = AlertRule(
            name="cooldown_test",
            condition=lambda: True,  # Always triggers
            level=AlertLevel.INFO,
            message_template="Test",
            cooldown_minutes=60
        )
        
        self.manager.add_rule(rule)
        
        # First trigger
        asyncio.run(self.manager.check_all_rules())
        assert len(self.manager.alerts) == 1
        
        # Second attempt - should be blocked by cooldown
        asyncio.run(self.manager.check_all_rules())
        assert len(self.manager.alerts) == 1  # No new alert
    
    def test_alert_rate_limiting(self):
        """Test alert rate limiting."""
        from src.infrastructure.monitoring.alert_manager import AlertRule
        
        rule = AlertRule(
            name="rate_test",
            condition=lambda: True,
            level=AlertLevel.INFO,
            message_template="Test",
            cooldown_minutes=0,  # No cooldown
            max_alerts_per_hour=2
        )
        
        self.manager.add_rule(rule)
        
        # Trigger multiple times
        for _ in range(5):
            rule.last_triggered = None  # Reset cooldown
            asyncio.run(self.manager.check_all_rules())
        
        # Should only have 2 alerts due to rate limit
        assert len(self.manager.alerts) == 2
    
    def test_alert_handlers(self):
        """Test alert handlers."""
        handled_alerts = []
        
        def test_handler(alert):
            handled_alerts.append(alert)
        
        self.manager.add_handler(AlertLevel.WARNING, test_handler)
        
        # Create and trigger alert
        alert = Alert(
            alert_id="test_1",
            rule_name="test",
            level=AlertLevel.WARNING,
            message="Test alert"
        )
        
        asyncio.run(self.manager.trigger_alert(alert))
        
        assert len(handled_alerts) == 1
        assert handled_alerts[0] == alert
    
    def test_alert_acknowledgment(self):
        """Test alert acknowledgment."""
        alert = Alert(
            alert_id="ack_test",
            rule_name="test",
            level=AlertLevel.ERROR,
            message="Test"
        )
        
        self.manager.alerts.append(alert)
        
        # Acknowledge
        success = self.manager.acknowledge_alert("ack_test", "test_user")
        assert success
        assert alert.acknowledged
        assert alert.acknowledged_by == "test_user"
        assert alert.acknowledged_at is not None
    
    def test_active_alerts(self):
        """Test getting active alerts."""
        # Add some alerts
        old_alert = Alert(
            alert_id="old",
            rule_name="test",
            level=AlertLevel.INFO,
            message="Old alert",
            timestamp=datetime.utcnow() - timedelta(hours=2)
        )
        
        recent_alert = Alert(
            alert_id="recent",
            rule_name="test",
            level=AlertLevel.WARNING,
            message="Recent alert"
        )
        
        acked_alert = Alert(
            alert_id="acked",
            rule_name="test",
            level=AlertLevel.ERROR,
            message="Acknowledged alert"
        )
        acked_alert.acknowledge()
        
        self.manager.alerts.extend([old_alert, recent_alert, acked_alert])
        
        # Get active alerts from last hour
        active = self.manager.get_active_alerts(since_minutes=60)
        assert len(active) == 1
        assert active[0].alert_id == "recent"
    
    def test_alert_summary(self):
        """Test alert summary generation."""
        # Add various alerts
        for i in range(10):
            alert = Alert(
                alert_id=f"alert_{i}",
                rule_name="test_rule",
                level=AlertLevel.WARNING if i % 2 == 0 else AlertLevel.INFO,
                message=f"Alert {i}"
            )
            self.manager.alerts.append(alert)
        
        summary = self.manager.get_alert_summary()
        assert summary["total_alerts"] == 10
        assert summary["by_level"]["warning"] == 5
        assert summary["by_level"]["info"] == 5


class TestPrometheusExporter:
    """Test Prometheus metrics export."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.collector = MetricsCollector()
        self.exporter = PrometheusExporter(self.collector)
    
    def test_export_counter(self):
        """Test exporting counter metrics."""
        self.collector.register_metric(
            "test_counter",
            MetricType.COUNTER,
            "Test counter"
        )
        self.collector.increment_counter("test_counter", 42)
        
        output = self.exporter.export_metrics()
        
        assert "# HELP test_counter Test counter" in output
        assert "# TYPE test_counter counter" in output
        assert "test_counter 42" in output
    
    def test_export_gauge(self):
        """Test exporting gauge metrics."""
        self.collector.register_metric(
            "test_gauge",
            MetricType.GAUGE,
            "Test gauge"
        )
        self.collector.set_gauge("test_gauge", 75.5)
        
        output = self.exporter.export_metrics()
        
        assert "# TYPE test_gauge gauge" in output
        assert "test_gauge 75.5" in output
    
    def test_export_histogram(self):
        """Test exporting histogram metrics."""
        self.collector.register_metric(
            "test_histogram",
            MetricType.HISTOGRAM,
            "Test histogram"
        )
        
        # Add some observations
        for value in [0.05, 0.2, 0.8, 1.5, 3.0, 12.0]:
            self.collector.observe_histogram("test_histogram", value)
        
        output = self.exporter.export_metrics()
        
        assert "# TYPE test_histogram histogram" in output
        assert 'test_histogram_bucket{le="0.1"}' in output
        assert 'test_histogram_bucket{le="+Inf"}' in output
        assert "test_histogram_sum" in output
        assert "test_histogram_count 6" in output
    
    def test_export_with_labels(self):
        """Test exporting metrics with labels."""
        self.collector.register_metric(
            "test_labeled",
            MetricType.COUNTER,
            "Test with labels",
            labels=["source", "status"]
        )
        
        self.collector.increment_counter(
            "test_labeled",
            labels={"source": "wfp", "status": "success"}
        )
        
        output = self.exporter.export_metrics()
        assert 'test_labeled{source="wfp",status="success"}' in output
    
    def test_export_to_file(self, tmp_path):
        """Test exporting metrics to file."""
        self.collector.register_metric(
            "test_metric",
            MetricType.GAUGE,
            "Test metric"
        )
        self.collector.set_gauge("test_metric", 100)
        
        output_file = tmp_path / "metrics.txt"
        self.exporter.export_to_file(str(output_file))
        
        assert output_file.exists()
        content = output_file.read_text()
        assert "test_metric 100" in content
    
    def test_json_export(self):
        """Test JSON format export."""
        self.collector.register_metric(
            "test_metric",
            MetricType.GAUGE,
            "Test metric",
            unit="percent"
        )
        self.collector.set_gauge("test_metric", 85.5)
        
        json_output = self.exporter.export_json()
        import json
        data = json.loads(json_output)
        
        assert "metrics" in data
        assert "test_metric" in data["metrics"]
        assert data["metrics"]["test_metric"]["current_value"] == 85.5
        assert data["metrics"]["test_metric"]["unit"] == "percent"
    
    def test_grafana_dashboard(self):
        """Test Grafana dashboard generation."""
        dashboard = self.exporter.create_grafana_dashboard()
        
        assert "dashboard" in dashboard
        assert dashboard["dashboard"]["title"] == "Yemen Market Integration Pipeline"
        assert len(dashboard["dashboard"]["panels"]) >= 6