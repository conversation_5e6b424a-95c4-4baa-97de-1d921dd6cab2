"""Tests for retry strategies."""

import asyncio
import pytest
from datetime import datetime
from unittest.mock import Mock, AsyncMock, patch

from src.infrastructure.resilience.retry_strategies import (
    RetryConfig,
    RetryError,
    RetryManager,
    ExponentialBackoffStrategy,
    LinearBackoffStrategy,
    FixedDelayStrategy,
    with_retry,
    AGGRESSIVE_RETRY,
    GENTLE_RETRY
)


class TestRetryStrategies:
    """Test different retry strategies."""
    
    @pytest.fixture
    def exponential_config(self):
        """Create exponential backoff config."""
        return RetryConfig(
            max_attempts=3,
            initial_delay=0.1,
            max_delay=1.0,
            exponential_base=2.0,
            jitter=False  # Disable for predictable tests
        )
        
    @pytest.fixture
    def linear_config(self):
        """Create linear backoff config."""
        return RetryConfig(
            max_attempts=3,
            initial_delay=0.1,
            jitter=False
        )
        
    def test_exponential_backoff_calculation(self, exponential_config):
        """Test exponential backoff delay calculation."""
        strategy = ExponentialBackoffStrategy(exponential_config)
        
        # Test delays: 0.1, 0.2, 0.4
        assert strategy.calculate_delay(1) == 0.1
        assert strategy.calculate_delay(2) == 0.2
        assert strategy.calculate_delay(3) == 0.4
        
    def test_exponential_backoff_max_delay(self):
        """Test exponential backoff respects max delay."""
        config = RetryConfig(
            initial_delay=1.0,
            max_delay=5.0,
            exponential_base=10.0,
            jitter=False
        )
        strategy = ExponentialBackoffStrategy(config)
        
        # Would be 1000 but capped at 5
        assert strategy.calculate_delay(4) == 5.0
        
    def test_exponential_backoff_with_jitter(self):
        """Test exponential backoff with jitter."""
        config = RetryConfig(
            initial_delay=1.0,
            jitter=True,
            jitter_range=(0.5, 1.5)
        )
        strategy = ExponentialBackoffStrategy(config)
        
        # With jitter, delay should be between 0.5 and 1.5
        delay = strategy.calculate_delay(1)
        assert 0.5 <= delay <= 1.5
        
    def test_linear_backoff_calculation(self, linear_config):
        """Test linear backoff delay calculation."""
        strategy = LinearBackoffStrategy(linear_config)
        
        # Test delays: 0.1, 0.2, 0.3
        assert strategy.calculate_delay(1) == 0.1
        assert strategy.calculate_delay(2) == 0.2
        assert strategy.calculate_delay(3) == 0.3
        
    def test_fixed_delay_calculation(self):
        """Test fixed delay calculation."""
        config = RetryConfig(initial_delay=0.5, jitter=False)
        strategy = FixedDelayStrategy(config)
        
        # Always returns same delay
        assert strategy.calculate_delay(1) == 0.5
        assert strategy.calculate_delay(5) == 0.5
        assert strategy.calculate_delay(10) == 0.5
        
    def test_should_retry_logic(self):
        """Test retry decision logic."""
        config = RetryConfig(
            retryable_exceptions={ValueError, TypeError},
            non_retryable_exceptions={RuntimeError}
        )
        strategy = ExponentialBackoffStrategy(config)
        
        # Retryable
        assert strategy.should_retry(ValueError("test"))
        assert strategy.should_retry(TypeError("test"))
        
        # Non-retryable
        assert not strategy.should_retry(RuntimeError("test"))
        assert not strategy.should_retry(KeyError("test"))  # Not in retryable list


class TestRetryManager:
    """Test retry manager functionality."""
    
    @pytest.fixture
    def retry_manager(self):
        """Create retry manager for testing."""
        config = RetryConfig(
            max_attempts=3,
            initial_delay=0.01,  # Fast for tests
            jitter=False
        )
        strategy = ExponentialBackoffStrategy(config)
        return RetryManager(strategy)
        
    @pytest.fixture
    def failing_function(self):
        """Create a function that fails N times."""
        call_count = 0
        
        async def fail_n_times(n=2):
            nonlocal call_count
            call_count += 1
            if call_count <= n:
                raise ValueError(f"Failure {call_count}")
            return f"Success after {call_count} attempts"
            
        fail_n_times.call_count = lambda: call_count
        fail_n_times.reset = lambda: setattr(fail_n_times, 'call_count', 0)
        return fail_n_times
        
    @pytest.mark.asyncio
    async def test_successful_first_attempt(self, retry_manager):
        """Test function succeeds on first attempt."""
        async def success():
            return "success"
            
        result = await retry_manager.execute_with_retry(success)
        assert result == "success"
        
        stats = retry_manager.get_stats()
        assert stats["success_success"] == 1
        
    @pytest.mark.asyncio
    async def test_retry_then_success(self, retry_manager, failing_function):
        """Test function fails then succeeds."""
        result = await retry_manager.execute_with_retry(
            failing_function,
            2  # Fail 2 times
        )
        
        assert "Success after 3 attempts" in result
        assert failing_function.call_count() == 3
        
        stats = retry_manager.get_stats()
        assert stats["fail_n_times_retry"] == 2
        assert stats["fail_n_times_success"] == 1
        
    @pytest.mark.asyncio
    async def test_max_attempts_exhausted(self, retry_manager):
        """Test retry fails after max attempts."""
        async def always_fail():
            raise ValueError("Always fails")
            
        with pytest.raises(RetryError) as exc_info:
            await retry_manager.execute_with_retry(always_fail)
            
        assert "failed after 3 attempts" in str(exc_info.value)
        assert len(exc_info.value.attempts) == 3
        
        stats = retry_manager.get_stats()
        assert stats["always_fail_exhausted"] == 1
        
    @pytest.mark.asyncio
    async def test_non_retryable_exception(self, retry_manager):
        """Test non-retryable exceptions fail immediately."""
        retry_manager.strategy.config.retryable_exceptions = {ValueError}
        retry_manager.strategy.config.non_retryable_exceptions = {RuntimeError}
        
        async def fail_non_retryable():
            raise RuntimeError("Non-retryable")
            
        with pytest.raises(RuntimeError):
            await retry_manager.execute_with_retry(fail_non_retryable)
            
        # Should not retry
        stats = retry_manager.get_stats()
        assert "fail_non_retryable_retry" not in stats
        
    @pytest.mark.asyncio
    async def test_retry_callback(self, retry_manager):
        """Test retry callback is called."""
        callbacks = []
        
        def on_retry(attempt):
            callbacks.append(attempt)
            
        retry_manager.on_retry = on_retry
        
        async def fail_twice():
            if len(callbacks) < 2:
                raise ValueError("Fail")
            return "success"
            
        result = await retry_manager.execute_with_retry(fail_twice)
        assert result == "success"
        assert len(callbacks) == 2
        assert callbacks[0].attempt_number == 1
        assert callbacks[1].attempt_number == 2
        
    @pytest.mark.asyncio
    async def test_sync_function_retry(self, retry_manager):
        """Test retrying synchronous functions."""
        call_count = 0
        
        def sync_fail_once():
            nonlocal call_count
            call_count += 1
            if call_count == 1:
                raise ValueError("First call fails")
            return "sync success"
            
        result = await retry_manager.execute_with_retry(sync_fail_once)
        assert result == "sync success"
        assert call_count == 2
        
    @pytest.mark.asyncio
    @patch('asyncio.sleep')
    async def test_retry_delays(self, mock_sleep, retry_manager):
        """Test retry delays are applied."""
        async def fail_always():
            raise ValueError("Fail")
            
        with pytest.raises(RetryError):
            await retry_manager.execute_with_retry(fail_always)
            
        # Check sleep was called with correct delays
        assert mock_sleep.call_count == 2  # 2 retries
        delays = [call[0][0] for call in mock_sleep.call_args_list]
        assert delays[0] == 0.01  # First retry
        assert delays[1] == 0.02  # Second retry (exponential)


class TestRetryDecorator:
    """Test retry decorator functionality."""
    
    @pytest.mark.asyncio
    async def test_async_decorator(self):
        """Test decorator with async function."""
        call_count = 0
        
        @with_retry(strategy=GENTLE_RETRY)
        async def decorated_async():
            nonlocal call_count
            call_count += 1
            if call_count < 2:
                raise ValueError("Fail once")
            return "async success"
            
        result = await decorated_async()
        assert result == "async success"
        assert call_count == 2
        
    def test_sync_decorator(self):
        """Test decorator with sync function."""
        call_count = 0
        
        @with_retry(
            strategy=FixedDelayStrategy(
                RetryConfig(max_attempts=2, initial_delay=0.01)
            )
        )
        def decorated_sync():
            nonlocal call_count
            call_count += 1
            if call_count < 2:
                raise ValueError("Fail once")
            return "sync success"
            
        result = decorated_sync()
        assert result == "sync success"
        assert call_count == 2
        
    @pytest.mark.asyncio
    async def test_decorator_with_operation_name(self):
        """Test decorator with custom operation name."""
        @with_retry(operation_name="custom_operation")
        async def some_function():
            return "success"
            
        result = await some_function()
        assert result == "success"


class TestPreConfiguredStrategies:
    """Test pre-configured retry strategies."""
    
    def test_aggressive_retry(self):
        """Test aggressive retry configuration."""
        assert AGGRESSIVE_RETRY.config.max_attempts == 10
        assert AGGRESSIVE_RETRY.config.initial_delay == 0.5
        assert AGGRESSIVE_RETRY.config.max_delay == 300
        
    def test_gentle_retry(self):
        """Test gentle retry configuration."""
        assert GENTLE_RETRY.config.max_attempts == 3
        assert GENTLE_RETRY.config.initial_delay == 2.0
        assert GENTLE_RETRY.config.max_delay == 10