"""Tests for resilient processor wrapper."""

import asyncio
import pytest
from datetime import datetime
from pathlib import Path
import tempfile
import shutil
from unittest.mock import Mock, AsyncMock, patch
import pandas as pd

from src.infrastructure.processors.resilient_processor_wrapper import (
    ResilientProcessorWrapper,
    ResilientProcessorFactory
)
from src.infrastructure.processors.base_processor import BaseProcessor
from src.infrastructure.resilience import CircuitOpenError
from src.infrastructure.queues import Dead<PERSON>etterQueue, RecordStatus


class MockProcessor(BaseProcessor):
    """Mock processor for testing."""
    
    def __init__(self, name: str = "MockProcessor"):
        self.name = name
        self.process_count = 0
        self.should_fail = False
        self.fail_count = 0
        self.fail_times = 0
        
    async def process(self, data: dict) -> dict:
        """Process data."""
        self.process_count += 1
        
        if self.should_fail:
            self.fail_count += 1
            if self.fail_times == 0 or self.fail_count <= self.fail_times:
                raise Exception(f"Process failed: attempt {self.fail_count}")
                
        return {"processed": data, "count": self.process_count}
        
    def get_empty_result(self) -> dict:
        """Get empty result."""
        return {"processed": None, "count": 0}
        
    async def health_check(self) -> dict:
        """Health check."""
        return {"healthy": not self.should_fail}


class TestResilientProcessorWrapper:
    """Test resilient processor wrapper."""
    
    @pytest.fixture
    async def dlq(self):
        """Create DLQ for testing."""
        temp_dir = tempfile.mkdtemp()
        dlq = DeadLetterQueue(storage_path=Path(temp_dir))
        await dlq.start()
        yield dlq
        await dlq.stop()
        shutil.rmtree(temp_dir)
        
    @pytest.fixture
    def mock_processor(self):
        """Create mock processor."""
        return MockProcessor()
        
    @pytest.fixture
    def wrapper(self, mock_processor, dlq):
        """Create resilient wrapper."""
        return ResilientProcessorWrapper(
            processor=mock_processor,
            dlq=dlq
        )
        
    @pytest.mark.asyncio
    async def test_successful_processing(self, wrapper, mock_processor):
        """Test successful processing."""
        result = await wrapper.process({"test": "data"})
        
        assert result["processed"]["test"] == "data"
        assert result["count"] == 1
        assert mock_processor.process_count == 1
        
        stats = wrapper.get_stats()
        assert stats["processing"]["total_processed"] == 1
        assert stats["processing"]["total_failed"] == 0
        
    @pytest.mark.asyncio
    async def test_retry_then_success(self, wrapper, mock_processor):
        """Test retry mechanism works."""
        # Fail first 2 times
        mock_processor.should_fail = True
        mock_processor.fail_times = 2
        
        result = await wrapper.process({"test": "data"})
        
        # Should succeed after retries
        assert result["processed"]["test"] == "data"
        assert mock_processor.process_count == 3  # 2 failures + 1 success
        
        stats = wrapper.get_stats()
        assert stats["processing"]["total_processed"] == 1
        
    @pytest.mark.asyncio
    async def test_circuit_breaker_opens(self, wrapper, mock_processor):
        """Test circuit breaker opens after failures."""
        mock_processor.should_fail = True
        
        # Fail multiple times to open circuit
        for i in range(5):
            result = await wrapper.process({"test": f"data{i}"})
            assert result == {"processed": None, "count": 0}  # Empty result
            
        # Circuit should be open
        assert wrapper.circuit_breaker.is_open
        
        # Next call should fail fast
        with patch.object(mock_processor, 'process') as mock_process:
            result = await wrapper.process({"test": "data"})
            mock_process.assert_not_called()  # Circuit open, no call made
            
        stats = wrapper.get_stats()
        assert stats["processing"]["circuit_opens"] > 0
        
    @pytest.mark.asyncio
    async def test_dlq_integration(self, wrapper, mock_processor, dlq):
        """Test failed records go to DLQ."""
        mock_processor.should_fail = True
        
        result = await wrapper.process({"test": "data"})
        
        # Should return empty result
        assert result == {"processed": None, "count": 0}
        
        # Check DLQ
        stats = wrapper.get_stats()
        assert stats["processing"]["dlq_sent"] == 1
        
        dlq_stats = dlq.get_stats()
        assert dlq_stats["queue_size"] == 1
        
        # Verify DLQ record
        records = await dlq.get_records()
        assert len(records) == 1
        assert records[0].source == "MockProcessor"
        
    @pytest.mark.asyncio
    async def test_batch_processing(self, wrapper, mock_processor):
        """Test batch processing with partial failures."""
        # Make some items fail
        items = [{"id": i} for i in range(10)]
        
        # Fail on specific IDs
        original_process = mock_processor.process
        
        async def selective_fail(data):
            if isinstance(data, list):
                # Batch processing
                results = []
                for item in data:
                    if item["id"] % 3 == 0:  # Fail every 3rd item
                        raise Exception(f"Failed on {item['id']}")
                    results.append({"processed": item})
                return results
            else:
                # Single item
                if data["id"] % 3 == 0:
                    raise Exception(f"Failed on {data['id']}")
                return await original_process(data)
                
        mock_processor.process = selective_fail
        
        result = await wrapper.process_batch(items, batch_size=5)
        
        # Check results
        assert len(result["successful"]) == 7  # Items 1,2,4,5,7,8,10
        assert len(result["failed"]) == 3      # Items 0,3,6,9
        assert result["success_rate"] == 0.7
        
    @pytest.mark.asyncio
    async def test_empty_result_types(self, wrapper):
        """Test empty result generation for different types."""
        # Test DataFrame processor
        wrapper.processor_name = "DataFrameProcessor"
        result = wrapper._get_empty_result()
        assert isinstance(result, pd.DataFrame)
        assert result.empty
        
        # Test list processor
        wrapper.processor_name = "ListProcessor"
        wrapper.processor.process.__annotations__['return'] = 'List[Dict]'
        result = wrapper._get_empty_result()
        assert result == []
        
        # Test dict processor
        wrapper.processor_name = "DictProcessor"
        wrapper.processor.process.__annotations__['return'] = 'Dict[str, Any]'
        result = wrapper._get_empty_result()
        assert result == {}
        
    @pytest.mark.asyncio
    async def test_health_check(self, wrapper, mock_processor):
        """Test health check integration."""
        # Healthy processor
        health = await wrapper.health_check()
        assert health["processor"] == "MockProcessor"
        assert health["circuit_state"] == "closed"
        assert health["is_healthy"] is True
        assert health["processor_health"]["healthy"] is True
        
        # Unhealthy processor
        mock_processor.should_fail = True
        health = await wrapper.health_check()
        assert health["processor_health"]["healthy"] is False
        
    @pytest.mark.asyncio
    async def test_stats_collection(self, wrapper, mock_processor):
        """Test statistics collection."""
        # Process some successful items
        for i in range(3):
            await wrapper.process({"id": i})
            
        # Process some failures
        mock_processor.should_fail = True
        for i in range(2):
            await wrapper.process({"id": i + 3})
            
        stats = wrapper.get_stats()
        
        assert stats["processor"] == "MockProcessor"
        assert stats["processing"]["total_processed"] == 3
        assert stats["processing"]["total_failed"] == 2
        assert stats["processing"]["dlq_sent"] == 2
        
        # Check circuit breaker stats
        assert "circuit_breaker" in stats
        assert stats["circuit_breaker"]["total_calls"] == 5
        
        # Check retry stats
        assert "retry" in stats


class TestResilientProcessorFactory:
    """Test resilient processor factory."""
    
    @pytest.fixture
    async def dlq(self):
        """Create DLQ for testing."""
        temp_dir = tempfile.mkdtemp()
        dlq = DeadLetterQueue(storage_path=Path(temp_dir))
        await dlq.start()
        yield dlq
        await dlq.stop()
        shutil.rmtree(temp_dir)
        
    @pytest.fixture
    def factory(self, dlq):
        """Create factory."""
        return ResilientProcessorFactory(dlq)
        
    def test_wrap_processor(self, factory):
        """Test wrapping processors."""
        processor1 = MockProcessor("Processor1")
        processor2 = MockProcessor("Processor2")
        
        wrapper1 = factory.wrap_processor(processor1)
        wrapper2 = factory.wrap_processor(processor2)
        
        assert wrapper1.processor_name == "MockProcessor"
        assert wrapper1 != wrapper2
        
        # Same processor returns same wrapper
        wrapper1_again = factory.wrap_processor(processor1)
        assert wrapper1 is wrapper1_again
        
    def test_get_all_stats(self, factory):
        """Test getting stats for all processors."""
        processor1 = MockProcessor("Processor1")
        processor2 = MockProcessor("Processor2")
        
        factory.wrap_processor(processor1)
        factory.wrap_processor(processor2)
        
        stats = factory.get_all_stats()
        assert "MockProcessor" in stats
        
    @pytest.mark.asyncio
    async def test_health_check_all(self, factory):
        """Test health checking all processors."""
        processor1 = MockProcessor("Processor1")
        processor2 = MockProcessor("Processor2")
        
        factory.wrap_processor(processor1)
        factory.wrap_processor(processor2)
        
        health = await factory.health_check_all()
        assert "MockProcessor" in health
        assert health["MockProcessor"]["is_healthy"] is True