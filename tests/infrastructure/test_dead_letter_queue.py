"""Tests for dead letter queue implementation."""

import asyncio
import json
import pytest
from datetime import datetime, timedelta
from pathlib import Path
import tempfile
import shutil
from unittest.mock import Mock, AsyncMock, patch

from src.infrastructure.queues.dead_letter_queue import (
    DeadLetterQueue,
    DLQRecord,
    RecordStatus
)


class TestDLQRecord:
    """Test DLQ record functionality."""
    
    def test_record_creation(self):
        """Test creating a DLQ record."""
        record = DLQRecord(
            id="test_123",
            source="test_processor",
            record_type="test_data",
            data={"key": "value"},
            error="Test error",
            error_type="ValueError",
            timestamp=datetime.now()
        )
        
        assert record.id == "test_123"
        assert record.source == "test_processor"
        assert record.status == RecordStatus.PENDING
        assert record.retry_count == 0
        assert record.max_retries == 3
        
    def test_record_expiry(self):
        """Test record expiry logic."""
        # Not expired
        record = DLQRecord(
            id="test_123",
            source="test",
            record_type="test",
            data={},
            error="error",
            error_type="Exception",
            timestamp=datetime.now(),
            ttl_hours=24
        )
        assert not record.is_expired
        
        # Expired
        old_record = DLQRecord(
            id="test_456",
            source="test",
            record_type="test",
            data={},
            error="error",
            error_type="Exception",
            timestamp=datetime.now() - timedelta(hours=25),
            ttl_hours=24
        )
        assert old_record.is_expired
        
    def test_can_retry_logic(self):
        """Test retry eligibility logic."""
        record = DLQRecord(
            id="test_123",
            source="test",
            record_type="test",
            data={},
            error="error",
            error_type="Exception",
            timestamp=datetime.now()
        )
        
        # Can retry initially
        assert record.can_retry
        
        # Can't retry if max attempts reached
        record.retry_count = 3
        assert not record.can_retry
        
        # Can't retry if not pending
        record.retry_count = 1
        record.status = RecordStatus.FAILED
        assert not record.can_retry
        
        # Can't retry if expired
        record.status = RecordStatus.PENDING
        record.timestamp = datetime.now() - timedelta(days=8)
        assert not record.can_retry
        
    def test_record_serialization(self):
        """Test record to/from dict conversion."""
        original = DLQRecord(
            id="test_123",
            source="test",
            record_type="test",
            data={"nested": {"key": "value"}},
            error="error",
            error_type="Exception",
            timestamp=datetime.now(),
            metadata={"extra": "info"}
        )
        
        # To dict
        record_dict = original.to_dict()
        assert record_dict["id"] == "test_123"
        assert record_dict["metadata"]["extra"] == "info"
        
        # From dict
        restored = DLQRecord.from_dict(record_dict)
        assert restored.id == original.id
        assert restored.source == original.source
        assert restored.data == original.data
        assert restored.metadata == original.metadata


class TestDeadLetterQueue:
    """Test dead letter queue functionality."""
    
    @pytest.fixture
    async def dlq(self):
        """Create a DLQ for testing."""
        temp_dir = tempfile.mkdtemp()
        dlq = DeadLetterQueue(
            storage_path=Path(temp_dir),
            max_queue_size=100,
            cleanup_interval=1  # Fast cleanup for tests
        )
        await dlq.start()
        yield dlq
        await dlq.stop()
        shutil.rmtree(temp_dir)
        
    @pytest.fixture
    def mock_handler(self):
        """Create a mock retry handler."""
        return AsyncMock(return_value=None)
        
    @pytest.mark.asyncio
    async def test_enqueue_record(self, dlq):
        """Test enqueueing a failed record."""
        record_id = await dlq.enqueue(
            source="test_processor",
            record_type="test_data",
            data={"id": 1, "value": "test"},
            error=ValueError("Test error"),
            metadata={"attempt": 1}
        )
        
        assert record_id.startswith("test_processor_test_data_")
        
        # Check stats
        stats = dlq.get_stats()
        assert stats["queue_size"] == 1
        assert stats["stats"]["total_enqueued"] == 1
        
    @pytest.mark.asyncio
    async def test_max_queue_size(self, dlq):
        """Test queue size limit enforcement."""
        # Fill queue
        for i in range(100):
            await dlq.enqueue(
                source="test",
                record_type="data",
                data={"id": i},
                error=Exception("error")
            )
            
        # Next enqueue should raise
        with pytest.raises(RuntimeError, match="Dead letter queue full"):
            await dlq.enqueue(
                source="test",
                record_type="data",
                data={"id": 100},
                error=Exception("error")
            )
            
    @pytest.mark.asyncio
    async def test_retry_handler_registration(self, dlq, mock_handler):
        """Test registering retry handlers."""
        dlq.register_retry_handler("test_processor", mock_handler)
        
        assert "test_processor" in dlq._retry_handlers
        assert dlq._retry_handlers["test_processor"] is mock_handler
        
    @pytest.mark.asyncio
    async def test_successful_retry(self, dlq, mock_handler):
        """Test successful retry of a record."""
        # Register handler
        dlq.register_retry_handler("test_processor", mock_handler)
        
        # Enqueue record
        await dlq.enqueue(
            source="test_processor",
            record_type="test",
            data={"id": 1},
            error=Exception("error")
        )
        
        # Get record for testing
        records = await dlq.get_records(source="test_processor")
        assert len(records) == 1
        record = records[0]
        
        # Retry record
        success = await dlq._retry_record(record)
        
        assert success
        assert mock_handler.called
        assert mock_handler.call_args[0][0] == {"id": 1}
        
        # Check stats
        stats = dlq.get_stats()
        assert stats["stats"]["total_succeeded"] == 1
        
    @pytest.mark.asyncio
    async def test_failed_retry(self, dlq):
        """Test failed retry of a record."""
        # Register failing handler
        async def failing_handler(data):
            raise Exception("Retry failed")
            
        dlq.register_retry_handler("test_processor", failing_handler)
        
        # Create record
        record = DLQRecord(
            id="test_123",
            source="test_processor",
            record_type="test",
            data={"id": 1},
            error="original error",
            error_type="Exception",
            timestamp=datetime.now(),
            max_retries=1  # Only one retry allowed
        )
        
        # First retry - should remain pending
        success = await dlq._retry_record(record)
        assert not success
        assert record.retry_count == 1
        assert record.status == RecordStatus.PENDING
        
        # Second retry - should permanently fail
        success = await dlq._retry_record(record)
        assert not success
        assert record.retry_count == 2
        assert record.status == RecordStatus.FAILED
        
        # Check stats
        stats = dlq.get_stats()
        assert stats["stats"]["total_retried"] == 2
        assert stats["stats"]["total_failed"] == 1
        
    @pytest.mark.asyncio
    async def test_no_handler_registered(self, dlq):
        """Test retry without registered handler."""
        record = DLQRecord(
            id="test_123",
            source="unknown_processor",
            record_type="test",
            data={},
            error="error",
            error_type="Exception",
            timestamp=datetime.now()
        )
        
        success = await dlq._retry_record(record)
        assert not success
        
    @pytest.mark.asyncio
    async def test_expired_record_cleanup(self, dlq):
        """Test cleanup of expired records."""
        # Add expired record
        expired_record = DLQRecord(
            id="expired_123",
            source="test",
            record_type="test",
            data={},
            error="error",
            error_type="Exception",
            timestamp=datetime.now() - timedelta(days=8),
            ttl_hours=168  # 7 days
        )
        
        # Add directly to queue
        await dlq._queue.put(expired_record)
        
        # Add non-expired record
        await dlq.enqueue(
            source="test",
            record_type="test",
            data={},
            error=Exception("error")
        )
        
        # Run cleanup
        removed = await dlq._cleanup_expired()
        
        assert removed == 1
        assert dlq._queue.qsize() == 1
        
        stats = dlq.get_stats()
        assert stats["stats"]["total_expired"] == 1
        
    @pytest.mark.asyncio
    async def test_get_records_filtering(self, dlq):
        """Test getting records with filters."""
        # Add various records
        await dlq.enqueue(
            source="processor1",
            record_type="type1",
            data={"id": 1},
            error=Exception("error")
        )
        
        await dlq.enqueue(
            source="processor2",
            record_type="type2",
            data={"id": 2},
            error=Exception("error")
        )
        
        # Mark one as failed
        records = await dlq.get_records()
        records[1].status = RecordStatus.FAILED
        
        # Test source filter
        processor1_records = await dlq.get_records(source="processor1")
        assert len(processor1_records) == 1
        assert processor1_records[0].source == "processor1"
        
        # Test status filter
        pending_records = await dlq.get_records(status=RecordStatus.PENDING)
        assert len(pending_records) == 2  # Both still pending in queue
        
        # Test limit
        limited_records = await dlq.get_records(limit=1)
        assert len(limited_records) == 1
        
    @pytest.mark.asyncio
    async def test_persistence(self):
        """Test DLQ persistence to disk."""
        temp_dir = tempfile.mkdtemp()
        
        # Create first DLQ and add records
        dlq1 = DeadLetterQueue(storage_path=Path(temp_dir))
        await dlq1.start()
        
        await dlq1.enqueue(
            source="test",
            record_type="test",
            data={"persisted": True},
            error=Exception("error")
        )
        
        await dlq1.stop()
        
        # Create second DLQ from same directory
        dlq2 = DeadLetterQueue(storage_path=Path(temp_dir))
        await dlq2.start()
        
        # Should load persisted record
        records = await dlq2.get_records()
        assert len(records) == 1
        assert records[0].data["persisted"] is True
        
        await dlq2.stop()
        shutil.rmtree(temp_dir)
        
    @pytest.mark.asyncio
    async def test_concurrent_operations(self, dlq):
        """Test concurrent enqueue operations."""
        async def enqueue_many(start_id: int, count: int):
            for i in range(count):
                await dlq.enqueue(
                    source="test",
                    record_type="test",
                    data={"id": start_id + i},
                    error=Exception("error")
                )
                
        # Run concurrent enqueues
        await asyncio.gather(
            enqueue_many(0, 10),
            enqueue_many(10, 10),
            enqueue_many(20, 10)
        )
        
        stats = dlq.get_stats()
        assert stats["queue_size"] == 30
        assert stats["stats"]["total_enqueued"] == 30