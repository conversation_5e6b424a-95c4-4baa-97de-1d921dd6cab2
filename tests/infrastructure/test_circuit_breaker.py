"""Tests for circuit breaker implementation."""

import asyncio
import pytest
from datetime import datetime, timedelta
from unittest.mock import Mo<PERSON>, AsyncMock

from src.infrastructure.resilience.circuit_breaker import (
    CircuitBreaker,
    CircuitBreakerConfig,
    CircuitState,
    CircuitOpenError,
    CircuitBreakerRegistry
)


class TestCircuitBreaker:
    """Test circuit breaker functionality."""
    
    @pytest.fixture
    def circuit_breaker(self):
        """Create a circuit breaker for testing."""
        config = CircuitBreakerConfig(
            failure_threshold=3,
            recovery_timeout=1,  # 1 second for faster tests
            success_threshold=2
        )
        return CircuitBreaker("test_service", config)
        
    @pytest.fixture
    def failing_function(self):
        """Create a function that always fails."""
        async def fail():
            raise Exception("Test failure")
        return fail
        
    @pytest.fixture
    def success_function(self):
        """Create a function that always succeeds."""
        async def success():
            return "success"
        return success
        
    @pytest.fixture
    def flaky_function(self):
        """Create a function that fails then succeeds."""
        call_count = 0
        
        async def flaky():
            nonlocal call_count
            call_count += 1
            if call_count <= 3:
                raise Exception(f"Failure {call_count}")
            return "success"
            
        return flaky
        
    @pytest.mark.asyncio
    async def test_circuit_starts_closed(self, circuit_breaker):
        """Test circuit breaker starts in closed state."""
        assert circuit_breaker.state == CircuitState.CLOSED
        assert circuit_breaker.is_closed
        assert not circuit_breaker.is_open
        
    @pytest.mark.asyncio
    async def test_successful_calls(self, circuit_breaker, success_function):
        """Test successful calls don't open circuit."""
        for _ in range(10):
            result = await circuit_breaker.call(success_function)
            assert result == "success"
            
        assert circuit_breaker.state == CircuitState.CLOSED
        assert circuit_breaker.stats.successful_calls == 10
        assert circuit_breaker.stats.failed_calls == 0
        
    @pytest.mark.asyncio
    async def test_circuit_opens_after_failures(self, circuit_breaker, failing_function):
        """Test circuit opens after threshold failures."""
        # First 2 failures - circuit stays closed
        for i in range(2):
            with pytest.raises(Exception):
                await circuit_breaker.call(failing_function)
        assert circuit_breaker.state == CircuitState.CLOSED
        
        # Third failure - circuit opens
        with pytest.raises(Exception):
            await circuit_breaker.call(failing_function)
        assert circuit_breaker.state == CircuitState.OPEN
        assert circuit_breaker.stats.failed_calls == 3
        
    @pytest.mark.asyncio
    async def test_circuit_open_blocks_calls(self, circuit_breaker, success_function):
        """Test open circuit blocks calls."""
        # Force circuit open
        circuit_breaker._state = CircuitState.OPEN
        circuit_breaker._last_failure_time = asyncio.get_event_loop().time()
        
        # Calls should fail fast
        with pytest.raises(CircuitOpenError):
            await circuit_breaker.call(success_function)
            
    @pytest.mark.asyncio
    async def test_half_open_after_timeout(self, circuit_breaker, success_function):
        """Test circuit transitions to half-open after timeout."""
        # Force circuit open
        circuit_breaker._state = CircuitState.OPEN
        circuit_breaker._last_failure_time = asyncio.get_event_loop().time() - 2  # 2 seconds ago
        
        # Next call should transition to half-open and succeed
        result = await circuit_breaker.call(success_function)
        assert result == "success"
        assert circuit_breaker.state == CircuitState.HALF_OPEN
        
    @pytest.mark.asyncio
    async def test_half_open_to_closed(self, circuit_breaker, success_function):
        """Test circuit closes after success threshold in half-open."""
        # Force half-open state
        circuit_breaker._state = CircuitState.HALF_OPEN
        
        # First success - stays half-open
        await circuit_breaker.call(success_function)
        assert circuit_breaker.state == CircuitState.HALF_OPEN
        
        # Second success - circuit closes
        await circuit_breaker.call(success_function)
        assert circuit_breaker.state == CircuitState.CLOSED
        
    @pytest.mark.asyncio
    async def test_half_open_to_open(self, circuit_breaker, failing_function):
        """Test circuit reopens on failure in half-open state."""
        # Force half-open state
        circuit_breaker._state = CircuitState.HALF_OPEN
        
        # Failure reopens circuit
        with pytest.raises(Exception):
            await circuit_breaker.call(failing_function)
        assert circuit_breaker.state == CircuitState.OPEN
        
    @pytest.mark.asyncio
    async def test_decorator_usage(self, circuit_breaker):
        """Test circuit breaker as decorator."""
        call_count = 0
        
        @circuit_breaker
        async def decorated_function():
            nonlocal call_count
            call_count += 1
            if call_count <= 3:
                raise Exception("Failure")
            return "success"
            
        # Test failures open circuit
        for _ in range(3):
            with pytest.raises(Exception):
                await decorated_function()
                
        assert circuit_breaker.state == CircuitState.OPEN
        
    @pytest.mark.asyncio
    async def test_sync_function_wrapping(self, circuit_breaker):
        """Test circuit breaker with sync functions."""
        def sync_success():
            return "sync_success"
            
        result = await circuit_breaker.call(sync_success)
        assert result == "sync_success"
        
    @pytest.mark.asyncio
    async def test_failure_tracking(self, circuit_breaker):
        """Test failure reason tracking."""
        async def fail_with_type():
            raise ValueError("Test error")
            
        with pytest.raises(ValueError):
            await circuit_breaker.call(fail_with_type)
            
        stats = circuit_breaker.get_stats()
        assert "ValueError" in stats["failure_reasons"]
        assert stats["failure_reasons"]["ValueError"] == 1
        
    @pytest.mark.asyncio
    async def test_time_window_failures(self, circuit_breaker):
        """Test failures outside time window don't count."""
        # Configure with 1 second window
        circuit_breaker.config.time_window = 1
        
        # Add old failure
        circuit_breaker._failure_timestamps = [
            asyncio.get_event_loop().time() - 2  # 2 seconds old
        ]
        circuit_breaker._failure_count = 1
        
        # Clean old failures
        circuit_breaker._clean_old_failures()
        
        assert circuit_breaker._failure_count == 0
        assert len(circuit_breaker._failure_timestamps) == 0


class TestCircuitBreakerRegistry:
    """Test circuit breaker registry."""
    
    @pytest.fixture
    def registry(self):
        """Create a registry for testing."""
        return CircuitBreakerRegistry()
        
    def test_register_breaker(self, registry):
        """Test registering circuit breakers."""
        breaker1 = registry.register("service1")
        breaker2 = registry.register("service2")
        
        assert breaker1.name == "service1"
        assert breaker2.name == "service2"
        assert breaker1 != breaker2
        
    def test_get_existing_breaker(self, registry):
        """Test getting existing breaker returns same instance."""
        breaker1 = registry.register("service1")
        breaker2 = registry.register("service1")
        
        assert breaker1 is breaker2
        
    def test_get_breaker(self, registry):
        """Test getting breaker by name."""
        breaker = registry.register("service1")
        
        retrieved = registry.get("service1")
        assert retrieved is breaker
        
        assert registry.get("nonexistent") is None
        
    def test_get_all_stats(self, registry):
        """Test getting stats for all breakers."""
        registry.register("service1")
        registry.register("service2")
        
        stats = registry.get_all_stats()
        
        assert "service1" in stats
        assert "service2" in stats
        assert stats["service1"]["name"] == "service1"
        assert stats["service2"]["name"] == "service2"
        
    def test_reset_breaker(self, registry):
        """Test resetting a circuit breaker."""
        breaker = registry.register("service1")
        
        # Force open
        breaker._state = CircuitState.OPEN
        breaker._failure_count = 5
        
        # Reset
        registry.reset("service1")
        
        assert breaker.state == CircuitState.CLOSED
        assert breaker._failure_count == 0