# Critical Import Fixes - Yemen Market Integration Data Pipeline V2

**Priority**: URGENT - Blocking all Day 10 objectives  
**Impact**: Prevents end-to-end testing, migration, and CLI functionality

## Root Cause Analysis

The system has cascading import failures due to missing value objects and enum values that are referenced throughout the codebase but not implemented.

## Critical Fixes Required

### 1. Missing ConflictEventId Value Object

**Error**: `ImportError: cannot import name 'ConflictEventId' from 'src.core.domain.conflict.value_objects'`

**Files Affected**:
- `src/infrastructure/persistence/repository_impls/postgres/conflict_repository.py`
- Multiple test files

**Fix**: Add ConflictEventId to `src/core/domain/conflict/value_objects.py`

```python
@dataclass(frozen=True)
class ConflictEventId(ValueObject):
    """Unique identifier for conflict events."""
    
    value: str
    
    def __post_init__(self) -> None:
        """Validate conflict event ID."""
        if not self.value or len(self.value) < 3:
            raise ValidationException("ConflictEventId must be at least 3 characters")
```

### 2. Missing BATTLE Enum Value

**Error**: `AttributeError: BATTLE`

**Files Affected**:
- `src/infrastructure/processors/acled_processor.py`
- Multiple test files

**Fix**: Add BATTLE to EventType enum in `src/core/domain/conflict/value_objects.py`

```python
class EventType(Enum):
    """Types of conflict events from ACLED."""
    BATTLES = "Battles"
    BATTLE = "Battles"  # Alias for backward compatibility
    EXPLOSIONS_REMOTE_VIOLENCE = "Explosions/Remote violence"
    VIOLENCE_AGAINST_CIVILIANS = "Violence against civilians"
    PROTESTS = "Protests"
    RIOTS = "Riots"
    STRATEGIC_DEVELOPMENTS = "Strategic developments"
```

### 3. Missing CacheStrategy

**Error**: `ImportError: cannot import name 'CacheStrategy' from 'src.infrastructure.caching.cache_manager'`

**Fix**: Add CacheStrategy enum to `src/infrastructure/caching/cache_manager.py`

```python
class CacheStrategy(Enum):
    """Cache strategy options."""
    LRU = "lru"
    FIFO = "fifo"
    TTL = "ttl"
    NONE = "none"
```

### 4. Missing ExchangeRateCollector

**Error**: `ImportError: cannot import name 'ExchangeRateCollector' from 'src.infrastructure.external_services.exchange_rate_collector'`

**Fix**: Ensure ExchangeRateCollector class is properly exported in the module

### 5. Missing HDXEnhancedClient

**Error**: `ModuleNotFoundError: No module named 'src.infrastructure.external_services.hdx_enhanced_client'`

**Fix**: Create or verify the hdx_enhanced_client.py file exists

## Implementation Priority

### Phase 1: Core Value Objects (30 minutes)
1. Add ConflictEventId to conflict value objects
2. Add BATTLE enum value to EventType
3. Add missing aliases for backward compatibility

### Phase 2: Infrastructure Components (45 minutes)
1. Add CacheStrategy enum to cache manager
2. Verify ExchangeRateCollector implementation
3. Check HDXEnhancedClient module existence

### Phase 3: Validation (15 minutes)
1. Run basic import tests
2. Verify processor loading works
3. Test CLI status command

## Quick Validation Commands

After implementing fixes, run these commands to validate:

```bash
# Test basic imports
uv run python -c "from src.core.domain.conflict.value_objects import ConflictEventId, ConflictType; print('✅ Conflict imports work')"

# Test processor imports
uv run python -c "from src.infrastructure.processors.acled_processor import ACLEDProcessor; print('✅ ACLED processor imports work')"

# Test cache manager
uv run python -c "from src.infrastructure.caching.cache_manager import CacheStrategy; print('✅ Cache manager imports work')"

# Test CLI
uv run python -m src.cli status
```

## Expected Outcome

Once these critical imports are fixed:
- ✅ CLI status command should work
- ✅ End-to-end pipeline test should load
- ✅ Migration scripts should be executable
- ✅ Test coverage can be properly measured

## Next Steps After Fixes

1. **Immediate**: Run performance validation again to confirm improvements
2. **Short-term**: Execute end-to-end pipeline test
3. **Medium-term**: Complete data migration and achieve >90% test coverage

This represents the minimum viable fixes needed to unblock Day 10 objectives and enable progression to Day 11 tasks.
