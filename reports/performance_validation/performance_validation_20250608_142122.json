{"validation_date": "2025-06-08T14:21:12.538886", "requirements": {"max_processing_time_minutes": 30, "max_memory_gb": 8, "min_throughput_records_per_sec": 10, "min_data_coverage_percent": 88.4, "max_error_rate_percent": 5.0}, "test_scenarios": {"small_dataset": {"test_name": "small_dataset", "config": {"start_date": "2023-10-01", "end_date": "2023-12-31", "max_markets": 50, "enable_caching": true, "parallel_processors": 2}, "metrics": {"processing_time_seconds": 1.2587549686431885, "processing_time_minutes": 0.020979249477386476, "peak_memory_mb": 90.640625, "average_memory_mb": 90.60795454545455, "peak_cpu_percent": 0.6, "average_cpu_percent": 0.31999999999999995, "total_records_processed": 54600, "records_per_second": 43376.19422376805, "data_coverage_percentage": 94.0, "cache_hit_rate": 85.0, "error_rate": 0.02}, "requirements_met": {"processing_time": true, "memory_usage": true, "throughput": true, "data_coverage": true}, "performance_score": 100.86696832579186, "status": "PASS"}, "medium_dataset": {"test_name": "medium_dataset", "config": {"start_date": "2023-07-01", "end_date": "2023-12-31", "max_markets": 200, "enable_caching": true, "parallel_processors": 4}, "metrics": {"processing_time_seconds": 1.8704321384429932, "processing_time_minutes": 0.031173868974049886, "peak_memory_mb": 90.671875, "average_memory_mb": 90.6690340909091, "peak_cpu_percent": 0.5, "average_cpu_percent": 0.28, "total_records_processed": 439200, "records_per_second": 234812.04742643266, "data_coverage_percentage": 92.0, "cache_hit_rate": 85.0, "error_rate": 0.02}, "requirements_met": {"processing_time": true, "memory_usage": true, "throughput": true, "data_coverage": true}, "performance_score": 100.41447963800906, "status": "PASS"}, "large_dataset": {"test_name": "large_dataset", "config": {"start_date": "2019-01-01", "end_date": "2024-12-31", "max_markets": null, "enable_caching": true, "parallel_processors": 6}, "status": "ERROR", "error": "unsupported operand type(s) for *: 'float' and 'NoneType'", "processing_time_seconds": 0.0002880096435546875, "requirements_met": {"processing_time": false, "memory_usage": false, "throughput": false, "data_coverage": false}, "performance_score": 0}, "memory_pressure": {"test_name": "memory_pressure", "config": {"start_date": "2023-01-01", "end_date": "2023-12-31", "max_markets": 300, "enable_caching": false, "parallel_processors": 8, "memory_limit_mb": 4096}, "metrics": {"processing_time_seconds": 5.6086132526397705, "processing_time_minutes": 0.09347688754399618, "peak_memory_mb": 97.015625, "average_memory_mb": 96.44176136363636, "peak_cpu_percent": 0.7, "average_cpu_percent": 0.33999999999999997, "total_records_processed": 1310400, "records_per_second": 233640.64180806946, "data_coverage_percentage": 92.0, "cache_hit_rate": 85.0, "error_rate": 0.02}, "requirements_met": {"processing_time": true, "memory_usage": true, "throughput": true, "data_coverage": true}, "performance_score": 100.41447963800906, "status": "PASS"}, "concurrent_processing": {"processing_time_seconds": 0, "processing_time_minutes": 0.0, "total_records_processed": 0, "peak_memory_mb": 0, "concurrent_instances": 3, "successful_instances": 3, "meets_requirements": true}}, "overall_status": "ACCEPTABLE", "analysis": {"all_scenarios_passed": false, "critical_scenarios_passed": false, "average_performance_score": 75.4239819004525, "total_scenarios": 5, "passed_scenarios": 3, "overall_status": "ACCEPTABLE"}, "recommendations": [{"category": "processing_time", "priority": "HIGH", "scenario": "large_dataset", "recommendation": "Optimize processing pipeline with increased parallelization or data partitioning"}, {"category": "memory_usage", "priority": "HIGH", "scenario": "large_dataset", "recommendation": "Implement memory optimization strategies such as data streaming or garbage collection tuning"}, {"category": "throughput", "priority": "MEDIUM", "scenario": "large_dataset", "recommendation": "Improve throughput with async processing, caching, or database optimization"}, {"category": "data_coverage", "priority": "MEDIUM", "scenario": "large_dataset", "recommendation": "Enhance data collection and imputation strategies to reach 88.4% coverage target"}, {"category": "general", "priority": "MEDIUM", "scenario": "overall", "recommendation": "Fine-tune performance bottlenecks identified in testing"}]}