"""Enhanced Data Pipeline V2 CLI commands."""

import asyncio
from datetime import datetime
from pathlib import Path
from typing import List, Optional, Dict, Any
import json

import typer
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn
from rich.panel import Panel
from rich.tree import Tree
from rich.layout import Layout
from rich.live import Live

from ...infrastructure.processors.processor_factory import ProcessorFactory
from ...infrastructure.processors.panel_builder import PanelBuilder
from ...infrastructure.processors.derived_variables_calculator import DerivedVariablesCalculator
from ...infrastructure.data_quality.validation_framework import (
    ValidationFramework, ValidationLevel, ValidationResult
)
from ...infrastructure.caching.cache_manager import CacheManager
from ...infrastructure.ui.progress_tracker import PipelineProgressTracker
from ...infrastructure.processors.base_processor import ProcessingResult


app = typer.Typer(
    name="pipeline",
    help="Data Pipeline V2 management commands with zero-error production design"
)
console = Console()


class PipelineV2Orchestrator:
    """Orchestrates the enhanced data pipeline V2."""
    
    def __init__(
        self,
        processor_factory: ProcessorFactory,
        panel_builder: PanelBuilder,
        derived_calculator: DerivedVariablesCalculator,
        validation_framework: ValidationFramework,
        cache_manager: CacheManager
    ):
        self.processor_factory = processor_factory
        self.panel_builder = panel_builder
        self.derived_calculator = derived_calculator
        self.validation_framework = validation_framework
        self.cache_manager = cache_manager
        self.progress_tracker = PipelineProgressTracker()
    
    async def run_full_pipeline(
        self,
        config: Dict[str, Any],
        progress_callback=None
    ) -> ProcessingResult:
        """Run the complete data pipeline."""
        pipeline_id = f"pipeline_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        async with self.progress_tracker.track_pipeline(
            pipeline_id=pipeline_id,
            total_stages=10
        ) as tracker:
            # Track each data source
            results = {}
            
            # Process each data source
            data_sources = [
                "conflict", "aid", "climate", "population", 
                "infrastructure", "control_zones", "global_prices",
                "ipc", "exchange_rates", "prices"
            ]
            
            for i, source in enumerate(data_sources):
                if progress_callback:
                    await progress_callback(tracker.get_current_progress())
                
                async with tracker.track_stage(
                    f"Processing {source}",
                    total_items=1000  # Estimate
                ) as stage_tracker:
                    processor = self.processor_factory.create_processor(source)
                    result = await processor.process(
                        start_date=config.get('start_date'),
                        end_date=config.get('end_date'),
                        progress_callback=stage_tracker.update_progress
                    )
                    results[source] = result
            
            # Build integrated panel
            async with tracker.track_stage("Building integrated panel") as stage_tracker:
                panel = await self.panel_builder.process(
                    markets=results['prices'].markets,
                    price_observations=results['prices'].observations,
                    conflict_events=results.get('conflict', {}).get('events'),
                    aid_distributions=results.get('aid', {}).get('distributions'),
                    climate_observations=results.get('climate', {}).get('observations'),
                    population_data=results.get('population', {}).get('data'),
                    infrastructure_metrics=results.get('infrastructure', {}).get('metrics'),
                    control_zones=results.get('control_zones', {}).get('zones'),
                    global_prices=results.get('global_prices', {}).get('prices'),
                    ipc_classifications=results.get('ipc', {}).get('classifications'),
                    exchange_rates=results.get('exchange_rates', {}).get('rates'),
                    start_date=config.get('start_date'),
                    end_date=config.get('end_date')
                )
            
            # Calculate derived variables
            async with tracker.track_stage("Calculating derived variables") as stage_tracker:
                panel_with_derived = await self.derived_calculator.calculate_all_variables(panel)
            
            # Validate final panel
            async with tracker.track_stage("Validating panel") as stage_tracker:
                validation_result = await self.validation_framework.validate_dataframe(
                    panel_with_derived,
                    source_type="integrated_panel",
                    level=ValidationLevel.STRICT
                )
                
                if not validation_result.is_valid:
                    raise ValueError(f"Panel validation failed: {validation_result.errors}")
            
            # Save panel
            output_path = Path(config.get('output_dir', 'data/processed')) / f"{pipeline_id}_panel.parquet"
            output_path.parent.mkdir(parents=True, exist_ok=True)
            panel_with_derived.to_parquet(output_path)
            
            return ProcessingResult(
                success=True,
                records_processed=len(panel_with_derived),
                metadata={
                    'pipeline_id': pipeline_id,
                    'output_path': str(output_path),
                    'coverage': len(panel_with_derived) / (300 * 36 * 12),  # Estimate
                    'validation_status': 'passed',
                    'sources_processed': list(results.keys())
                }
            )


@app.command()
def run(
    start_date: str = typer.Option("2019-01-01", help="Start date (YYYY-MM-DD)"),
    end_date: str = typer.Option("2025-03-01", help="End date (YYYY-MM-DD)"),
    sources: Optional[List[str]] = typer.Option(None, help="Specific sources to process"),
    output_dir: Path = typer.Option("data/processed", help="Output directory"),
    config_file: Optional[Path] = typer.Option(None, help="Configuration file"),
    validate: bool = typer.Option(True, help="Run validation"),
    cache: bool = typer.Option(True, help="Use caching"),
    parallel: bool = typer.Option(True, help="Run processors in parallel"),
    dry_run: bool = typer.Option(False, help="Show what would be done without executing")
):
    """Run the enhanced data pipeline V2 with zero-error design."""
    console.print(Panel.fit(
        "[bold blue]Data Pipeline V2[/bold blue]\n"
        f"Period: {start_date} to {end_date}\n"
        f"Output: {output_dir}",
        title="Pipeline Configuration"
    ))
    
    if dry_run:
        console.print("[yellow]DRY RUN MODE - No data will be processed[/yellow]")
    
    # Load configuration
    config = {
        'start_date': datetime.fromisoformat(start_date),
        'end_date': datetime.fromisoformat(end_date),
        'output_dir': output_dir,
        'validate': validate,
        'use_cache': cache,
        'parallel': parallel
    }
    
    if config_file and config_file.exists():
        with open(config_file) as f:
            file_config = json.load(f)
            config.update(file_config)
    
    if sources:
        config['sources'] = sources
    
    # Show execution plan
    if dry_run:
        _show_execution_plan(config)
        return
    
    # Initialize components
    cache_manager = CacheManager() if cache else None
    processor_factory = ProcessorFactory(cache_manager=cache_manager)
    panel_builder = PanelBuilder()
    derived_calculator = DerivedVariablesCalculator()
    validation_framework = ValidationFramework()
    
    orchestrator = PipelineV2Orchestrator(
        processor_factory=processor_factory,
        panel_builder=panel_builder,
        derived_calculator=derived_calculator,
        validation_framework=validation_framework,
        cache_manager=cache_manager
    )
    
    # Progress tracking with Rich
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
        TimeElapsedColumn(),
        console=console
    ) as progress:
        
        main_task = progress.add_task("Running pipeline...", total=100)
        
        async def update_progress(status):
            progress.update(main_task, completed=status['overall_progress'] * 100)
            progress.update(main_task, description=status.get('current_stage', 'Processing...'))
        
        try:
            # Run pipeline
            result = asyncio.run(orchestrator.run_full_pipeline(config, update_progress))
            
            # Show results
            if result.success:
                console.print(Panel.fit(
                    f"[bold green]Pipeline completed successfully![/bold green]\n"
                    f"Records processed: {result.records_processed:,}\n"
                    f"Output: {result.metadata['output_path']}\n"
                    f"Coverage: {result.metadata['coverage']:.1%}",
                    title="Success"
                ))
            else:
                console.print(f"[bold red]Pipeline failed![/bold red]")
                if result.errors:
                    for error in result.errors:
                        console.print(f"  - {error}")
                        
        except Exception as e:
            console.print(f"[bold red]Error:[/bold red] {str(e)}")
            raise typer.Exit(1)


@app.command()
def status(
    pipeline_id: Optional[str] = typer.Option(None, help="Specific pipeline ID"),
    show_all: bool = typer.Option(False, help="Show all pipelines")
):
    """Check status of running or completed pipelines."""
    console.print("[bold blue]Pipeline Status[/bold blue]")
    
    # In production, this would query a database or status file
    status_dir = Path("data/pipeline_status")
    
    if pipeline_id:
        status_file = status_dir / f"{pipeline_id}_status.json"
        if status_file.exists():
            with open(status_file) as f:
                status = json.load(f)
                _display_pipeline_status(status)
        else:
            console.print(f"[red]Pipeline {pipeline_id} not found[/red]")
    else:
        # Show recent pipelines
        if status_dir.exists():
            status_files = sorted(status_dir.glob("*_status.json"), reverse=True)
            
            if not status_files:
                console.print("[yellow]No pipeline runs found[/yellow]")
                return
            
            table = Table(title="Recent Pipeline Runs")
            table.add_column("Pipeline ID", style="cyan")
            table.add_column("Status", style="green")
            table.add_column("Started", style="yellow")
            table.add_column("Duration", style="blue")
            table.add_column("Coverage", style="magenta")
            
            for status_file in status_files[:10 if not show_all else None]:
                with open(status_file) as f:
                    status = json.load(f)
                    table.add_row(
                        status['pipeline_id'],
                        status['status'],
                        status['started_at'],
                        f"{status.get('duration_seconds', 0):.1f}s",
                        f"{status.get('coverage', 0):.1%}"
                    )
            
            console.print(table)


@app.command()
def validate(
    input_path: Path = typer.Argument(..., help="Path to data to validate"),
    level: str = typer.Option("strict", help="Validation level (basic/standard/strict)"),
    source_type: str = typer.Option("panel", help="Source type for validation rules"),
    report_path: Optional[Path] = typer.Option(None, help="Save validation report"),
    fix: bool = typer.Option(False, help="Attempt to fix validation issues")
):
    """Validate data quality and methodology compliance."""
    console.print(f"[bold blue]Validating {input_path}[/bold blue]")
    
    if not input_path.exists():
        console.print(f"[red]File not found: {input_path}[/red]")
        raise typer.Exit(1)
    
    # Load data
    import pandas as pd
    if input_path.suffix == '.parquet':
        df = pd.read_parquet(input_path)
    elif input_path.suffix == '.csv':
        df = pd.read_csv(input_path)
    else:
        console.print(f"[red]Unsupported file format: {input_path.suffix}[/red]")
        raise typer.Exit(1)
    
    console.print(f"Loaded {len(df):,} rows, {len(df.columns)} columns")
    
    # Run validation
    validation_framework = ValidationFramework()
    
    level_map = {
        'basic': ValidationLevel.BASIC,
        'standard': ValidationLevel.STANDARD,
        'strict': ValidationLevel.STRICT
    }
    
    result = asyncio.run(validation_framework.validate_dataframe(
        df, 
        source_type=source_type,
        level=level_map.get(level, ValidationLevel.STRICT)
    ))
    
    # Display results
    _display_validation_results(result)
    
    # Save report if requested
    if report_path:
        report = {
            'input_file': str(input_path),
            'validation_time': datetime.now().isoformat(),
            'level': level,
            'source_type': source_type,
            'is_valid': result.is_valid,
            'errors': result.errors,
            'warnings': result.warnings,
            'summary': result.summary
        }
        
        report_path.parent.mkdir(parents=True, exist_ok=True)
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2)
        
        console.print(f"[green]Report saved to: {report_path}[/green]")
    
    # Attempt fixes if requested
    if fix and not result.is_valid:
        console.print("[yellow]Attempting to fix validation issues...[/yellow]")
        # This would implement specific fix strategies
        console.print("[yellow]Fix functionality not yet implemented[/yellow]")
    
    if not result.is_valid:
        raise typer.Exit(1)


@app.command()
def config(
    show: bool = typer.Option(False, help="Show current configuration"),
    edit: bool = typer.Option(False, help="Edit configuration interactively"),
    export: Optional[Path] = typer.Option(None, help="Export configuration to file"),
    import_from: Optional[Path] = typer.Option(None, help="Import configuration from file")
):
    """Manage pipeline configuration."""
    config_path = Path.home() / ".yemen_market" / "pipeline_v2_config.json"
    
    if show:
        if config_path.exists():
            with open(config_path) as f:
                config = json.load(f)
                console.print(Panel.fit(
                    json.dumps(config, indent=2),
                    title="Current Pipeline Configuration"
                ))
        else:
            console.print("[yellow]No configuration file found[/yellow]")
    
    elif edit:
        # Interactive configuration editor
        console.print("[yellow]Interactive configuration editor not yet implemented[/yellow]")
        console.print("Please edit configuration file directly or use import")
    
    elif export:
        if config_path.exists():
            import shutil
            shutil.copy(config_path, export)
            console.print(f"[green]Configuration exported to: {export}[/green]")
        else:
            console.print("[red]No configuration to export[/red]")
    
    elif import_from:
        if not import_from.exists():
            console.print(f"[red]File not found: {import_from}[/red]")
            raise typer.Exit(1)
        
        # Validate configuration
        with open(import_from) as f:
            try:
                config = json.load(f)
                # Basic validation
                required_keys = ['start_date', 'end_date', 'sources']
                missing = [k for k in required_keys if k not in config]
                if missing:
                    console.print(f"[red]Missing required keys: {missing}[/red]")
                    raise typer.Exit(1)
                
                # Save configuration
                config_path.parent.mkdir(parents=True, exist_ok=True)
                with open(config_path, 'w') as out:
                    json.dump(config, out, indent=2)
                
                console.print(f"[green]Configuration imported successfully[/green]")
                
            except json.JSONDecodeError as e:
                console.print(f"[red]Invalid JSON: {e}[/red]")
                raise typer.Exit(1)
    else:
        console.print("Use --show, --edit, --export, or --import-from")


@app.command()
def sources(
    list_all: bool = typer.Option(False, help="List all available sources"),
    info: Optional[str] = typer.Option(None, help="Get info about specific source"),
    test: Optional[str] = typer.Option(None, help="Test connection to source")
):
    """Manage data sources."""
    available_sources = {
        'conflict': {
            'name': 'ACLED Conflict Data',
            'type': 'API',
            'frequency': 'Daily',
            'coverage': '2015-present'
        },
        'aid': {
            'name': 'OCHA Aid Distribution',
            'type': 'Multiple (3W, FTS, Cash)',
            'frequency': 'Monthly',
            'coverage': '2019-present'
        },
        'climate': {
            'name': 'Climate Data (CHIRPS, MODIS)',
            'type': 'Raster/API',
            'frequency': 'Daily',
            'coverage': '2000-present'
        },
        'population': {
            'name': 'WorldPop & IOM DTM',
            'type': 'Raster/CSV',
            'frequency': 'Annual/Quarterly',
            'coverage': '2015-present'
        },
        'infrastructure': {
            'name': 'OSM Infrastructure',
            'type': 'API',
            'frequency': 'Static',
            'coverage': 'Current'
        },
        'control_zones': {
            'name': 'ACAPS Control Zones',
            'type': 'Shapefile',
            'frequency': 'Quarterly',
            'coverage': '2018-present'
        },
        'global_prices': {
            'name': 'FAO GIEWS Global Prices',
            'type': 'API',
            'frequency': 'Monthly',
            'coverage': '2010-present'
        },
        'ipc': {
            'name': 'IPC Food Security',
            'type': 'API/Excel',
            'frequency': 'Quarterly',
            'coverage': '2017-present'
        },
        'exchange_rates': {
            'name': 'WFP Exchange Rates',
            'type': 'API',
            'frequency': 'Daily',
            'coverage': '2019-present'
        },
        'prices': {
            'name': 'WFP Market Prices',
            'type': 'API',
            'frequency': 'Daily',
            'coverage': '2019-present'
        }
    }
    
    if list_all:
        table = Table(title="Available Data Sources")
        table.add_column("Source ID", style="cyan")
        table.add_column("Name", style="green")
        table.add_column("Type", style="yellow")
        table.add_column("Frequency", style="blue")
        table.add_column("Coverage", style="magenta")
        
        for source_id, info in available_sources.items():
            table.add_row(
                source_id,
                info['name'],
                info['type'],
                info['frequency'],
                info['coverage']
            )
        
        console.print(table)
    
    elif info:
        if info in available_sources:
            source_info = available_sources[info]
            console.print(Panel.fit(
                f"[bold]{source_info['name']}[/bold]\n\n"
                f"Type: {source_info['type']}\n"
                f"Update Frequency: {source_info['frequency']}\n"
                f"Coverage: {source_info['coverage']}\n",
                title=f"Source: {info}"
            ))
        else:
            console.print(f"[red]Unknown source: {info}[/red]")
    
    elif test:
        console.print(f"[yellow]Testing connection to {test}...[/yellow]")
        # This would test actual connections
        console.print("[yellow]Connection testing not yet implemented[/yellow]")
    
    else:
        console.print("Use --list-all, --info, or --test")


def _show_execution_plan(config: Dict[str, Any]):
    """Display execution plan for dry run."""
    tree = Tree("[bold]Execution Plan[/bold]")
    
    # Data collection
    collection = tree.add("1. Data Collection")
    sources = config.get('sources', [
        'conflict', 'aid', 'climate', 'population', 'infrastructure',
        'control_zones', 'global_prices', 'ipc', 'exchange_rates', 'prices'
    ])
    for source in sources:
        collection.add(f"• Process {source}")
    
    # Processing
    processing = tree.add("2. Data Processing")
    processing.add("• Temporal alignment to monthly")
    processing.add("• Spatial joining")
    processing.add("• Quality validation")
    
    # Integration
    integration = tree.add("3. Panel Integration")
    integration.add("• Merge all data sources")
    integration.add("• Calculate exchange rates")
    integration.add("• Apply currency zones")
    
    # Derived variables
    derived = tree.add("4. Derived Variables")
    derived.add("• Market integration metrics")
    derived.add("• Vulnerability indices")
    derived.add("• Food security indicators")
    
    # Validation
    validation = tree.add("5. Final Validation")
    validation.add("• Methodology compliance")
    validation.add("• Coverage calculation")
    validation.add("• Quality metrics")
    
    console.print(tree)
    
    # Show estimated resources
    console.print(Panel.fit(
        f"Estimated Duration: 15-30 minutes\n"
        f"Estimated Memory: 4-8 GB\n"
        f"Output Size: ~500 MB",
        title="Resource Estimates"
    ))


def _display_pipeline_status(status: Dict[str, Any]):
    """Display detailed pipeline status."""
    # Create layout
    layout = Layout()
    layout.split_column(
        Layout(name="header", size=3),
        Layout(name="body"),
        Layout(name="footer", size=3)
    )
    
    # Header
    layout["header"].update(Panel(
        f"Pipeline: {status['pipeline_id']}",
        style="bold blue"
    ))
    
    # Body with progress
    progress_text = ""
    for stage, progress in status.get('stages', {}).items():
        progress_text += f"{stage}: {'█' * int(progress * 20)}{'░' * (20 - int(progress * 20))} {progress:.0%}\n"
    
    layout["body"].update(Panel(progress_text, title="Progress"))
    
    # Footer
    layout["footer"].update(Panel(
        f"Status: {status['status']} | Duration: {status.get('duration_seconds', 0):.1f}s"
    ))
    
    console.print(layout)


def _display_validation_results(result: ValidationResult):
    """Display validation results in a nice format."""
    # Summary panel
    status_color = "green" if result.is_valid else "red"
    console.print(Panel.fit(
        f"[{status_color}]Validation {'PASSED' if result.is_valid else 'FAILED'}[/{status_color}]\n"
        f"Errors: {len(result.errors)}\n"
        f"Warnings: {len(result.warnings)}",
        title="Validation Summary"
    ))
    
    # Errors
    if result.errors:
        console.print("\n[bold red]Errors:[/bold red]")
        for error in result.errors[:10]:  # Show first 10
            console.print(f"  • {error}")
        if len(result.errors) > 10:
            console.print(f"  ... and {len(result.errors) - 10} more")
    
    # Warnings
    if result.warnings:
        console.print("\n[bold yellow]Warnings:[/bold yellow]")
        for warning in result.warnings[:10]:  # Show first 10
            console.print(f"  • {warning}")
        if len(result.warnings) > 10:
            console.print(f"  ... and {len(result.warnings) - 10} more")
    
    # Detailed summary
    if result.summary:
        console.print("\n[bold]Detailed Summary:[/bold]")
        table = Table()
        table.add_column("Metric", style="cyan")
        table.add_column("Value", style="green")
        
        for key, value in result.summary.items():
            table.add_row(key, str(value))
        
        console.print(table)


if __name__ == "__main__":
    app()