"""Main migration orchestrator for transitioning from old to new pipeline."""

import asyncio
import shutil
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
import pandas as pd
import numpy as np
import json
from src.infrastructure.logging import Logger
from dataclasses import dataclass, field, asdict
from enum import Enum

from src.infrastructure.data_quality.migration_tools import (
    DataFormatMigrator,
    VersionCompatibilityChecker,
    MigrationReport
)
from src.infrastructure.processors.panel_builder import PanelBuilder
from src.infrastructure.caching.cache_manager import CacheManager
from src.infrastructure.external_services.hdx_client import HDXClient
from src.application.services.data_pipeline_orchestrator import (
    DataPipelineOrchestrator,
    PipelineConfig,
    PipelineStatus
)
from src.core.domain.market.value_objects import CurrencyZone
from src.infrastructure.monitoring import MetricsCollector


logger = Logger(__name__)


class MigrationStrategy(Enum):
    """Migration strategy options."""
    FULL = "full"  # Migrate all data from scratch
    INCREMENTAL = "incremental"  # Migrate only new/changed data
    PARALLEL = "parallel"  # Run old and new in parallel
    ROLLBACK = "rollback"  # Rollback to previous state


@dataclass
class MigrationConfig:
    """Configuration for pipeline migration."""
    strategy: MigrationStrategy = MigrationStrategy.FULL
    backup_enabled: bool = True
    validation_enabled: bool = True
    parallel_validation: bool = True
    
    # Paths
    old_data_path: Path = Path("data/processed")
    new_data_path: Path = Path("data/processed_v2")
    backup_path: Path = Path("data/backups/migration")
    
    # Processing options
    batch_size: int = 10000
    max_workers: int = 4
    memory_limit_gb: float = 8.0
    
    # Validation thresholds
    max_difference_pct: float = 0.01  # 1% max difference
    min_correlation: float = 0.99  # 99% correlation required
    max_missing_pct: float = 0.05  # 5% max missing data
    
    # Rollback configuration
    rollback_checkpoint_interval: int = 1000  # Records per checkpoint
    max_rollback_attempts: int = 3


@dataclass 
class MigrationState:
    """Current state of migration."""
    migration_id: str
    start_time: datetime
    end_time: Optional[datetime] = None
    status: str = "initializing"
    current_phase: str = ""
    progress_pct: float = 0.0
    
    # Statistics
    records_processed: int = 0
    records_migrated: int = 0
    records_failed: int = 0
    
    # Validation results
    validation_passed: bool = False
    validation_details: Dict[str, Any] = field(default_factory=dict)
    
    # Checkpoints for rollback
    checkpoints: List[Dict[str, Any]] = field(default_factory=list)
    rollback_points: List[str] = field(default_factory=list)
    
    # Errors and warnings
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)


class PipelineMigrator:
    """Orchestrates migration from old to new data pipeline."""
    
    def __init__(self, config: MigrationConfig):
        """Initialize pipeline migrator."""
        self.config = config
        self.migration_id = f"migration_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.state = MigrationState(
            migration_id=self.migration_id,
            start_time=datetime.now()
        )
        
        # Components
        self.data_migrator = DataFormatMigrator(version="v2")
        self.compatibility_checker = VersionCompatibilityChecker()
        self.metrics_collector = MetricsCollector()
        
        # Ensure directories exist
        self._setup_directories()
        
    def _setup_directories(self) -> None:
        """Create necessary directories."""
        self.config.new_data_path.mkdir(parents=True, exist_ok=True)
        self.config.backup_path.mkdir(parents=True, exist_ok=True)
        
        # Create timestamped backup directory
        self.backup_dir = self.config.backup_path / self.migration_id
        self.backup_dir.mkdir(exist_ok=True)
        
    async def migrate(self) -> MigrationState:
        """Execute complete migration process."""
        try:
            logger.info(f"Starting pipeline migration: {self.migration_id}")
            self.state.status = "running"
            
            # Phase 1: Backup existing data
            if self.config.backup_enabled:
                await self._backup_existing_data()
            
            # Phase 2: Analyze old data structure
            data_inventory = await self._analyze_old_data()
            
            # Phase 3: Migrate data formats
            migration_results = await self._migrate_data_formats(data_inventory)
            
            # Phase 4: Validate migrated data
            if self.config.validation_enabled:
                validation_passed = await self._validate_migration(migration_results)
                if not validation_passed and self.config.strategy != MigrationStrategy.PARALLEL:
                    raise ValueError("Migration validation failed")
            
            # Phase 5: Run parallel validation if enabled
            if self.config.parallel_validation:
                await self._run_parallel_validation()
            
            # Phase 6: Finalize migration
            await self._finalize_migration()
            
            self.state.status = "completed"
            self.state.end_time = datetime.now()
            
            logger.info(f"Migration completed successfully: {self.migration_id}")
            
        except Exception as e:
            logger.error(f"Migration failed: {str(e)}")
            self.state.status = "failed"
            self.state.errors.append(str(e))
            
            # Attempt rollback if enabled
            if self.config.backup_enabled:
                await self._rollback_migration()
                
            raise
            
        finally:
            # Save final state
            await self._save_migration_state()
            
        return self.state
    
    async def _backup_existing_data(self) -> None:
        """Backup existing data before migration."""
        self.state.current_phase = "backup"
        logger.info("Creating backup of existing data")
        
        try:
            # Copy all existing processed data
            for item in self.config.old_data_path.rglob("*"):
                if item.is_file():
                    relative_path = item.relative_to(self.config.old_data_path)
                    backup_path = self.backup_dir / "old_data" / relative_path
                    backup_path.parent.mkdir(parents=True, exist_ok=True)
                    shutil.copy2(item, backup_path)
                    
            # Create backup manifest
            manifest = {
                "migration_id": self.migration_id,
                "backup_time": datetime.now().isoformat(),
                "source_path": str(self.config.old_data_path),
                "file_count": len(list(self.backup_dir.rglob("*")))
            }
            
            with open(self.backup_dir / "manifest.json", "w") as f:
                json.dump(manifest, f, indent=2)
                
            self.state.rollback_points.append(str(self.backup_dir))
            logger.info(f"Backup completed: {self.backup_dir}")
            
        except Exception as e:
            self.state.errors.append(f"Backup failed: {str(e)}")
            raise
    
    async def _analyze_old_data(self) -> Dict[str, Path]:
        """Analyze structure of old data."""
        self.state.current_phase = "analysis"
        logger.info("Analyzing old data structure")
        
        data_inventory = {}
        
        # Find key data files
        patterns = {
            "integrated_panel": "**/yemen_integrated_balanced_panel.parquet",
            "price_data": "**/wfp_food_prices.parquet",
            "exchange_rates": "**/exchange_rates.parquet",
            "control_zones": "**/control_zones_master.parquet",
            "conflict_data": "**/acled_events.parquet",
            "metadata": "**/panel_metadata.json"
        }
        
        for data_type, pattern in patterns.items():
            matches = list(self.config.old_data_path.glob(pattern))
            if matches:
                data_inventory[data_type] = matches[0]
                logger.info(f"Found {data_type}: {matches[0]}")
            else:
                self.state.warnings.append(f"No {data_type} found")
                
        return data_inventory
    
    async def _migrate_data_formats(
        self, 
        data_inventory: Dict[str, Path]
    ) -> Dict[str, MigrationReport]:
        """Migrate data to new formats."""
        self.state.current_phase = "migration"
        logger.info("Migrating data formats")
        
        migration_results = {}
        total_files = len(data_inventory)
        
        for idx, (data_type, file_path) in enumerate(data_inventory.items()):
            try:
                # Update progress
                self.state.progress_pct = (idx / total_files) * 100
                
                # Load data
                if file_path.suffix == '.parquet':
                    df = pd.read_parquet(file_path)
                elif file_path.suffix == '.json':
                    with open(file_path) as f:
                        data = json.load(f)
                    # Skip JSON files for now
                    continue
                else:
                    continue
                    
                # Determine source type for migration
                source_type = self._determine_source_type(data_type)
                
                # Migrate dataframe
                df_migrated, report = self.data_migrator.migrate_dataframe(
                    df, source_type
                )
                
                # Save migrated data
                output_path = self.config.new_data_path / data_type / f"{data_type}_v2.parquet"
                output_path.parent.mkdir(parents=True, exist_ok=True)
                df_migrated.to_parquet(output_path, index=False)
                
                migration_results[data_type] = report
                
                # Update statistics
                self.state.records_processed += len(df)
                self.state.records_migrated += report.records_migrated
                self.state.records_failed += report.records_failed
                
                # Create checkpoint
                if self.state.records_processed % self.config.rollback_checkpoint_interval == 0:
                    checkpoint = {
                        "timestamp": datetime.now().isoformat(),
                        "records_processed": self.state.records_processed,
                        "current_file": str(file_path),
                        "output_path": str(output_path)
                    }
                    self.state.checkpoints.append(checkpoint)
                    
                logger.info(f"Migrated {data_type}: {report.records_migrated} records")
                
            except Exception as e:
                error_msg = f"Failed to migrate {data_type}: {str(e)}"
                self.state.errors.append(error_msg)
                logger.error(error_msg)
                
                # Create failed migration report
                migration_results[data_type] = MigrationReport(
                    success=False,
                    records_migrated=0,
                    records_failed=len(df) if 'df' in locals() else 0,
                    issues=[str(e)]
                )
                
        return migration_results
    
    async def _validate_migration(
        self, 
        migration_results: Dict[str, MigrationReport]
    ) -> bool:
        """Validate migrated data against original."""
        self.state.current_phase = "validation"
        logger.info("Validating migrated data")
        
        validation_passed = True
        validation_details = {}
        
        for data_type, report in migration_results.items():
            if not report.success:
                validation_details[data_type] = {"status": "failed", "reason": "migration_failed"}
                validation_passed = False
                continue
                
            try:
                # Load old and new data
                old_path = self.data_inventory.get(data_type)
                new_path = self.config.new_data_path / data_type / f"{data_type}_v2.parquet"
                
                if not old_path or not new_path.exists():
                    continue
                    
                old_df = pd.read_parquet(old_path)
                new_df = pd.read_parquet(new_path)
                
                # Run compatibility check
                compatibility = self.compatibility_checker.check_compatibility(
                    old_df, new_df
                )
                
                # Additional validation checks
                validation_result = await self._validate_data_integrity(
                    old_df, new_df, data_type
                )
                
                validation_details[data_type] = {
                    "status": "passed" if compatibility['compatible'] else "failed",
                    "compatibility": compatibility,
                    "integrity": validation_result
                }
                
                if not compatibility['compatible']:
                    validation_passed = False
                    self.state.warnings.extend(compatibility['issues'])
                    
            except Exception as e:
                validation_details[data_type] = {
                    "status": "error",
                    "error": str(e)
                }
                validation_passed = False
                
        self.state.validation_passed = validation_passed
        self.state.validation_details = validation_details
        
        return validation_passed
    
    async def _validate_data_integrity(
        self,
        old_df: pd.DataFrame,
        new_df: pd.DataFrame,
        data_type: str
    ) -> Dict[str, Any]:
        """Validate data integrity between old and new formats."""
        result = {
            "checks_passed": True,
            "details": {}
        }
        
        # Check 1: Row count difference
        row_diff_pct = abs(len(new_df) - len(old_df)) / len(old_df) * 100
        result["details"]["row_count_diff_pct"] = row_diff_pct
        
        if row_diff_pct > self.config.max_difference_pct:
            result["checks_passed"] = False
            result["details"]["row_count_check"] = "failed"
            
        # Check 2: Key columns preservation
        if data_type == "integrated_panel":
            key_columns = ["market_id", "commodity", "date", "price", "price_usd"]
        elif data_type == "price_data":
            key_columns = ["market_id", "commodity", "date", "price"]
        else:
            key_columns = []
            
        for col in key_columns:
            if col in old_df.columns and col not in new_df.columns:
                result["checks_passed"] = False
                result["details"][f"missing_column_{col}"] = True
                
        # Check 3: Data value integrity (for numeric columns)
        numeric_columns = old_df.select_dtypes(include=[np.number]).columns
        
        for col in numeric_columns:
            if col in new_df.columns:
                # Calculate correlation
                if len(old_df) == len(new_df):
                    corr = old_df[col].corr(new_df[col])
                    result["details"][f"{col}_correlation"] = corr
                    
                    if corr < self.config.min_correlation:
                        result["checks_passed"] = False
                        
                # Check missing data increase
                old_missing_pct = old_df[col].isna().sum() / len(old_df) * 100
                new_missing_pct = new_df[col].isna().sum() / len(new_df) * 100
                
                if new_missing_pct - old_missing_pct > self.config.max_missing_pct:
                    result["checks_passed"] = False
                    result["details"][f"{col}_missing_increase"] = new_missing_pct - old_missing_pct
                    
        return result
    
    async def _run_parallel_validation(self) -> None:
        """Run old and new pipelines in parallel for comparison."""
        self.state.current_phase = "parallel_validation"
        logger.info("Running parallel validation")
        
        # This will be implemented by the parallel_validator.py
        # For now, just log the intent
        self.state.validation_details["parallel_validation"] = {
            "status": "pending",
            "message": "Parallel validation will be run by separate validator"
        }
        
    async def _finalize_migration(self) -> None:
        """Finalize migration process."""
        self.state.current_phase = "finalization"
        logger.info("Finalizing migration")
        
        # Create migration summary
        summary = {
            "migration_id": self.migration_id,
            "start_time": self.state.start_time.isoformat(),
            "end_time": datetime.now().isoformat(),
            "status": self.state.status,
            "records_processed": self.state.records_processed,
            "records_migrated": self.state.records_migrated,
            "records_failed": self.state.records_failed,
            "validation_passed": self.state.validation_passed,
            "errors": self.state.errors,
            "warnings": self.state.warnings
        }
        
        # Save summary
        summary_path = self.config.new_data_path / "migration_summary.json"
        with open(summary_path, "w") as f:
            json.dump(summary, f, indent=2)
            
        # Update symbolic links or references to point to new data
        if self.state.validation_passed and self.config.strategy == MigrationStrategy.FULL:
            logger.info("Migration validated - new pipeline data is ready for use")
            
    async def _rollback_migration(self) -> None:
        """Rollback migration in case of failure."""
        logger.info("Attempting rollback")
        
        try:
            # Remove any new data created
            if self.config.new_data_path.exists():
                shutil.rmtree(self.config.new_data_path)
                
            # Restore from backup if available
            if self.backup_dir.exists():
                backup_data_dir = self.backup_dir / "old_data"
                if backup_data_dir.exists():
                    # Copy back the original data
                    for item in backup_data_dir.rglob("*"):
                        if item.is_file():
                            relative_path = item.relative_to(backup_data_dir)
                            restore_path = self.config.old_data_path / relative_path
                            restore_path.parent.mkdir(parents=True, exist_ok=True)
                            shutil.copy2(item, restore_path)
                            
            self.state.status = "rolled_back"
            logger.info("Rollback completed successfully")
            
        except Exception as e:
            logger.error(f"Rollback failed: {str(e)}")
            self.state.errors.append(f"Rollback failed: {str(e)}")
            
    async def _save_migration_state(self) -> None:
        """Save current migration state."""
        state_path = self.config.new_data_path / f"migration_state_{self.migration_id}.json"
        state_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(state_path, "w") as f:
            json.dump(asdict(self.state), f, indent=2, default=str)
            
    def _determine_source_type(self, data_type: str) -> str:
        """Determine source type for migration based on data type."""
        mapping = {
            "integrated_panel": "wfp_prices",
            "price_data": "wfp_prices",
            "exchange_rates": "exchange_rates",
            "control_zones": "control_zones",
            "conflict_data": "conflict"
        }
        return mapping.get(data_type, "generic")
    
    async def get_migration_status(self) -> Dict[str, Any]:
        """Get current migration status."""
        duration = datetime.now() - self.state.start_time
        
        return {
            "migration_id": self.migration_id,
            "status": self.state.status,
            "current_phase": self.state.current_phase,
            "progress_pct": self.state.progress_pct,
            "duration_seconds": duration.total_seconds(),
            "records_processed": self.state.records_processed,
            "records_migrated": self.state.records_migrated,
            "records_failed": self.state.records_failed,
            "validation_passed": self.state.validation_passed,
            "error_count": len(self.state.errors),
            "warning_count": len(self.state.warnings)
        }
    
    # Add property to make data_inventory accessible
    @property
    def data_inventory(self) -> Dict[str, Path]:
        """Get data inventory from analysis phase."""
        return getattr(self, '_data_inventory', {})
    
    @data_inventory.setter 
    def data_inventory(self, value: Dict[str, Path]) -> None:
        """Set data inventory."""
        self._data_inventory = value