"""Parallel validation system for comparing old and new pipeline outputs."""

import asyncio
from pathlib import Path
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Tuple, Any, Callable
import pandas as pd
import numpy as np
import json
from src.infrastructure.logging import Logger
from dataclasses import dataclass, field, asdict
from enum import Enum
import hashlib
from concurrent.futures import <PERSON><PERSON>oolExecutor, ThreadPoolExecutor

from src.infrastructure.processors.panel_builder import PanelBuilder
from src.application.services.data_pipeline_orchestrator import (
    DataPipelineOrchestrator,
    PipelineConfig,
    PipelineResult
)
from src.infrastructure.data_quality.validation_framework import (
    ValidationFramework,
    ValidationLevel,
    ValidationReport
)
from src.infrastructure.observability.monitoring import MetricsCollector
from src.infrastructure.external_services.hdx_client import HDXClient
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeRemainingColumn


logger = Logger(__name__)
console = Console()


class ComparisonMetric(Enum):
    """Types of comparison metrics."""
    ROW_COUNT = "row_count"
    COLUMN_COUNT = "column_count" 
    SCHEMA_MATCH = "schema_match"
    VALUE_CORRELATION = "value_correlation"
    MISSING_DATA = "missing_data"
    DISTRIBUTION = "distribution"
    AGGREGATES = "aggregates"
    CHECKSUM = "checksum"


@dataclass
class ComparisonResult:
    """Result of comparing a single metric."""
    metric: ComparisonMetric
    passed: bool
    old_value: Any
    new_value: Any
    difference: Optional[float] = None
    threshold: Optional[float] = None
    details: Dict[str, Any] = field(default_factory=dict)


@dataclass
class DatasetComparison:
    """Comparison results for a dataset."""
    dataset_name: str
    overall_match: bool
    match_percentage: float
    comparison_results: List[ComparisonResult]
    recommendations: List[str]
    timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class ParallelValidationConfig:
    """Configuration for parallel validation."""
    # Comparison thresholds
    row_count_tolerance: float = 0.01  # 1% tolerance
    value_correlation_threshold: float = 0.99  # 99% correlation required
    missing_data_tolerance: float = 0.02  # 2% tolerance
    distribution_ks_threshold: float = 0.05  # KS test p-value threshold
    
    # Processing options
    sample_size: Optional[int] = None  # Use full data by default
    parallel_workers: int = 4
    comparison_timeout_minutes: int = 30
    
    # Output options
    generate_reports: bool = True
    report_format: str = "html"  # html, json, markdown
    report_output_dir: Path = Path("reports/parallel_validation")
    
    # Datasets to compare
    datasets_to_compare: List[str] = field(default_factory=lambda: [
        "integrated_panel",
        "price_observations", 
        "exchange_rates",
        "control_zones",
        "conflict_events"
    ])


class ParallelValidator:
    """Validates data pipeline outputs by running old and new in parallel."""
    
    def __init__(self, config: ParallelValidationConfig):
        """Initialize parallel validator."""
        self.config = config
        self.validation_id = f"validation_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.metrics_collector = MetricsCollector()
        
        # Ensure output directory exists
        self.config.report_output_dir.mkdir(parents=True, exist_ok=True)
        
        # Executors for parallel processing
        self.thread_executor = ThreadPoolExecutor(max_workers=config.parallel_workers)
        self.process_executor = ProcessPoolExecutor(max_workers=config.parallel_workers)
        
    async def validate_pipelines(
        self,
        old_pipeline_config: PipelineConfig,
        new_pipeline_config: PipelineConfig
    ) -> Dict[str, DatasetComparison]:
        """Run both pipelines and compare outputs."""
        console.print(f"[bold blue]Starting parallel validation: {self.validation_id}[/bold blue]")
        
        try:
            # Step 1: Run both pipelines in parallel
            console.print("[yellow]Step 1: Running pipelines in parallel...[/yellow]")
            old_result, new_result = await self._run_pipelines_parallel(
                old_pipeline_config, 
                new_pipeline_config
            )
            
            # Step 2: Compare outputs
            console.print("[yellow]Step 2: Comparing pipeline outputs...[/yellow]")
            comparison_results = await self._compare_pipeline_outputs(
                old_result,
                new_result
            )
            
            # Step 3: Generate validation report
            if self.config.generate_reports:
                console.print("[yellow]Step 3: Generating validation report...[/yellow]")
                await self._generate_validation_report(comparison_results)
                
            # Step 4: Display summary
            self._display_validation_summary(comparison_results)
            
            return comparison_results
            
        except Exception as e:
            logger.error(f"Parallel validation failed: {str(e)}")
            raise
        finally:
            # Cleanup
            self.thread_executor.shutdown(wait=False)
            self.process_executor.shutdown(wait=False)
            
    async def _run_pipelines_parallel(
        self,
        old_config: PipelineConfig,
        new_config: PipelineConfig
    ) -> Tuple[PipelineResult, PipelineResult]:
        """Run old and new pipelines in parallel."""
        # Create pipeline instances
        old_pipeline = DataPipelineOrchestrator(old_config)
        new_pipeline = DataPipelineOrchestrator(new_config)
        
        # Run in parallel with progress tracking
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TimeRemainingColumn(),
            console=console
        ) as progress:
            old_task = progress.add_task("[cyan]Running old pipeline...", total=100)
            new_task = progress.add_task("[green]Running new pipeline...", total=100)
            
            # Define progress callbacks
            def old_progress_callback(status: PipelineStatus):
                progress.update(old_task, completed=status.progress_percentage)
                
            def new_progress_callback(status: PipelineStatus):
                progress.update(new_task, completed=status.progress_percentage)
                
            # Add callbacks
            old_pipeline.add_progress_callback(old_progress_callback)
            new_pipeline.add_progress_callback(new_progress_callback)
            
            # Run pipelines
            old_task_coro = old_pipeline.run_pipeline()
            new_task_coro = new_pipeline.run_pipeline()
            
            old_result, new_result = await asyncio.gather(
                old_task_coro,
                new_task_coro
            )
            
        return old_result, new_result
        
    async def _compare_pipeline_outputs(
        self,
        old_result: PipelineResult,
        new_result: PipelineResult
    ) -> Dict[str, DatasetComparison]:
        """Compare outputs from both pipelines."""
        comparison_results = {}
        
        # Compare each dataset type
        for dataset_name in self.config.datasets_to_compare:
            console.print(f"[cyan]Comparing {dataset_name}...[/cyan]")
            
            # Get data paths
            old_path = self._get_dataset_path(old_result, dataset_name)
            new_path = self._get_dataset_path(new_result, dataset_name)
            
            if not old_path or not new_path:
                console.print(f"[red]Skipping {dataset_name} - data not found[/red]")
                continue
                
            # Load datasets
            old_df = await self._load_dataset(old_path)
            new_df = await self._load_dataset(new_path)
            
            # Run comparisons
            comparison = await self._compare_datasets(
                dataset_name,
                old_df,
                new_df
            )
            
            comparison_results[dataset_name] = comparison
            
        return comparison_results
        
    async def _compare_datasets(
        self,
        dataset_name: str,
        old_df: pd.DataFrame,
        new_df: pd.DataFrame
    ) -> DatasetComparison:
        """Compare two datasets comprehensively."""
        comparison_results = []
        
        # 1. Row count comparison
        row_count_result = self._compare_row_counts(old_df, new_df)
        comparison_results.append(row_count_result)
        
        # 2. Column/Schema comparison  
        schema_result = self._compare_schemas(old_df, new_df)
        comparison_results.append(schema_result)
        
        # 3. Value correlation (for numeric columns)
        if row_count_result.passed and schema_result.passed:
            correlation_results = await self._compare_value_correlations(old_df, new_df)
            comparison_results.extend(correlation_results)
            
        # 4. Missing data comparison
        missing_data_result = self._compare_missing_data(old_df, new_df)
        comparison_results.append(missing_data_result)
        
        # 5. Distribution comparison
        distribution_results = await self._compare_distributions(old_df, new_df)
        comparison_results.extend(distribution_results)
        
        # 6. Aggregate statistics comparison
        aggregate_results = self._compare_aggregates(old_df, new_df)
        comparison_results.extend(aggregate_results)
        
        # 7. Checksum comparison
        checksum_result = self._compare_checksums(old_df, new_df)
        comparison_results.append(checksum_result)
        
        # Calculate overall match
        passed_count = sum(1 for r in comparison_results if r.passed)
        match_percentage = (passed_count / len(comparison_results)) * 100
        overall_match = match_percentage >= 95  # 95% threshold
        
        # Generate recommendations
        recommendations = self._generate_recommendations(comparison_results)
        
        return DatasetComparison(
            dataset_name=dataset_name,
            overall_match=overall_match,
            match_percentage=match_percentage,
            comparison_results=comparison_results,
            recommendations=recommendations
        )
        
    def _compare_row_counts(self, old_df: pd.DataFrame, new_df: pd.DataFrame) -> ComparisonResult:
        """Compare row counts between datasets."""
        old_count = len(old_df)
        new_count = len(new_df)
        
        if old_count == 0:
            difference = 0 if new_count == 0 else float('inf')
        else:
            difference = abs(new_count - old_count) / old_count
            
        passed = difference <= self.config.row_count_tolerance
        
        return ComparisonResult(
            metric=ComparisonMetric.ROW_COUNT,
            passed=passed,
            old_value=old_count,
            new_value=new_count,
            difference=difference,
            threshold=self.config.row_count_tolerance,
            details={
                "absolute_difference": abs(new_count - old_count),
                "percentage_difference": difference * 100
            }
        )
        
    def _compare_schemas(self, old_df: pd.DataFrame, new_df: pd.DataFrame) -> ComparisonResult:
        """Compare schemas between datasets."""
        old_columns = set(old_df.columns)
        new_columns = set(new_df.columns)
        
        missing_in_new = old_columns - new_columns
        added_in_new = new_columns - old_columns
        
        # Check data types for common columns
        common_columns = old_columns & new_columns
        type_mismatches = {}
        
        for col in common_columns:
            old_dtype = str(old_df[col].dtype)
            new_dtype = str(new_df[col].dtype)
            if old_dtype != new_dtype:
                type_mismatches[col] = {
                    "old_type": old_dtype,
                    "new_type": new_dtype
                }
                
        passed = len(missing_in_new) == 0 and len(type_mismatches) == 0
        
        return ComparisonResult(
            metric=ComparisonMetric.SCHEMA_MATCH,
            passed=passed,
            old_value=list(old_columns),
            new_value=list(new_columns),
            details={
                "missing_columns": list(missing_in_new),
                "added_columns": list(added_in_new),
                "type_mismatches": type_mismatches
            }
        )
        
    async def _compare_value_correlations(
        self, 
        old_df: pd.DataFrame, 
        new_df: pd.DataFrame
    ) -> List[ComparisonResult]:
        """Compare value correlations for numeric columns."""
        results = []
        
        # Align dataframes by index if possible
        if len(old_df) == len(new_df):
            numeric_columns = old_df.select_dtypes(include=[np.number]).columns
            numeric_columns = [col for col in numeric_columns if col in new_df.columns]
            
            for col in numeric_columns:
                try:
                    correlation = old_df[col].corr(new_df[col])
                    passed = correlation >= self.config.value_correlation_threshold
                    
                    results.append(ComparisonResult(
                        metric=ComparisonMetric.VALUE_CORRELATION,
                        passed=passed,
                        old_value=f"{col}_values",
                        new_value=f"{col}_values",
                        difference=1 - correlation if not np.isnan(correlation) else 1.0,
                        threshold=self.config.value_correlation_threshold,
                        details={
                            "column": col,
                            "correlation": correlation,
                            "sample_old_values": old_df[col].head().tolist(),
                            "sample_new_values": new_df[col].head().tolist()
                        }
                    ))
                except Exception as e:
                    logger.warning(f"Failed to calculate correlation for {col}: {str(e)}")
                    
        return results
        
    def _compare_missing_data(self, old_df: pd.DataFrame, new_df: pd.DataFrame) -> ComparisonResult:
        """Compare missing data patterns."""
        old_missing_pct = (old_df.isna().sum().sum() / (len(old_df) * len(old_df.columns))) * 100
        new_missing_pct = (new_df.isna().sum().sum() / (len(new_df) * len(new_df.columns))) * 100
        
        difference = abs(new_missing_pct - old_missing_pct)
        passed = difference <= self.config.missing_data_tolerance * 100
        
        # Column-wise missing data
        old_missing_by_col = old_df.isna().sum().to_dict()
        new_missing_by_col = new_df.isna().sum().to_dict()
        
        return ComparisonResult(
            metric=ComparisonMetric.MISSING_DATA,
            passed=passed,
            old_value=old_missing_pct,
            new_value=new_missing_pct,
            difference=difference,
            threshold=self.config.missing_data_tolerance * 100,
            details={
                "old_missing_by_column": old_missing_by_col,
                "new_missing_by_column": new_missing_by_col
            }
        )
        
    async def _compare_distributions(
        self, 
        old_df: pd.DataFrame, 
        new_df: pd.DataFrame
    ) -> List[ComparisonResult]:
        """Compare distributions using statistical tests."""
        results = []
        
        numeric_columns = old_df.select_dtypes(include=[np.number]).columns
        numeric_columns = [col for col in numeric_columns if col in new_df.columns]
        
        for col in numeric_columns[:5]:  # Limit to first 5 columns for performance
            try:
                from scipy import stats
                
                # Kolmogorov-Smirnov test
                ks_statistic, p_value = stats.ks_2samp(
                    old_df[col].dropna(),
                    new_df[col].dropna()
                )
                
                passed = p_value >= self.config.distribution_ks_threshold
                
                results.append(ComparisonResult(
                    metric=ComparisonMetric.DISTRIBUTION,
                    passed=passed,
                    old_value=f"{col}_distribution",
                    new_value=f"{col}_distribution",
                    difference=ks_statistic,
                    threshold=self.config.distribution_ks_threshold,
                    details={
                        "column": col,
                        "ks_statistic": ks_statistic,
                        "p_value": p_value,
                        "old_mean": old_df[col].mean(),
                        "new_mean": new_df[col].mean(),
                        "old_std": old_df[col].std(),
                        "new_std": new_df[col].std()
                    }
                ))
            except Exception as e:
                logger.warning(f"Failed to compare distribution for {col}: {str(e)}")
                
        return results
        
    def _compare_aggregates(self, old_df: pd.DataFrame, new_df: pd.DataFrame) -> List[ComparisonResult]:
        """Compare aggregate statistics."""
        results = []
        
        # Compare basic aggregates
        aggregates = ['mean', 'sum', 'std', 'min', 'max']
        numeric_columns = old_df.select_dtypes(include=[np.number]).columns
        numeric_columns = [col for col in numeric_columns if col in new_df.columns]
        
        for col in numeric_columns[:3]:  # Limit for performance
            for agg in aggregates:
                try:
                    old_val = getattr(old_df[col], agg)()
                    new_val = getattr(new_df[col], agg)()
                    
                    if old_val != 0:
                        difference = abs(new_val - old_val) / abs(old_val)
                    else:
                        difference = 0 if new_val == 0 else float('inf')
                        
                    passed = difference <= 0.01  # 1% tolerance
                    
                    results.append(ComparisonResult(
                        metric=ComparisonMetric.AGGREGATES,
                        passed=passed,
                        old_value=old_val,
                        new_value=new_val,
                        difference=difference,
                        threshold=0.01,
                        details={
                            "column": col,
                            "aggregate": agg
                        }
                    ))
                except Exception as e:
                    logger.warning(f"Failed to compare {agg} for {col}: {str(e)}")
                    
        return results
        
    def _compare_checksums(self, old_df: pd.DataFrame, new_df: pd.DataFrame) -> ComparisonResult:
        """Compare checksums of datasets."""
        # Convert to string representation for hashing
        old_str = old_df.to_csv(index=False).encode()
        new_str = new_df.to_csv(index=False).encode()
        
        old_checksum = hashlib.md5(old_str).hexdigest()
        new_checksum = hashlib.md5(new_str).hexdigest()
        
        passed = old_checksum == new_checksum
        
        return ComparisonResult(
            metric=ComparisonMetric.CHECKSUM,
            passed=passed,
            old_value=old_checksum,
            new_value=new_checksum,
            details={
                "exact_match": passed
            }
        )
        
    def _generate_recommendations(self, results: List[ComparisonResult]) -> List[str]:
        """Generate recommendations based on comparison results."""
        recommendations = []
        
        # Check for critical failures
        row_count_fail = any(r.metric == ComparisonMetric.ROW_COUNT and not r.passed for r in results)
        schema_fail = any(r.metric == ComparisonMetric.SCHEMA_MATCH and not r.passed for r in results)
        
        if row_count_fail:
            recommendations.append("Critical: Row count mismatch detected. Investigate data loss or duplication.")
            
        if schema_fail:
            recommendations.append("Critical: Schema mismatch detected. Review column mappings and data types.")
            
        # Check for data quality issues
        correlation_failures = [r for r in results if r.metric == ComparisonMetric.VALUE_CORRELATION and not r.passed]
        if correlation_failures:
            columns = [r.details.get('column') for r in correlation_failures]
            recommendations.append(f"Warning: Low correlation in columns: {', '.join(columns)}. Review transformation logic.")
            
        # Distribution changes
        distribution_failures = [r for r in results if r.metric == ComparisonMetric.DISTRIBUTION and not r.passed]
        if distribution_failures:
            recommendations.append("Info: Distribution changes detected. May be due to different processing methods.")
            
        if not recommendations:
            recommendations.append("Success: All validation checks passed. New pipeline output matches old pipeline.")
            
        return recommendations
        
    async def _generate_validation_report(
        self, 
        comparison_results: Dict[str, DatasetComparison]
    ) -> None:
        """Generate comprehensive validation report."""
        report_data = {
            "validation_id": self.validation_id,
            "timestamp": datetime.now().isoformat(),
            "summary": self._generate_summary(comparison_results),
            "detailed_results": {}
        }
        
        # Add detailed results for each dataset
        for dataset_name, comparison in comparison_results.items():
            report_data["detailed_results"][dataset_name] = {
                "overall_match": comparison.overall_match,
                "match_percentage": comparison.match_percentage,
                "recommendations": comparison.recommendations,
                "metrics": [
                    {
                        "metric": r.metric.value,
                        "passed": r.passed,
                        "old_value": str(r.old_value)[:100],  # Truncate for readability
                        "new_value": str(r.new_value)[:100],
                        "difference": r.difference,
                        "threshold": r.threshold,
                        "details": r.details
                    }
                    for r in comparison.comparison_results
                ]
            }
            
        # Save report
        if self.config.report_format == "json":
            report_path = self.config.report_output_dir / f"validation_report_{self.validation_id}.json"
            with open(report_path, "w") as f:
                json.dump(report_data, f, indent=2, default=str)
                
        elif self.config.report_format == "html":
            report_path = self.config.report_output_dir / f"validation_report_{self.validation_id}.html"
            html_content = self._generate_html_report(report_data)
            with open(report_path, "w") as f:
                f.write(html_content)
                
        console.print(f"[green]Validation report saved to: {report_path}[/green]")
        
    def _generate_summary(self, comparison_results: Dict[str, DatasetComparison]) -> Dict[str, Any]:
        """Generate summary statistics."""
        total_datasets = len(comparison_results)
        passed_datasets = sum(1 for c in comparison_results.values() if c.overall_match)
        
        all_metrics = []
        for comparison in comparison_results.values():
            all_metrics.extend(comparison.comparison_results)
            
        total_checks = len(all_metrics)
        passed_checks = sum(1 for m in all_metrics if m.passed)
        
        return {
            "total_datasets": total_datasets,
            "passed_datasets": passed_datasets,
            "dataset_success_rate": (passed_datasets / total_datasets * 100) if total_datasets > 0 else 0,
            "total_checks": total_checks,
            "passed_checks": passed_checks,
            "check_success_rate": (passed_checks / total_checks * 100) if total_checks > 0 else 0,
            "validation_passed": passed_datasets == total_datasets
        }
        
    def _generate_html_report(self, report_data: Dict[str, Any]) -> str:
        """Generate HTML report."""
        summary = report_data['summary']
        
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Pipeline Validation Report - {report_data['validation_id']}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .summary {{ background: #f0f0f0; padding: 20px; border-radius: 5px; }}
                .passed {{ color: green; font-weight: bold; }}
                .failed {{ color: red; font-weight: bold; }}
                table {{ border-collapse: collapse; width: 100%; margin-top: 20px; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #4CAF50; color: white; }}
                tr:nth-child(even) {{ background-color: #f2f2f2; }}
                .metric-passed {{ background-color: #d4edda; }}
                .metric-failed {{ background-color: #f8d7da; }}
            </style>
        </head>
        <body>
            <h1>Pipeline Validation Report</h1>
            <p>Validation ID: {report_data['validation_id']}</p>
            <p>Generated: {report_data['timestamp']}</p>
            
            <div class="summary">
                <h2>Summary</h2>
                <p>Overall Status: <span class="{'passed' if summary['validation_passed'] else 'failed'}">
                    {'PASSED' if summary['validation_passed'] else 'FAILED'}
                </span></p>
                <p>Datasets Passed: {summary['passed_datasets']}/{summary['total_datasets']} 
                   ({summary['dataset_success_rate']:.1f}%)</p>
                <p>Checks Passed: {summary['passed_checks']}/{summary['total_checks']} 
                   ({summary['check_success_rate']:.1f}%)</p>
            </div>
            
            <h2>Detailed Results</h2>
        """
        
        for dataset_name, results in report_data['detailed_results'].items():
            html += f"""
            <h3>{dataset_name}</h3>
            <p>Match Percentage: {results['match_percentage']:.1f}%</p>
            <p>Recommendations:</p>
            <ul>
            """
            for rec in results['recommendations']:
                html += f"<li>{rec}</li>"
            html += "</ul>"
            
            html += """
            <table>
                <tr>
                    <th>Metric</th>
                    <th>Status</th>
                    <th>Difference</th>
                    <th>Threshold</th>
                    <th>Details</th>
                </tr>
            """
            
            for metric in results['metrics']:
                row_class = 'metric-passed' if metric['passed'] else 'metric-failed'
                status = '✓' if metric['passed'] else '✗'
                
                html += f"""
                <tr class="{row_class}">
                    <td>{metric['metric']}</td>
                    <td>{status}</td>
                    <td>{metric.get('difference', 'N/A'):.4f if isinstance(metric.get('difference'), float) else 'N/A'}</td>
                    <td>{metric.get('threshold', 'N/A')}</td>
                    <td>{str(metric.get('details', {}))[:100]}...</td>
                </tr>
                """
                
            html += "</table>"
            
        html += """
        </body>
        </html>
        """
        
        return html
        
    def _display_validation_summary(self, comparison_results: Dict[str, DatasetComparison]) -> None:
        """Display validation summary in console."""
        table = Table(title="Parallel Validation Summary")
        table.add_column("Dataset", style="cyan")
        table.add_column("Match %", style="magenta")
        table.add_column("Status", style="bold")
        table.add_column("Key Issues", style="yellow")
        
        for dataset_name, comparison in comparison_results.items():
            status = "[green]PASS[/green]" if comparison.overall_match else "[red]FAIL[/red]"
            
            # Find key issues
            failed_metrics = [r.metric.value for r in comparison.comparison_results if not r.passed]
            issues = ", ".join(failed_metrics[:3]) if failed_metrics else "None"
            
            table.add_row(
                dataset_name,
                f"{comparison.match_percentage:.1f}%",
                status,
                issues
            )
            
        console.print(table)
        
        # Overall summary
        total_datasets = len(comparison_results)
        passed_datasets = sum(1 for c in comparison_results.values() if c.overall_match)
        
        console.print(f"\n[bold]Overall Result: {passed_datasets}/{total_datasets} datasets passed[/bold]")
        
        if passed_datasets == total_datasets:
            console.print("[bold green]✓ All validation checks passed![/bold green]")
        else:
            console.print("[bold red]✗ Some validation checks failed. Review the detailed report.[/bold red]")
            
    def _get_dataset_path(self, pipeline_result: PipelineResult, dataset_name: str) -> Optional[Path]:
        """Get the path to a dataset from pipeline results."""
        # This would be implemented based on your pipeline result structure
        # For now, return a placeholder
        if hasattr(pipeline_result, 'output_paths'):
            return pipeline_result.output_paths.get(dataset_name)
        return None
        
    async def _load_dataset(self, path: Path) -> pd.DataFrame:
        """Load dataset from path."""
        if path.suffix == '.parquet':
            return pd.read_parquet(path)
        elif path.suffix == '.csv':
            return pd.read_csv(path)
        else:
            raise ValueError(f"Unsupported file format: {path.suffix}")
            
    async def compare_specific_datasets(
        self,
        old_data_path: Path,
        new_data_path: Path,
        dataset_name: str
    ) -> DatasetComparison:
        """Compare specific datasets directly without running full pipelines."""
        console.print(f"[cyan]Comparing {dataset_name} datasets...[/cyan]")
        
        # Load datasets
        old_df = await self._load_dataset(old_data_path)
        new_df = await self._load_dataset(new_data_path)
        
        # Run comparison
        comparison = await self._compare_datasets(dataset_name, old_df, new_df)
        
        # Display results
        self._display_validation_summary({dataset_name: comparison})
        
        return comparison