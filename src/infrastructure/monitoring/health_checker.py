"""Health checking system for pipeline components."""

import asyncio
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Callable, Dict, List, Optional
from pathlib import Path

from ...core.utils.logging import get_logger
from .metrics_collector import get_metrics_collector


logger = get_logger(__name__)


class HealthStatus(Enum):
    """Health status levels."""
    
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"


@dataclass
class HealthCheckResult:
    """Result of a health check."""
    
    component: str
    status: HealthStatus
    message: str = ""
    details: Dict[str, any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.utcnow)
    response_time_ms: Optional[float] = None
    
    @property
    def is_healthy(self) -> bool:
        return self.status == HealthStatus.HEALTHY


@dataclass
class ComponentHealth:
    """Health tracking for a component."""
    
    name: str
    check_function: Callable
    timeout_seconds: int = 30
    consecutive_failures: int = 0
    last_check: Optional[datetime] = None
    last_result: Optional[HealthCheckResult] = None
    failure_threshold: int = 3
    
    @property
    def is_critical(self) -> bool:
        """Check if component has exceeded failure threshold."""
        return self.consecutive_failures >= self.failure_threshold


class HealthChecker:
    """Health checking system for pipeline components."""
    
    def __init__(
        self,
        check_interval_seconds: int = 60,
        enable_auto_checks: bool = True
    ):
        """Initialize health checker.
        
        Args:
            check_interval_seconds: How often to run health checks
            enable_auto_checks: Whether to run checks automatically
        """
        self.check_interval = check_interval_seconds
        self.enable_auto_checks = enable_auto_checks
        self.components: Dict[str, ComponentHealth] = {}
        self._check_task: Optional[asyncio.Task] = None
        self.logger = logger
        self.metrics_collector = get_metrics_collector()
        
        # Register default health checks
        self._register_default_checks()
    
    def _register_default_checks(self):
        """Register default health checks."""
        # Data directory check
        self.register_component(
            "data_directory",
            self._check_data_directory,
            timeout_seconds=5
        )
        
        # Disk space check
        self.register_component(
            "disk_space",
            self._check_disk_space,
            timeout_seconds=5
        )
        
        # Memory check
        self.register_component(
            "memory",
            self._check_memory,
            timeout_seconds=5
        )
        
        # Process count check
        self.register_component(
            "process_count",
            self._check_process_count,
            timeout_seconds=5
        )
    
    def register_component(
        self,
        name: str,
        check_function: Callable,
        timeout_seconds: int = 30,
        failure_threshold: int = 3
    ):
        """Register a component for health checking."""
        self.components[name] = ComponentHealth(
            name=name,
            check_function=check_function,
            timeout_seconds=timeout_seconds,
            failure_threshold=failure_threshold
        )
        self.logger.info(f"Registered health check for component: {name}")
    
    async def start(self):
        """Start automatic health checking."""
        if self._check_task and not self._check_task.done():
            self.logger.warning("Health checker already running")
            return
        
        if self.enable_auto_checks:
            self._check_task = asyncio.create_task(self._check_loop())
            self.logger.info("Health checker started")
    
    async def stop(self):
        """Stop automatic health checking."""
        if self._check_task:
            self._check_task.cancel()
            try:
                await self._check_task
            except asyncio.CancelledError:
                pass
        self.logger.info("Health checker stopped")
    
    async def _check_loop(self):
        """Main health check loop."""
        while True:
            try:
                await self.check_all_components()
                await asyncio.sleep(self.check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in health check loop: {e}")
                await asyncio.sleep(self.check_interval)
    
    async def check_component(self, component_name: str) -> HealthCheckResult:
        """Check health of a specific component."""
        if component_name not in self.components:
            return HealthCheckResult(
                component=component_name,
                status=HealthStatus.UNKNOWN,
                message=f"Component {component_name} not registered"
            )
        
        component = self.components[component_name]
        start_time = datetime.utcnow()
        
        try:
            # Run check with timeout
            check_coro = component.check_function()
            if asyncio.iscoroutine(check_coro):
                result = await asyncio.wait_for(
                    check_coro,
                    timeout=component.timeout_seconds
                )
            else:
                result = check_coro
            
            # Calculate response time
            response_time_ms = (datetime.utcnow() - start_time).total_seconds() * 1000
            
            # Update component state
            component.last_check = datetime.utcnow()
            component.last_result = result
            result.response_time_ms = response_time_ms
            
            if result.is_healthy:
                component.consecutive_failures = 0
            else:
                component.consecutive_failures += 1
            
            # Update metrics
            self.metrics_collector.set_gauge(
                "pipeline_health_check_status",
                1 if result.is_healthy else 0,
                labels={"component": component_name}
            )
            
            self.metrics_collector.observe_histogram(
                "pipeline_health_check_duration_ms",
                response_time_ms,
                labels={"component": component_name}
            )
            
            return result
            
        except asyncio.TimeoutError:
            component.consecutive_failures += 1
            result = HealthCheckResult(
                component=component_name,
                status=HealthStatus.UNHEALTHY,
                message=f"Health check timeout after {component.timeout_seconds}s"
            )
            component.last_result = result
            return result
            
        except Exception as e:
            component.consecutive_failures += 1
            result = HealthCheckResult(
                component=component_name,
                status=HealthStatus.UNHEALTHY,
                message=f"Health check failed: {str(e)}"
            )
            component.last_result = result
            return result
    
    async def check_all_components(self) -> Dict[str, HealthCheckResult]:
        """Check health of all registered components."""
        results = {}
        
        # Run checks concurrently
        tasks = {
            name: asyncio.create_task(self.check_component(name))
            for name in self.components
        }
        
        for name, task in tasks.items():
            try:
                results[name] = await task
            except Exception as e:
                self.logger.error(f"Error checking {name}: {e}")
                results[name] = HealthCheckResult(
                    component=name,
                    status=HealthStatus.UNKNOWN,
                    message=str(e)
                )
        
        # Update overall health metric
        healthy_count = sum(1 for r in results.values() if r.is_healthy)
        total_count = len(results)
        health_ratio = healthy_count / total_count if total_count > 0 else 0
        
        self.metrics_collector.set_gauge(
            "pipeline_health_ratio",
            health_ratio
        )
        
        return results
    
    def get_overall_status(self) -> HealthStatus:
        """Get overall system health status."""
        if not self.components:
            return HealthStatus.UNKNOWN
        
        unhealthy_count = 0
        degraded_count = 0
        
        for component in self.components.values():
            if component.last_result:
                if component.last_result.status == HealthStatus.UNHEALTHY:
                    unhealthy_count += 1
                elif component.last_result.status == HealthStatus.DEGRADED:
                    degraded_count += 1
        
        if unhealthy_count > 0:
            return HealthStatus.UNHEALTHY
        elif degraded_count > 0:
            return HealthStatus.DEGRADED
        else:
            return HealthStatus.HEALTHY
    
    def get_critical_components(self) -> List[ComponentHealth]:
        """Get components that have exceeded failure threshold."""
        return [c for c in self.components.values() if c.is_critical]
    
    # Default health check implementations
    
    async def _check_data_directory(self) -> HealthCheckResult:
        """Check if data directories are accessible."""
        data_dirs = [
            Path("data/raw"),
            Path("data/processed"),
            Path("data/interim")
        ]
        
        missing_dirs = []
        for dir_path in data_dirs:
            if not dir_path.exists():
                missing_dirs.append(str(dir_path))
        
        if missing_dirs:
            return HealthCheckResult(
                component="data_directory",
                status=HealthStatus.UNHEALTHY,
                message=f"Missing directories: {', '.join(missing_dirs)}",
                details={"missing_dirs": missing_dirs}
            )
        
        return HealthCheckResult(
            component="data_directory",
            status=HealthStatus.HEALTHY,
            message="All data directories accessible"
        )
    
    async def _check_disk_space(self) -> HealthCheckResult:
        """Check available disk space."""
        import shutil
        
        usage = shutil.disk_usage("/")
        free_gb = usage.free / (1024 ** 3)
        percent_used = (usage.used / usage.total) * 100
        
        if free_gb < 1:  # Less than 1GB free
            return HealthCheckResult(
                component="disk_space",
                status=HealthStatus.UNHEALTHY,
                message=f"Low disk space: {free_gb:.1f}GB free",
                details={"free_gb": free_gb, "percent_used": percent_used}
            )
        elif free_gb < 5:  # Less than 5GB free
            return HealthCheckResult(
                component="disk_space",
                status=HealthStatus.DEGRADED,
                message=f"Disk space warning: {free_gb:.1f}GB free",
                details={"free_gb": free_gb, "percent_used": percent_used}
            )
        
        return HealthCheckResult(
            component="disk_space",
            status=HealthStatus.HEALTHY,
            message=f"Disk space OK: {free_gb:.1f}GB free",
            details={"free_gb": free_gb, "percent_used": percent_used}
        )
    
    async def _check_memory(self) -> HealthCheckResult:
        """Check available memory."""
        import psutil
        
        memory = psutil.virtual_memory()
        available_gb = memory.available / (1024 ** 3)
        percent_used = memory.percent
        
        if percent_used > 90:
            return HealthCheckResult(
                component="memory",
                status=HealthStatus.UNHEALTHY,
                message=f"High memory usage: {percent_used:.1f}%",
                details={"available_gb": available_gb, "percent_used": percent_used}
            )
        elif percent_used > 80:
            return HealthCheckResult(
                component="memory",
                status=HealthStatus.DEGRADED,
                message=f"Memory usage warning: {percent_used:.1f}%",
                details={"available_gb": available_gb, "percent_used": percent_used}
            )
        
        return HealthCheckResult(
            component="memory",
            status=HealthStatus.HEALTHY,
            message=f"Memory OK: {available_gb:.1f}GB available",
            details={"available_gb": available_gb, "percent_used": percent_used}
        )
    
    async def _check_process_count(self) -> HealthCheckResult:
        """Check number of running processes."""
        import psutil
        
        process_count = len(psutil.pids())
        
        if process_count > 500:
            return HealthCheckResult(
                component="process_count",
                status=HealthStatus.DEGRADED,
                message=f"High process count: {process_count}",
                details={"process_count": process_count}
            )
        
        return HealthCheckResult(
            component="process_count",
            status=HealthStatus.HEALTHY,
            message=f"Process count OK: {process_count}",
            details={"process_count": process_count}
        )