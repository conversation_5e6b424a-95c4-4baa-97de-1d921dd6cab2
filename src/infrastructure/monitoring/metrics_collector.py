"""Metrics collection system for pipeline monitoring."""

import time
from collections import defaultdict, deque
from contextlib import contextmanager
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Tuple
from threading import Lock
import statistics

from src.infrastructure.logging import Logger


logger = Logger(__name__)


class MetricType(Enum):
    """Types of metrics collected."""

    COUNTER = "counter"  # Monotonically increasing
    GAUGE = "gauge"  # Can go up or down
    HISTOGRAM = "histogram"  # Distribution of values
    SUMMARY = "summary"  # Similar to histogram with percentiles


@dataclass
class MetricValue:
    """Single metric value with metadata."""

    value: float
    timestamp: datetime = field(default_factory=datetime.utcnow)
    labels: Dict[str, str] = field(default_factory=dict)


@dataclass
class Metric:
    """Metric definition and storage."""

    name: str
    metric_type: MetricType
    description: str
    unit: Optional[str] = None
    values: deque = field(default_factory=lambda: deque(maxlen=10000))
    labels: List[str] = field(default_factory=list)

    def add_value(self, value: float, labels: Optional[Dict[str, str]] = None):
        """Add a value to this metric."""
        self.values.append(MetricValue(value, labels=labels or {}))

    def get_current_value(self) -> Optional[float]:
        """Get the most recent value."""
        if self.values:
            return self.values[-1].value
        return None

    def get_statistics(self, window_minutes: int = 5) -> Dict[str, float]:
        """Get statistics for recent values."""
        cutoff = datetime.utcnow() - timedelta(minutes=window_minutes)
        recent_values = [v.value for v in self.values if v.timestamp >= cutoff]

        if not recent_values:
            return {}

        return {
            "count": len(recent_values),
            "sum": sum(recent_values),
            "mean": statistics.mean(recent_values),
            "min": min(recent_values),
            "max": max(recent_values),
            "stddev": statistics.stdev(recent_values) if len(recent_values) > 1 else 0
        }


class MetricsCollector:
    """Centralized metrics collection system."""

    def __init__(self):
        self._metrics: Dict[str, Metric] = {}
        self._lock = Lock()
        self.logger = logger

        # Register default pipeline metrics
        self._register_default_metrics()

    def _register_default_metrics(self):
        """Register default pipeline metrics."""
        # Processing metrics
        self.register_metric(
            "pipeline_records_processed_total",
            MetricType.COUNTER,
            "Total records processed",
            labels=["processor", "status"]
        )

        self.register_metric(
            "pipeline_processing_duration_seconds",
            MetricType.HISTOGRAM,
            "Processing duration in seconds",
            unit="seconds",
            labels=["processor", "stage"]
        )

        # Resource metrics
        self.register_metric(
            "pipeline_cpu_usage_percent",
            MetricType.GAUGE,
            "CPU usage percentage",
            unit="percent"
        )

        self.register_metric(
            "pipeline_memory_usage_mb",
            MetricType.GAUGE,
            "Memory usage in MB",
            unit="megabytes"
        )

        # Data quality metrics
        self.register_metric(
            "pipeline_data_quality_score",
            MetricType.GAUGE,
            "Data quality score (0-100)",
            labels=["source", "check_type"]
        )

        self.register_metric(
            "pipeline_validation_errors_total",
            MetricType.COUNTER,
            "Total validation errors",
            labels=["source", "error_type"]
        )

        # Pipeline state metrics
        self.register_metric(
            "pipeline_active_executions",
            MetricType.GAUGE,
            "Number of active pipeline executions"
        )

        self.register_metric(
            "pipeline_checkpoint_saves_total",
            MetricType.COUNTER,
            "Total checkpoint saves",
            labels=["pipeline_id", "status"]
        )

    def register_metric(
        self,
        name: str,
        metric_type: MetricType,
        description: str,
        unit: Optional[str] = None,
        labels: Optional[List[str]] = None
    ) -> Metric:
        """Register a new metric."""
        with self._lock:
            if name in self._metrics:
                self.logger.warning(f"Metric {name} already registered")
                return self._metrics[name]

            metric = Metric(
                name=name,
                metric_type=metric_type,
                description=description,
                unit=unit,
                labels=labels or []
            )
            self._metrics[name] = metric
            self.logger.info(f"Registered metric: {name}")
            return metric

    def record_value(
        self,
        metric_name: str,
        value: float,
        labels: Optional[Dict[str, str]] = None
    ):
        """Record a value for a metric."""
        with self._lock:
            if metric_name not in self._metrics:
                self.logger.error(f"Metric {metric_name} not registered")
                return

            metric = self._metrics[metric_name]

            # Validate counter metrics only increase
            if metric.metric_type == MetricType.COUNTER:
                current = metric.get_current_value()
                if current is not None and value < current:
                    self.logger.error(
                        f"Counter {metric_name} cannot decrease: {current} -> {value}"
                    )
                    return

            metric.add_value(value, labels)

    def increment_counter(
        self,
        metric_name: str,
        increment: float = 1.0,
        labels: Optional[Dict[str, str]] = None
    ):
        """Increment a counter metric."""
        with self._lock:
            if metric_name not in self._metrics:
                self.logger.error(f"Metric {metric_name} not registered")
                return

            metric = self._metrics[metric_name]
            if metric.metric_type != MetricType.COUNTER:
                self.logger.error(f"Metric {metric_name} is not a counter")
                return

            current = metric.get_current_value() or 0
            metric.add_value(current + increment, labels)

    def set_gauge(
        self,
        metric_name: str,
        value: float,
        labels: Optional[Dict[str, str]] = None
    ):
        """Set a gauge metric value."""
        self.record_value(metric_name, value, labels)

    def observe_histogram(
        self,
        metric_name: str,
        value: float,
        labels: Optional[Dict[str, str]] = None
    ):
        """Observe a value for histogram metric."""
        self.record_value(metric_name, value, labels)

    @contextmanager
    def timer(
        self,
        metric_name: str,
        labels: Optional[Dict[str, str]] = None
    ):
        """Context manager to time operations."""
        start_time = time.time()
        try:
            yield
        finally:
            duration = time.time() - start_time
            self.observe_histogram(metric_name, duration, labels)

    def get_metric(self, metric_name: str) -> Optional[Metric]:
        """Get a metric by name."""
        return self._metrics.get(metric_name)

    def get_all_metrics(self) -> Dict[str, Metric]:
        """Get all registered metrics."""
        with self._lock:
            return dict(self._metrics)

    def get_metric_summary(
        self,
        metric_name: str,
        window_minutes: int = 5
    ) -> Dict[str, Any]:
        """Get summary statistics for a metric."""
        with self._lock:
            metric = self._metrics.get(metric_name)
            if not metric:
                return {}

            stats = metric.get_statistics(window_minutes)
            return {
                "name": metric.name,
                "type": metric.metric_type.value,
                "description": metric.description,
                "unit": metric.unit,
                "current_value": metric.get_current_value(),
                "statistics": stats
            }

    def get_metrics_by_type(self, metric_type: MetricType) -> List[Metric]:
        """Get all metrics of a specific type."""
        with self._lock:
            return [
                metric for metric in self._metrics.values()
                if metric.metric_type == metric_type
            ]

    def clear_old_values(self, older_than_hours: int = 24):
        """Clear metric values older than specified hours."""
        cutoff = datetime.utcnow() - timedelta(hours=older_than_hours)

        with self._lock:
            for metric in self._metrics.values():
                # Create new deque with recent values only
                recent_values = deque(
                    (v for v in metric.values if v.timestamp >= cutoff),
                    maxlen=metric.values.maxlen
                )
                metric.values = recent_values

        self.logger.info(f"Cleared metric values older than {older_than_hours} hours")

    def export_metrics(self) -> Dict[str, List[Dict[str, Any]]]:
        """Export all metrics in a structured format."""
        with self._lock:
            export_data = {}

            for metric_name, metric in self._metrics.items():
                metric_data = []

                for value in metric.values:
                    metric_data.append({
                        "timestamp": value.timestamp.isoformat(),
                        "value": value.value,
                        "labels": value.labels
                    })

                export_data[metric_name] = {
                    "type": metric.metric_type.value,
                    "description": metric.description,
                    "unit": metric.unit,
                    "labels": metric.labels,
                    "values": metric_data
                }

            return export_data


# Global metrics collector instance
_metrics_collector = None


def get_metrics_collector() -> MetricsCollector:
    """Get the global metrics collector instance."""
    global _metrics_collector
    if _metrics_collector is None:
        _metrics_collector = MetricsCollector()
    return _metrics_collector
