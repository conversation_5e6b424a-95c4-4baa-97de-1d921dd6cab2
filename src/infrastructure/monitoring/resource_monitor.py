"""Resource monitoring for pipeline execution."""

import asyncio
import psutil
import threading
from datetime import datetime
from typing import Callable, Dict, List, Optional

from ...core.domain.pipeline.value_objects import ResourceMetrics
from ...core.utils.logging import get_logger
from .metrics_collector import get_metrics_collector


logger = get_logger(__name__)


class ResourceMonitor:
    """Monitor system resource usage during pipeline execution."""
    
    def __init__(
        self,
        check_interval_seconds: int = 30,
        history_size: int = 1000
    ):
        """Initialize resource monitor.
        
        Args:
            check_interval_seconds: How often to collect metrics
            history_size: Maximum number of historical metrics to keep
        """
        self.check_interval = check_interval_seconds
        self.history_size = history_size
        self.metrics_history: List[ResourceMetrics] = []
        self._monitoring = False
        self._monitor_thread: Optional[threading.Thread] = None
        self._callbacks: List[Callable[[ResourceMetrics], None]] = []
        self.logger = logger
        self.metrics_collector = get_metrics_collector()
        
        # Get process handle
        self.process = psutil.Process()
    
    def start_monitoring(self):
        """Start resource monitoring in background thread."""
        if self._monitoring:
            self.logger.warning("Resource monitoring already running")
            return
        
        self._monitoring = True
        self._monitor_thread = threading.Thread(
            target=self._monitor_loop,
            daemon=True
        )
        self._monitor_thread.start()
        self.logger.info("Resource monitoring started")
    
    def stop_monitoring(self):
        """Stop resource monitoring."""
        self._monitoring = False
        if self._monitor_thread:
            self._monitor_thread.join(timeout=5)
        self.logger.info("Resource monitoring stopped")
    
    def add_callback(self, callback: Callable[[ResourceMetrics], None]):
        """Add a callback to be called when new metrics are collected."""
        self._callbacks.append(callback)
    
    def _monitor_loop(self):
        """Main monitoring loop running in background thread."""
        while self._monitoring:
            try:
                # Collect metrics
                metrics = self._collect_metrics()
                
                # Store in history
                self.metrics_history.append(metrics)
                if len(self.metrics_history) > self.history_size:
                    self.metrics_history.pop(0)
                
                # Update metrics collector
                self._update_metrics_collector(metrics)
                
                # Call callbacks
                for callback in self._callbacks:
                    try:
                        callback(metrics)
                    except Exception as e:
                        self.logger.error(f"Callback error: {e}")
                
                # Check for high resource usage
                if metrics.is_high_load:
                    self.logger.warning(
                        f"High resource usage detected: "
                        f"CPU={metrics.cpu_percent}%, "
                        f"Memory={metrics.memory_percent}%"
                    )
                
            except Exception as e:
                self.logger.error(f"Error collecting metrics: {e}")
            
            # Sleep until next check
            threading.Event().wait(self.check_interval)
    
    def _collect_metrics(self) -> ResourceMetrics:
        """Collect current resource metrics."""
        # CPU usage (over 1 second interval)
        cpu_percent = self.process.cpu_percent(interval=1)
        
        # Memory usage
        memory_info = self.process.memory_info()
        memory_mb = memory_info.rss / (1024 * 1024)
        memory_percent = self.process.memory_percent()
        
        # Disk I/O
        try:
            io_counters = self.process.io_counters()
            disk_io_mb = (io_counters.read_bytes + io_counters.write_bytes) / (1024 * 1024)
        except Exception:
            disk_io_mb = 0.0
        
        # Network I/O (system-wide as process-level not always available)
        try:
            net_io = psutil.net_io_counters()
            network_io_mb = (net_io.bytes_sent + net_io.bytes_recv) / (1024 * 1024)
        except Exception:
            network_io_mb = 0.0
        
        return ResourceMetrics(
            cpu_percent=cpu_percent,
            memory_mb=memory_mb,
            memory_percent=memory_percent,
            disk_io_mb=disk_io_mb,
            network_io_mb=network_io_mb,
            timestamp=datetime.utcnow()
        )
    
    def _update_metrics_collector(self, metrics: ResourceMetrics):
        """Update the global metrics collector with resource data."""
        prometheus_metrics = metrics.to_prometheus_format()
        
        for metric_name, value in prometheus_metrics.items():
            self.metrics_collector.set_gauge(metric_name, value)
    
    def get_current_metrics(self) -> Optional[ResourceMetrics]:
        """Get the most recent metrics."""
        if self.metrics_history:
            return self.metrics_history[-1]
        return None
    
    def get_average_metrics(self, window_seconds: int = 300) -> Optional[ResourceMetrics]:
        """Get average metrics over a time window."""
        if not self.metrics_history:
            return None
        
        cutoff = datetime.utcnow()
        recent_metrics = [
            m for m in self.metrics_history
            if (cutoff - m.timestamp).total_seconds() <= window_seconds
        ]
        
        if not recent_metrics:
            return None
        
        # Calculate averages
        avg_cpu = sum(m.cpu_percent for m in recent_metrics) / len(recent_metrics)
        avg_memory_mb = sum(m.memory_mb for m in recent_metrics) / len(recent_metrics)
        avg_memory_pct = sum(m.memory_percent for m in recent_metrics) / len(recent_metrics)
        avg_disk_io = sum(m.disk_io_mb for m in recent_metrics) / len(recent_metrics)
        avg_network_io = sum(m.network_io_mb for m in recent_metrics) / len(recent_metrics)
        
        return ResourceMetrics(
            cpu_percent=avg_cpu,
            memory_mb=avg_memory_mb,
            memory_percent=avg_memory_pct,
            disk_io_mb=avg_disk_io,
            network_io_mb=avg_network_io,
            timestamp=datetime.utcnow()
        )
    
    def get_peak_metrics(self) -> Optional[ResourceMetrics]:
        """Get peak resource usage from history."""
        if not self.metrics_history:
            return None
        
        peak_cpu = max(m.cpu_percent for m in self.metrics_history)
        peak_memory_mb = max(m.memory_mb for m in self.metrics_history)
        peak_memory_pct = max(m.memory_percent for m in self.metrics_history)
        peak_disk_io = max(m.disk_io_mb for m in self.metrics_history)
        peak_network_io = max(m.network_io_mb for m in self.metrics_history)
        
        return ResourceMetrics(
            cpu_percent=peak_cpu,
            memory_mb=peak_memory_mb,
            memory_percent=peak_memory_pct,
            disk_io_mb=peak_disk_io,
            network_io_mb=peak_network_io,
            timestamp=datetime.utcnow()
        )
    
    def check_resource_limits(
        self,
        max_cpu_percent: float = 90,
        max_memory_mb: float = 8192
    ) -> Dict[str, bool]:
        """Check if resource usage exceeds limits."""
        current = self.get_current_metrics()
        if not current:
            return {"cpu_ok": True, "memory_ok": True}
        
        return {
            "cpu_ok": current.cpu_percent <= max_cpu_percent,
            "memory_ok": current.memory_mb <= max_memory_mb,
            "within_limits": (
                current.cpu_percent <= max_cpu_percent and
                current.memory_mb <= max_memory_mb
            )
        }
    
    async def wait_for_resources(
        self,
        max_cpu_percent: float = 80,
        max_memory_mb: float = 8192,
        timeout_seconds: int = 300,
        check_interval: int = 5
    ) -> bool:
        """Wait for resources to be available within limits.
        
        Returns:
            True if resources became available, False if timeout
        """
        start_time = datetime.utcnow()
        
        while (datetime.utcnow() - start_time).total_seconds() < timeout_seconds:
            limits = self.check_resource_limits(max_cpu_percent, max_memory_mb)
            
            if limits["within_limits"]:
                return True
            
            self.logger.info(
                f"Waiting for resources - CPU: {self.get_current_metrics().cpu_percent:.1f}%, "
                f"Memory: {self.get_current_metrics().memory_mb:.0f}MB"
            )
            
            await asyncio.sleep(check_interval)
        
        self.logger.warning(f"Resource wait timeout after {timeout_seconds}s")
        return False