"""Prometheus metrics exporter for pipeline monitoring."""

from datetime import datetime
from typing import Dict, List, Optional
import json

from ...core.utils.logging import get_logger
from .metrics_collector import MetricsCollector, MetricType


logger = get_logger(__name__)


class PrometheusExporter:
    """Export metrics in Prometheus format."""
    
    def __init__(self, metrics_collector: MetricsCollector):
        """Initialize Prometheus exporter.
        
        Args:
            metrics_collector: The metrics collector to export from
        """
        self.metrics_collector = metrics_collector
        self.logger = logger
        
        # Metric type mappings to Prometheus
        self._type_mapping = {
            MetricType.COUNTER: "counter",
            MetricType.GAUGE: "gauge",
            MetricType.HISTOGRAM: "histogram",
            MetricType.SUMMARY: "summary"
        }
    
    def export_metrics(self, include_help: bool = True) -> str:
        """Export all metrics in Prometheus text format.
        
        Args:
            include_help: Whether to include HELP lines
            
        Returns:
            Prometheus formatted metrics text
        """
        lines = []
        
        # Add header
        lines.append("# Yemen Market Integration Pipeline Metrics")
        lines.append(f"# Generated at {datetime.utcnow().isoformat()}")
        lines.append("")
        
        # Export each metric
        all_metrics = self.metrics_collector.get_all_metrics()
        
        for metric_name, metric in all_metrics.items():
            # Add HELP and TYPE lines
            if include_help:
                lines.append(f"# HELP {metric_name} {metric.description}")
            lines.append(f"# TYPE {metric_name} {self._type_mapping[metric.metric_type]}")
            
            # Export values based on type
            if metric.metric_type in [MetricType.COUNTER, MetricType.GAUGE]:
                lines.extend(self._export_simple_metric(metric_name, metric))
            elif metric.metric_type == MetricType.HISTOGRAM:
                lines.extend(self._export_histogram(metric_name, metric))
            elif metric.metric_type == MetricType.SUMMARY:
                lines.extend(self._export_summary(metric_name, metric))
            
            lines.append("")  # Empty line between metrics
        
        return "\n".join(lines)
    
    def _export_simple_metric(self, metric_name: str, metric) -> List[str]:
        """Export counter or gauge metric."""
        lines = []
        
        # Get current value
        current_value = metric.get_current_value()
        if current_value is None:
            return lines
        
        # Group by labels for aggregation
        if metric.labels and metric.values:
            # Export with labels
            label_groups = {}
            for value in metric.values:
                label_key = self._format_labels(value.labels)
                if label_key not in label_groups:
                    label_groups[label_key] = []
                label_groups[label_key].append(value.value)
            
            # Export latest value for each label combination
            for label_str, values in label_groups.items():
                if label_str:
                    lines.append(f"{metric_name}{{{label_str}}} {values[-1]}")
                else:
                    lines.append(f"{metric_name} {values[-1]}")
        else:
            # Export without labels
            lines.append(f"{metric_name} {current_value}")
        
        return lines
    
    def _export_histogram(self, metric_name: str, metric) -> List[str]:
        """Export histogram metric with buckets."""
        lines = []
        
        # Get statistics
        stats = metric.get_statistics(window_minutes=5)
        if not stats:
            return lines
        
        # Define buckets (in seconds for duration metrics)
        buckets = [0.1, 0.5, 1.0, 2.5, 5.0, 10.0, 30.0, 60.0, float('inf')]
        
        # Calculate bucket counts
        bucket_counts = {b: 0 for b in buckets}
        total_count = 0
        total_sum = 0
        
        if metric.values:
            for value in metric.values:
                total_count += 1
                total_sum += value.value
                
                for bucket in buckets:
                    if value.value <= bucket:
                        bucket_counts[bucket] += 1
        
        # Export buckets
        cumulative_count = 0
        for bucket in buckets[:-1]:  # Skip +Inf for now
            cumulative_count = bucket_counts[bucket]
            lines.append(f'{metric_name}_bucket{{le="{bucket}"}} {cumulative_count}')
        
        # Add +Inf bucket
        lines.append(f'{metric_name}_bucket{{le="+Inf"}} {total_count}')
        
        # Add sum and count
        lines.append(f"{metric_name}_sum {total_sum}")
        lines.append(f"{metric_name}_count {total_count}")
        
        return lines
    
    def _export_summary(self, metric_name: str, metric) -> List[str]:
        """Export summary metric with quantiles."""
        lines = []
        
        # Get statistics
        stats = metric.get_statistics(window_minutes=5)
        if not stats or not metric.values:
            return lines
        
        # Calculate quantiles
        values = sorted([v.value for v in metric.values])
        count = len(values)
        
        if count > 0:
            # Export quantiles
            quantiles = [0.5, 0.9, 0.95, 0.99]
            for q in quantiles:
                index = int(count * q)
                if index < count:
                    lines.append(f'{metric_name}{{quantile="{q}"}} {values[index]}')
            
            # Add sum and count
            lines.append(f"{metric_name}_sum {sum(values)}")
            lines.append(f"{metric_name}_count {count}")
        
        return lines
    
    def _format_labels(self, labels: Dict[str, str]) -> str:
        """Format labels for Prometheus syntax."""
        if not labels:
            return ""
        
        label_parts = []
        for key, value in sorted(labels.items()):
            # Escape special characters
            escaped_value = value.replace('"', '\\"').replace('\\', '\\\\')
            label_parts.append(f'{key}="{escaped_value}"')
        
        return ",".join(label_parts)
    
    def export_to_file(self, file_path: str):
        """Export metrics to a file."""
        metrics_text = self.export_metrics()
        
        with open(file_path, 'w') as f:
            f.write(metrics_text)
        
        self.logger.info(f"Exported metrics to {file_path}")
    
    def get_metric_names(self) -> List[str]:
        """Get list of all metric names."""
        return list(self.metrics_collector.get_all_metrics().keys())
    
    def get_metric_metadata(self) -> Dict[str, Dict[str, any]]:
        """Get metadata for all metrics."""
        metadata = {}
        
        for name, metric in self.metrics_collector.get_all_metrics().items():
            metadata[name] = {
                "type": metric.metric_type.value,
                "description": metric.description,
                "unit": metric.unit,
                "labels": metric.labels,
                "current_value": metric.get_current_value(),
                "statistics": metric.get_statistics()
            }
        
        return metadata
    
    def export_json(self) -> str:
        """Export metrics in JSON format for debugging."""
        export_data = {
            "timestamp": datetime.utcnow().isoformat(),
            "metrics": {}
        }
        
        for name, metric in self.metrics_collector.get_all_metrics().items():
            metric_data = {
                "type": metric.metric_type.value,
                "description": metric.description,
                "unit": metric.unit,
                "labels": metric.labels,
                "current_value": metric.get_current_value(),
                "statistics": metric.get_statistics(),
                "recent_values": [
                    {
                        "value": v.value,
                        "timestamp": v.timestamp.isoformat(),
                        "labels": v.labels
                    }
                    for v in list(metric.values)[-10:]  # Last 10 values
                ]
            }
            export_data["metrics"][name] = metric_data
        
        return json.dumps(export_data, indent=2)
    
    def create_grafana_dashboard(self) -> Dict[str, any]:
        """Create a basic Grafana dashboard configuration."""
        return {
            "dashboard": {
                "title": "Yemen Market Integration Pipeline",
                "panels": [
                    {
                        "title": "Pipeline Processing Rate",
                        "targets": [
                            {"expr": "rate(pipeline_records_processed_total[5m])"}
                        ]
                    },
                    {
                        "title": "CPU Usage",
                        "targets": [
                            {"expr": "pipeline_cpu_usage_percent"}
                        ]
                    },
                    {
                        "title": "Memory Usage",
                        "targets": [
                            {"expr": "pipeline_memory_usage_mb"}
                        ]
                    },
                    {
                        "title": "Data Quality Score",
                        "targets": [
                            {"expr": "pipeline_data_quality_score"}
                        ]
                    },
                    {
                        "title": "Processing Duration",
                        "targets": [
                            {"expr": "histogram_quantile(0.95, pipeline_processing_duration_seconds_bucket)"}
                        ]
                    },
                    {
                        "title": "Active Executions",
                        "targets": [
                            {"expr": "pipeline_active_executions"}
                        ]
                    }
                ]
            }
        }