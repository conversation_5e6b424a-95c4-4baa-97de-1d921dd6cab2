"""Alert management system for pipeline monitoring."""

import asyncio
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Callable, Dict, List, Optional, Set
import json

from ...core.utils.logging import get_logger
from .health_checker import HealthStatus, HealthCheckResult


logger = get_logger(__name__)


class AlertLevel(Enum):
    """Alert severity levels."""
    
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"
    
    @property
    def priority(self) -> int:
        """Get numeric priority (higher = more severe)."""
        priorities = {
            self.INFO: 1,
            self.WARNING: 2,
            self.ERROR: 3,
            self.CRITICAL: 4
        }
        return priorities[self]


@dataclass
class AlertRule:
    """Rule for triggering alerts."""
    
    name: str
    condition: Callable[[], bool]
    level: AlertLevel
    message_template: str
    cooldown_minutes: int = 15
    max_alerts_per_hour: int = 4
    enabled: bool = True
    
    # Tracking
    last_triggered: Optional[datetime] = None
    trigger_count: int = 0
    hourly_triggers: List[datetime] = field(default_factory=list)
    
    def can_trigger(self) -> bool:
        """Check if rule can trigger based on cooldown and rate limits."""
        if not self.enabled:
            return False
        
        now = datetime.utcnow()
        
        # Check cooldown
        if self.last_triggered:
            cooldown_end = self.last_triggered + timedelta(minutes=self.cooldown_minutes)
            if now < cooldown_end:
                return False
        
        # Check hourly rate limit
        hour_ago = now - timedelta(hours=1)
        recent_triggers = [t for t in self.hourly_triggers if t > hour_ago]
        if len(recent_triggers) >= self.max_alerts_per_hour:
            return False
        
        return True
    
    def record_trigger(self):
        """Record that this rule was triggered."""
        now = datetime.utcnow()
        self.last_triggered = now
        self.trigger_count += 1
        self.hourly_triggers.append(now)
        
        # Clean old triggers
        hour_ago = now - timedelta(hours=1)
        self.hourly_triggers = [t for t in self.hourly_triggers if t > hour_ago]


@dataclass
class Alert:
    """Alert instance."""
    
    alert_id: str
    rule_name: str
    level: AlertLevel
    message: str
    timestamp: datetime = field(default_factory=datetime.utcnow)
    details: Dict[str, any] = field(default_factory=dict)
    acknowledged: bool = False
    acknowledged_at: Optional[datetime] = None
    acknowledged_by: Optional[str] = None
    
    def acknowledge(self, user: str = "system"):
        """Acknowledge this alert."""
        self.acknowledged = True
        self.acknowledged_at = datetime.utcnow()
        self.acknowledged_by = user
    
    def to_dict(self) -> Dict[str, any]:
        """Convert to dictionary for serialization."""
        return {
            "alert_id": self.alert_id,
            "rule_name": self.rule_name,
            "level": self.level.value,
            "message": self.message,
            "timestamp": self.timestamp.isoformat(),
            "details": self.details,
            "acknowledged": self.acknowledged,
            "acknowledged_at": self.acknowledged_at.isoformat() if self.acknowledged_at else None,
            "acknowledged_by": self.acknowledged_by
        }


class AlertManager:
    """Manages alerts for pipeline monitoring."""
    
    def __init__(
        self,
        check_interval_seconds: int = 30,
        max_alerts_history: int = 1000
    ):
        """Initialize alert manager.
        
        Args:
            check_interval_seconds: How often to check alert rules
            max_alerts_history: Maximum alerts to keep in history
        """
        self.check_interval = check_interval_seconds
        self.max_alerts_history = max_alerts_history
        self.rules: Dict[str, AlertRule] = {}
        self.alerts: List[Alert] = []
        self.handlers: Dict[AlertLevel, List[Callable[[Alert], None]]] = {
            level: [] for level in AlertLevel
        }
        self._check_task: Optional[asyncio.Task] = None
        self._alert_counter = 0
        self.logger = logger
        
        # Register default rules
        self._register_default_rules()
    
    def _register_default_rules(self):
        """Register default alert rules."""
        # High CPU usage
        self.add_rule(
            AlertRule(
                name="high_cpu_usage",
                condition=lambda: self._check_cpu_usage() > 85,
                level=AlertLevel.WARNING,
                message_template="High CPU usage: {cpu_percent}%",
                cooldown_minutes=10
            )
        )
        
        # Critical CPU usage
        self.add_rule(
            AlertRule(
                name="critical_cpu_usage",
                condition=lambda: self._check_cpu_usage() > 95,
                level=AlertLevel.CRITICAL,
                message_template="Critical CPU usage: {cpu_percent}%",
                cooldown_minutes=5,
                max_alerts_per_hour=10
            )
        )
        
        # High memory usage
        self.add_rule(
            AlertRule(
                name="high_memory_usage",
                condition=lambda: self._check_memory_usage() > 85,
                level=AlertLevel.WARNING,
                message_template="High memory usage: {memory_percent}%",
                cooldown_minutes=10
            )
        )
        
        # Data quality issues
        self.add_rule(
            AlertRule(
                name="low_data_quality",
                condition=lambda: self._check_data_quality() < 70,
                level=AlertLevel.ERROR,
                message_template="Low data quality score: {quality_score}%",
                cooldown_minutes=30
            )
        )
        
        # Pipeline failures
        self.add_rule(
            AlertRule(
                name="pipeline_failure_rate",
                condition=lambda: self._check_failure_rate() > 0.1,
                level=AlertLevel.ERROR,
                message_template="High pipeline failure rate: {failure_rate}%",
                cooldown_minutes=15
            )
        )
    
    def add_rule(self, rule: AlertRule):
        """Add an alert rule."""
        self.rules[rule.name] = rule
        self.logger.info(f"Added alert rule: {rule.name}")
    
    def add_handler(self, level: AlertLevel, handler: Callable[[Alert], None]):
        """Add a handler for alerts of specific level."""
        self.handlers[level].append(handler)
    
    async def start(self):
        """Start alert monitoring."""
        if self._check_task and not self._check_task.done():
            self.logger.warning("Alert manager already running")
            return
        
        self._check_task = asyncio.create_task(self._check_loop())
        self.logger.info("Alert manager started")
    
    async def stop(self):
        """Stop alert monitoring."""
        if self._check_task:
            self._check_task.cancel()
            try:
                await self._check_task
            except asyncio.CancelledError:
                pass
        self.logger.info("Alert manager stopped")
    
    async def _check_loop(self):
        """Main alert checking loop."""
        while True:
            try:
                await self.check_all_rules()
                await asyncio.sleep(self.check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in alert check loop: {e}")
                await asyncio.sleep(self.check_interval)
    
    async def check_all_rules(self):
        """Check all alert rules."""
        for rule in self.rules.values():
            if not rule.can_trigger():
                continue
            
            try:
                # Evaluate condition
                should_alert = rule.condition()
                
                if should_alert:
                    # Create and trigger alert
                    alert = self._create_alert(rule)
                    await self.trigger_alert(alert)
                    rule.record_trigger()
                    
            except Exception as e:
                self.logger.error(f"Error checking rule {rule.name}: {e}")
    
    def _create_alert(self, rule: AlertRule) -> Alert:
        """Create an alert from a rule."""
        self._alert_counter += 1
        
        # Gather context for message
        context = {
            "cpu_percent": self._check_cpu_usage(),
            "memory_percent": self._check_memory_usage(),
            "quality_score": self._check_data_quality(),
            "failure_rate": self._check_failure_rate() * 100
        }
        
        # Format message
        try:
            message = rule.message_template.format(**context)
        except Exception:
            message = rule.message_template
        
        return Alert(
            alert_id=f"alert_{self._alert_counter}",
            rule_name=rule.name,
            level=rule.level,
            message=message,
            details=context
        )
    
    async def trigger_alert(self, alert: Alert):
        """Trigger an alert."""
        # Add to history
        self.alerts.append(alert)
        if len(self.alerts) > self.max_alerts_history:
            self.alerts.pop(0)
        
        # Log alert
        log_method = {
            AlertLevel.INFO: self.logger.info,
            AlertLevel.WARNING: self.logger.warning,
            AlertLevel.ERROR: self.logger.error,
            AlertLevel.CRITICAL: self.logger.error
        }[alert.level]
        
        log_method(f"Alert [{alert.level.value}]: {alert.message}")
        
        # Call handlers
        handlers = self.handlers.get(alert.level, [])
        for handler in handlers:
            try:
                if asyncio.iscoroutinefunction(handler):
                    await handler(alert)
                else:
                    handler(alert)
            except Exception as e:
                self.logger.error(f"Error in alert handler: {e}")
    
    def get_active_alerts(self, since_minutes: int = 60) -> List[Alert]:
        """Get active (unacknowledged) alerts from recent history."""
        cutoff = datetime.utcnow() - timedelta(minutes=since_minutes)
        return [
            alert for alert in self.alerts
            if not alert.acknowledged and alert.timestamp >= cutoff
        ]
    
    def get_alerts_by_level(self, level: AlertLevel) -> List[Alert]:
        """Get all alerts of specific level."""
        return [alert for alert in self.alerts if alert.level == level]
    
    def acknowledge_alert(self, alert_id: str, user: str = "system") -> bool:
        """Acknowledge an alert by ID."""
        for alert in self.alerts:
            if alert.alert_id == alert_id:
                alert.acknowledge(user)
                self.logger.info(f"Alert {alert_id} acknowledged by {user}")
                return True
        return False
    
    def get_alert_summary(self) -> Dict[str, any]:
        """Get summary of alert activity."""
        now = datetime.utcnow()
        hour_ago = now - timedelta(hours=1)
        day_ago = now - timedelta(days=1)
        
        recent_alerts = [a for a in self.alerts if a.timestamp >= hour_ago]
        daily_alerts = [a for a in self.alerts if a.timestamp >= day_ago]
        
        return {
            "total_alerts": len(self.alerts),
            "alerts_last_hour": len(recent_alerts),
            "alerts_last_day": len(daily_alerts),
            "active_alerts": len(self.get_active_alerts()),
            "by_level": {
                level.value: len(self.get_alerts_by_level(level))
                for level in AlertLevel
            },
            "top_rules": self._get_top_triggered_rules(5)
        }
    
    def _get_top_triggered_rules(self, n: int) -> List[Dict[str, any]]:
        """Get top N most triggered rules."""
        sorted_rules = sorted(
            self.rules.values(),
            key=lambda r: r.trigger_count,
            reverse=True
        )
        
        return [
            {
                "name": rule.name,
                "trigger_count": rule.trigger_count,
                "level": rule.level.value,
                "enabled": rule.enabled
            }
            for rule in sorted_rules[:n]
        ]
    
    # Helper methods for default rules
    
    def _check_cpu_usage(self) -> float:
        """Get current CPU usage percentage."""
        try:
            import psutil
            return psutil.cpu_percent(interval=1)
        except Exception:
            return 0.0
    
    def _check_memory_usage(self) -> float:
        """Get current memory usage percentage."""
        try:
            import psutil
            return psutil.virtual_memory().percent
        except Exception:
            return 0.0
    
    def _check_data_quality(self) -> float:
        """Get current data quality score."""
        # This would integrate with actual data quality metrics
        # For now, return a placeholder
        return 85.0
    
    def _check_failure_rate(self) -> float:
        """Get recent pipeline failure rate."""
        # This would calculate from actual pipeline metrics
        # For now, return a placeholder
        return 0.05
    
    def export_alerts(self, file_path: str):
        """Export alerts to JSON file."""
        alert_data = [alert.to_dict() for alert in self.alerts]
        
        with open(file_path, 'w') as f:
            json.dump(alert_data, f, indent=2)
        
        self.logger.info(f"Exported {len(alert_data)} alerts to {file_path}")