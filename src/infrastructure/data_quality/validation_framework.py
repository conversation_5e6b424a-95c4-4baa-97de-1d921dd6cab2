"""Data validation framework for ensuring data quality."""
from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Dict, List, Optional, Protocol, Callable
import pandas as pd
import numpy as np
from datetime import datetime
import asyncio
from abc import ABC, abstractmethod


class ValidationLevel(Enum):
    """Validation strictness levels."""
    STRICT = "strict"      # Any error stops processing
    STANDARD = "standard"  # Critical errors stop, warnings continue  
    BASIC = "basic"        # Only catastrophic errors stop


@dataclass
class ValidationResult:
    """Result from data validation."""
    is_valid: bool
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    summary: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class ValidationReport:
    """Report from data validation."""
    is_valid: bool
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    metrics: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    
    def add_error(self, error: str) -> None:
        """Add an error to the report."""
        self.errors.append(error)
        self.is_valid = False
    
    def add_warning(self, warning: str) -> None:
        """Add a warning to the report."""
        self.warnings.append(warning)
    
    def merge(self, other: 'ValidationReport') -> 'ValidationReport':
        """Merge another report into this one."""
        return ValidationReport(
            is_valid=self.is_valid and other.is_valid,
            errors=self.errors + other.errors,
            warnings=self.warnings + other.warnings,
            metrics={**self.metrics, **other.metrics}
        )


class DataValidator(Protocol):
    """Protocol for data validators."""
    
    def validate(self, data: Any) -> ValidationReport:
        """Validate data and return report."""
        ...


class PanelDataValidator:
    """Validator for panel data structures."""
    
    def __init__(self, required_columns: List[str], index_columns: List[str]):
        self.required_columns = required_columns
        self.index_columns = index_columns
    
    def validate(self, df: pd.DataFrame) -> ValidationReport:
        """Validate panel data DataFrame."""
        report = ValidationReport(is_valid=True)
        
        # Check required columns
        missing_cols = set(self.required_columns) - set(df.columns)
        if missing_cols:
            report.add_error(f"Missing required columns: {missing_cols}")
        
        # Check index uniqueness
        if df[self.index_columns].duplicated().any():
            n_dups = df[self.index_columns].duplicated().sum()
            report.add_error(f"Found {n_dups} duplicate index combinations")
        
        # Check for all-null columns
        null_cols = [col for col in df.columns if df[col].isna().all()]
        if null_cols:
            report.add_warning(f"Columns with all null values: {null_cols}")
        
        # Calculate coverage metrics
        report.metrics['total_observations'] = len(df)
        report.metrics['complete_observations'] = len(df.dropna())
        report.metrics['coverage_rate'] = len(df.dropna()) / len(df) if len(df) > 0 else 0
        
        return report


class PriceDataValidator:
    """Validator specific to price data."""
    
    def __init__(self, min_price: float = 0.01, max_price: float = 1000000):
        self.min_price = min_price
        self.max_price = max_price
    
    def validate(self, df: pd.DataFrame, price_column: str = 'price') -> ValidationReport:
        """Validate price data."""
        report = ValidationReport(is_valid=True)
        
        if price_column not in df.columns:
            report.add_error(f"Price column '{price_column}' not found")
            return report
        
        prices = df[price_column]
        
        # Check for negative prices
        if (prices < 0).any():
            n_negative = (prices < 0).sum()
            report.add_error(f"Found {n_negative} negative prices")
        
        # Check for outliers
        if (prices < self.min_price).any():
            n_too_low = (prices < self.min_price).sum()
            report.add_warning(f"Found {n_too_low} prices below {self.min_price}")
        
        if (prices > self.max_price).any():
            n_too_high = (prices > self.max_price).sum()
            report.add_warning(f"Found {n_too_high} prices above {self.max_price}")
        
        # Statistical checks
        report.metrics['price_mean'] = prices.mean()
        report.metrics['price_std'] = prices.std()
        report.metrics['price_min'] = prices.min()
        report.metrics['price_max'] = prices.max()
        
        # Check for sudden jumps
        price_changes = prices.pct_change()
        large_changes = price_changes.abs() > 2.0  # 200% change
        if large_changes.any():
            n_jumps = large_changes.sum()
            report.add_warning(f"Found {n_jumps} price changes >200%")
        
        return report


class ExchangeRateValidator:
    """Validator for exchange rate data."""
    
    def __init__(self):
        # Yemen-specific bounds based on historical data
        self.min_rate = 100    # YER/USD
        self.max_rate = 3000   # YER/USD
    
    def validate(self, df: pd.DataFrame, rate_column: str = 'exchange_rate') -> ValidationReport:
        """Validate exchange rate data."""
        report = ValidationReport(is_valid=True)
        
        if rate_column not in df.columns:
            report.add_error(f"Exchange rate column '{rate_column}' not found")
            return report
        
        rates = df[rate_column]
        
        # Check bounds
        if (rates < self.min_rate).any():
            n_low = (rates < self.min_rate).sum()
            report.add_error(f"Found {n_low} exchange rates below {self.min_rate}")
        
        if (rates > self.max_rate).any():
            n_high = (rates > self.max_rate).sum()
            report.add_error(f"Found {n_high} exchange rates above {self.max_rate}")
        
        # Check for zone consistency
        if 'currency_zone' in df.columns:
            for zone in df['currency_zone'].unique():
                zone_rates = df[df['currency_zone'] == zone][rate_column]
                zone_std = zone_rates.std()
                zone_mean = zone_rates.mean()
                
                # Check if variation within zone is reasonable
                if zone_std / zone_mean > 0.5:  # CV > 50%
                    report.add_warning(
                        f"High exchange rate variation in zone {zone}: "
                        f"CV={zone_std/zone_mean:.2f}"
                    )
        
        report.metrics['rate_mean'] = rates.mean()
        report.metrics['rate_std'] = rates.std()
        report.metrics['rate_min'] = rates.min()
        report.metrics['rate_max'] = rates.max()
        
        return report


class ConflictDataValidator:
    """Validator for conflict event data."""
    
    def validate(self, df: pd.DataFrame) -> ValidationReport:
        """Validate conflict data."""
        report = ValidationReport(is_valid=True)
        
        # Check required columns
        required = ['date', 'latitude', 'longitude', 'event_type', 'fatalities']
        missing = set(required) - set(df.columns)
        if missing:
            report.add_error(f"Missing required columns: {missing}")
            return report
        
        # Check coordinates
        if (df['latitude'].abs() > 90).any():
            report.add_error("Invalid latitude values (>90 or <-90)")
        
        if (df['longitude'].abs() > 180).any():
            report.add_error("Invalid longitude values (>180 or <-180)")
        
        # Check Yemen bounds (approximate)
        yemen_bounds = {
            'lat_min': 12.0, 'lat_max': 19.0,
            'lon_min': 42.0, 'lon_max': 54.0
        }
        
        outside_yemen = (
            (df['latitude'] < yemen_bounds['lat_min']) |
            (df['latitude'] > yemen_bounds['lat_max']) |
            (df['longitude'] < yemen_bounds['lon_min']) |
            (df['longitude'] > yemen_bounds['lon_max'])
        )
        
        if outside_yemen.any():
            n_outside = outside_yemen.sum()
            report.add_warning(f"Found {n_outside} events outside Yemen bounds")
        
        # Check fatalities
        if (df['fatalities'] < 0).any():
            report.add_error("Negative fatalities found")
        
        # Check for duplicates
        dup_cols = ['date', 'latitude', 'longitude', 'event_type']
        if df[dup_cols].duplicated().any():
            n_dups = df[dup_cols].duplicated().sum()
            report.add_warning(f"Found {n_dups} potential duplicate events")
        
        report.metrics['total_events'] = len(df)
        report.metrics['total_fatalities'] = df['fatalities'].sum()
        report.metrics['event_types'] = df['event_type'].value_counts().to_dict()
        
        return report


class CompositeValidator:
    """Combines multiple validators."""
    
    def __init__(self, validators: List[DataValidator]):
        self.validators = validators
    
    def validate(self, data: Any) -> ValidationReport:
        """Run all validators and merge reports."""
        reports = [v.validate(data) for v in self.validators]
        
        # Start with first report
        combined = reports[0]
        
        # Merge others
        for report in reports[1:]:
            combined = combined.merge(report)
        
        return combined


class IntegratedPanelValidator:
    """Comprehensive validator for integrated panel data."""
    
    def __init__(self, methodology_compliant: bool = True):
        self.methodology_compliant = methodology_compliant
        
    def validate(self, df: pd.DataFrame) -> ValidationReport:
        """Validate integrated panel data."""
        report = ValidationReport(is_valid=True)
        
        # Essential columns for panel analysis
        essential_cols = [
            'market_id', 'commodity', 'time_period', 'price', 'usdprice',
            'exchange_rate_used', 'currency_zone'
        ]
        
        missing_essential = set(essential_cols) - set(df.columns)
        if missing_essential:
            report.add_error(f"Missing essential columns: {missing_essential}")
            return report
        
        # Check USD price correction
        if self.methodology_compliant:
            if df['usdprice'].isna().any():
                n_missing = df['usdprice'].isna().sum()
                report.add_error(f"Missing USD prices: {n_missing} observations")
            
            if 'usd_price_corrected' in df.columns:
                if not df['usd_price_corrected'].all():
                    n_uncorrected = (~df['usd_price_corrected']).sum()
                    report.add_error(f"Uncorrected USD prices: {n_uncorrected}")
        
        # Check currency zones
        valid_zones = ['HOUTHI', 'GOVERNMENT', 'CONTESTED']
        invalid_zones = ~df['currency_zone'].isin(valid_zones)
        if invalid_zones.any():
            report.add_error(f"Invalid currency zones found: {df[invalid_zones]['currency_zone'].unique()}")
        
        # Check exchange rate ranges by zone
        zone_ranges = {
            'HOUTHI': (400, 700),
            'GOVERNMENT': (1500, 2500),
            'CONTESTED': (800, 1800)
        }
        
        for zone, (min_rate, max_rate) in zone_ranges.items():
            zone_data = df[df['currency_zone'] == zone]
            if not zone_data.empty:
                rates = zone_data['exchange_rate_used']
                if (rates < min_rate).any() or (rates > max_rate).any():
                    report.add_warning(
                        f"Exchange rates outside expected range for {zone}: "
                        f"min={rates.min():.0f}, max={rates.max():.0f}"
                    )
        
        # Panel completeness
        markets = df['market_id'].nunique()
        commodities = df['commodity'].nunique()
        periods = df['time_period'].nunique()
        
        expected_obs = markets * commodities * periods
        actual_obs = len(df)
        completeness = actual_obs / expected_obs if expected_obs > 0 else 0
        
        report.metrics['panel_completeness'] = completeness
        report.metrics['n_markets'] = markets
        report.metrics['n_commodities'] = commodities
        report.metrics['n_periods'] = periods
        report.metrics['n_observations'] = actual_obs
        
        if completeness < 0.5:
            report.add_warning(f"Low panel completeness: {completeness:.1%}")
        
        # Check for balanced panel
        panel_balance = df.groupby(['market_id', 'commodity']).size()
        if panel_balance.std() / panel_balance.mean() > 0.2:
            report.add_warning("Panel is unbalanced (high variation in observations per series)")
        
        # Derived variables check
        derived_cols = [col for col in df.columns if col.startswith('derived_')]
        report.metrics['n_derived_variables'] = len(derived_cols)
        
        if len(derived_cols) < 10:
            report.add_warning(f"Few derived variables found: {len(derived_cols)}")
        
        return report


class ValidationFramework:
    """Enhanced validation framework with source-specific validators."""
    
    def __init__(self):
        self.validators = self._initialize_validators()
        
    def _initialize_validators(self) -> Dict[str, DataValidator]:
        """Initialize source-specific validators."""
        return {
            'prices': PriceDataValidator(),
            'exchange_rates': ExchangeRateValidator(),
            'conflict': ConflictDataValidator(),
            'panel': PanelDataValidator(
                required_columns=['market_id', 'commodity', 'time_period', 'price'],
                index_columns=['market_id', 'commodity', 'time_period']
            ),
            'integrated_panel': IntegratedPanelValidator(methodology_compliant=True)
        }
    
    async def validate_dataframe(
        self,
        df: pd.DataFrame,
        source_type: str,
        level: ValidationLevel = ValidationLevel.STANDARD
    ) -> ValidationResult:
        """Validate a dataframe based on source type and level."""
        
        # Get appropriate validator
        validator = self.validators.get(source_type)
        if not validator:
            return ValidationResult(
                is_valid=False,
                errors=[f"No validator found for source type: {source_type}"]
            )
        
        # Run validation
        report = validator.validate(df)
        
        # Convert report to result based on level
        result = ValidationResult(
            is_valid=report.is_valid,
            errors=report.errors[:],
            warnings=report.warnings[:],
            summary=report.metrics
        )
        
        # Apply level-based filtering
        if level == ValidationLevel.BASIC:
            # Only catastrophic errors
            result.errors = [e for e in result.errors if 'Missing' in e or 'not found' in e]
            result.is_valid = len(result.errors) == 0
            
        elif level == ValidationLevel.STANDARD:
            # Critical errors only
            result.warnings.extend([e for e in result.errors if 'outlier' in e.lower()])
            result.errors = [e for e in result.errors if 'outlier' not in e.lower()]
            result.is_valid = len(result.errors) == 0
            
        # STRICT level uses all errors as-is
        
        return result
    
    async def validate_pipeline_output(
        self,
        panel_path: Path,
        coverage_target: float = 0.884
    ) -> ValidationResult:
        """Validate complete pipeline output."""
        import pandas as pd
        from pathlib import Path
        
        if not panel_path.exists():
            return ValidationResult(
                is_valid=False,
                errors=[f"Panel file not found: {panel_path}"]
            )
        
        # Load panel
        df = pd.read_parquet(panel_path)
        
        # Run integrated panel validation
        result = await self.validate_dataframe(
            df, 
            source_type='integrated_panel',
            level=ValidationLevel.STRICT
        )
        
        # Check coverage target
        coverage = len(df) / (300 * 36 * 12)  # Approximate
        result.summary['coverage'] = coverage
        
        if coverage < coverage_target:
            result.errors.append(
                f"Coverage {coverage:.1%} below target {coverage_target:.1%}"
            )
            result.is_valid = False
        
        # Check methodology compliance
        from ...core.validation.methodology_validator import MethodologyValidator
        validator = MethodologyValidator()
        
        is_valid, methodology_report = validator.validate_analysis_inputs(
            observations=df,
            analysis_type="panel_analysis"
        )
        
        if not is_valid:
            result.errors.extend(methodology_report.critical_failures)
            result.is_valid = False
        
        result.warnings.extend(methodology_report.warnings)
        
        return result


class PanelQualityChecker:
    """Check quality metrics for panel data."""
    
    @staticmethod
    def calculate_quality_metrics(df: pd.DataFrame) -> Dict[str, float]:
        """Calculate comprehensive quality metrics."""
        metrics = {}
        
        # Completeness
        metrics['overall_completeness'] = 1 - (df.isna().sum().sum() / df.size)
        
        # Price data quality
        if 'price' in df.columns:
            metrics['price_completeness'] = 1 - (df['price'].isna().sum() / len(df))
            metrics['price_cv'] = df['price'].std() / df['price'].mean()
        
        # USD price quality
        if 'usdprice' in df.columns:
            metrics['usd_price_completeness'] = 1 - (df['usdprice'].isna().sum() / len(df))
            if 'price' in df.columns and 'exchange_rate_used' in df.columns:
                # Check consistency
                calculated_usd = df['price'] / df['exchange_rate_used']
                diff = (df['usdprice'] - calculated_usd).abs() / calculated_usd
                metrics['usd_price_consistency'] = 1 - (diff > 0.01).mean()
        
        # Panel balance
        if all(col in df.columns for col in ['market_id', 'commodity', 'time_period']):
            panel_counts = df.groupby(['market_id', 'commodity']).size()
            metrics['panel_balance_cv'] = panel_counts.std() / panel_counts.mean()
            metrics['min_series_length'] = panel_counts.min()
            metrics['max_series_length'] = panel_counts.max()
        
        # Currency zone distribution
        if 'currency_zone' in df.columns:
            zone_dist = df['currency_zone'].value_counts(normalize=True)
            for zone, pct in zone_dist.items():
                metrics[f'zone_{zone.lower()}_pct'] = pct
        
        # Temporal coverage
        if 'time_period' in df.columns:
            metrics['n_time_periods'] = df['time_period'].nunique()
        
        return metrics