"""Tools for migrating existing data to new pipeline format."""

import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
import logging
from dataclasses import dataclass, field

from ...core.domain.market.value_objects import CurrencyZone


logger = logging.getLogger(__name__)


@dataclass
class MigrationReport:
    """Report from data migration."""
    success: bool
    records_migrated: int
    records_failed: int = 0
    issues: List[str] = field(default_factory=list)
    mappings_applied: Dict[str, Any] = field(default_factory=dict)
    output_path: Optional[Path] = None


class DataFormatMigrator:
    """Base class for data format migration."""
    
    def __init__(self, version: str = "v2"):
        self.version = version
        self.column_mappings = self._get_column_mappings()
    
    def _get_column_mappings(self) -> Dict[str, str]:
        """Get column name mappings from old to new format."""
        return {
            # Common mappings
            'Market': 'market_id',
            'Commodity': 'commodity',
            'Date': 'date',
            'Price': 'price',
            'Currency': 'currency',
            'Unit': 'unit',
            'Source': 'source',
            # WFP specific
            'Admin1': 'governorate',
            'Admin2': 'district',
            'Market_Name': 'market_name',
            'Commodity_Name': 'commodity_name',
            'Price_Type': 'price_type',
            # Exchange rate specific
            'Official_Rate': 'official_rate',
            'Parallel_Rate': 'parallel_rate',
            'Exchange_Rate': 'exchange_rate',
            # Control zones
            'Control_Authority': 'control_authority',
            'Zone_Type': 'zone_type'
        }
    
    def migrate_dataframe(
        self,
        df: pd.DataFrame,
        source_type: str
    ) -> Tuple[pd.DataFrame, MigrationReport]:
        """Migrate a dataframe to new format."""
        report = MigrationReport(success=True, records_migrated=0)
        
        try:
            # Apply column mappings
            df_migrated = self._apply_column_mappings(df, report)
            
            # Source-specific migrations
            if source_type == 'wfp_prices':
                df_migrated = self._migrate_wfp_prices(df_migrated, report)
            elif source_type == 'exchange_rates':
                df_migrated = self._migrate_exchange_rates(df_migrated, report)
            elif source_type == 'control_zones':
                df_migrated = self._migrate_control_zones(df_migrated, report)
            elif source_type == 'conflict':
                df_migrated = self._migrate_conflict_data(df_migrated, report)
            
            # Common migrations
            df_migrated = self._standardize_dates(df_migrated, report)
            df_migrated = self._add_metadata_columns(df_migrated, report)
            
            report.records_migrated = len(df_migrated)
            
        except Exception as e:
            logger.error(f"Migration failed: {str(e)}")
            report.success = False
            report.issues.append(f"Migration error: {str(e)}")
            df_migrated = df
        
        return df_migrated, report
    
    def _apply_column_mappings(
        self,
        df: pd.DataFrame,
        report: MigrationReport
    ) -> pd.DataFrame:
        """Apply column name mappings."""
        renamed_cols = {}
        
        for old_col, new_col in self.column_mappings.items():
            if old_col in df.columns:
                renamed_cols[old_col] = new_col
        
        if renamed_cols:
            df = df.rename(columns=renamed_cols)
            report.mappings_applied['columns_renamed'] = renamed_cols
        
        return df
    
    def _migrate_wfp_prices(
        self,
        df: pd.DataFrame,
        report: MigrationReport
    ) -> pd.DataFrame:
        """Migrate WFP price data specifics."""
        # Ensure commodity codes are lowercase
        if 'commodity' in df.columns:
            df['commodity'] = df['commodity'].str.lower().str.strip()
        
        # Standardize market IDs
        if 'market_id' in df.columns:
            df['market_id'] = df['market_id'].str.lower().str.replace(' ', '_')
        
        # Add currency zone based on governorate
        if 'governorate' in df.columns:
            df['currency_zone'] = df['governorate'].apply(self._map_governorate_to_zone)
            report.mappings_applied['currency_zones'] = True
        
        # Ensure numeric price
        if 'price' in df.columns:
            df['price'] = pd.to_numeric(df['price'], errors='coerce')
            n_invalid = df['price'].isna().sum()
            if n_invalid > 0:
                report.issues.append(f"Found {n_invalid} invalid prices")
        
        return df
    
    def _migrate_exchange_rates(
        self,
        df: pd.DataFrame,
        report: MigrationReport
    ) -> pd.DataFrame:
        """Migrate exchange rate data."""
        # Consolidate rate columns
        if 'parallel_rate' in df.columns and 'official_rate' in df.columns:
            # Use parallel rate as primary, fall back to official
            df['exchange_rate'] = df['parallel_rate'].fillna(df['official_rate'])
            report.mappings_applied['rate_consolidation'] = 'parallel_preferred'
        
        # Add rate type indicator
        df['rate_type'] = 'parallel'
        df.loc[df['parallel_rate'].isna(), 'rate_type'] = 'official'
        
        # Ensure positive rates
        if 'exchange_rate' in df.columns:
            invalid_rates = df['exchange_rate'] <= 0
            if invalid_rates.any():
                n_invalid = invalid_rates.sum()
                report.issues.append(f"Found {n_invalid} invalid exchange rates")
                df.loc[invalid_rates, 'exchange_rate'] = np.nan
        
        return df
    
    def _migrate_control_zones(
        self,
        df: pd.DataFrame,
        report: MigrationReport
    ) -> pd.DataFrame:
        """Migrate control zone data."""
        # Map control authorities to standard zones
        authority_mapping = {
            'DFA': 'HOUTHI',
            'Defacto authorities': 'HOUTHI',
            'IRG': 'GOVERNMENT',
            'Internationally Recognized Government': 'GOVERNMENT',
            'STC': 'GOVERNMENT',  # Simplified
            'Contested': 'CONTESTED',
            'Unknown': 'CONTESTED'
        }
        
        if 'control_authority' in df.columns:
            df['currency_zone'] = df['control_authority'].map(authority_mapping)
            df['currency_zone'] = df['currency_zone'].fillna('CONTESTED')
            report.mappings_applied['control_authority_mapping'] = authority_mapping
        
        return df
    
    def _migrate_conflict_data(
        self,
        df: pd.DataFrame,
        report: MigrationReport
    ) -> pd.DataFrame:
        """Migrate conflict event data."""
        # Standardize event types
        if 'event_type' in df.columns:
            df['event_type'] = df['event_type'].str.lower().str.strip()
        
        # Ensure numeric coordinates
        for coord in ['latitude', 'longitude']:
            if coord in df.columns:
                df[coord] = pd.to_numeric(df[coord], errors='coerce')
        
        # Ensure non-negative fatalities
        if 'fatalities' in df.columns:
            df['fatalities'] = pd.to_numeric(df['fatalities'], errors='coerce')
            df['fatalities'] = df['fatalities'].fillna(0).clip(lower=0)
        
        return df
    
    def _standardize_dates(
        self,
        df: pd.DataFrame,
        report: MigrationReport
    ) -> pd.DataFrame:
        """Standardize date formats."""
        date_cols = ['date', 'observed_date', 'event_date', 'timestamp']
        
        for col in date_cols:
            if col in df.columns:
                try:
                    df[col] = pd.to_datetime(df[col], errors='coerce')
                    n_invalid = df[col].isna().sum()
                    if n_invalid > 0:
                        report.issues.append(f"Found {n_invalid} invalid dates in {col}")
                except Exception as e:
                    report.issues.append(f"Date conversion failed for {col}: {str(e)}")
        
        return df
    
    def _add_metadata_columns(
        self,
        df: pd.DataFrame,
        report: MigrationReport
    ) -> pd.DataFrame:
        """Add metadata columns for tracking."""
        df['migration_version'] = self.version
        df['migration_timestamp'] = datetime.now()
        df['original_row_count'] = len(df)
        
        return df
    
    def _map_governorate_to_zone(self, governorate: str) -> str:
        """Map governorate to currency zone."""
        if pd.isna(governorate):
            return 'CONTESTED'
        
        gov_lower = governorate.lower().strip()
        
        # Northern governorates (Houthi control)
        houthi_govs = [
            "sana'a", "sanaa", "amran", "dhamar", "hajjah", 
            "sa'ada", "saada", "al mahwit", "raymah", "ibb"
        ]
        
        # Southern governorates (Government control)
        government_govs = [
            "aden", "lahj", "abyan", "shabwah", "hadramaut",
            "al maharah", "socotra", "al dhale", "al bayda"
        ]
        
        if any(houthi in gov_lower for houthi in houthi_govs):
            return 'HOUTHI'
        elif any(gov in gov_lower for gov in government_govs):
            return 'GOVERNMENT'
        else:
            return 'CONTESTED'


class BulkDataMigrator:
    """Migrate multiple data files in bulk."""
    
    def __init__(self, migrator: DataFormatMigrator):
        self.migrator = migrator
    
    async def migrate_directory(
        self,
        input_dir: Path,
        output_dir: Path,
        file_pattern: str = "*.csv",
        source_type: str = "auto"
    ) -> Dict[str, MigrationReport]:
        """Migrate all files in a directory."""
        input_dir = Path(input_dir)
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        reports = {}
        
        for file_path in input_dir.glob(file_pattern):
            logger.info(f"Migrating {file_path.name}")
            
            # Determine source type
            if source_type == "auto":
                detected_type = self._detect_source_type(file_path)
            else:
                detected_type = source_type
            
            # Load data
            if file_path.suffix == '.csv':
                df = pd.read_csv(file_path)
            elif file_path.suffix == '.parquet':
                df = pd.read_parquet(file_path)
            elif file_path.suffix in ['.xlsx', '.xls']:
                df = pd.read_excel(file_path)
            else:
                logger.warning(f"Unsupported file type: {file_path.suffix}")
                continue
            
            # Migrate
            df_migrated, report = self.migrator.migrate_dataframe(df, detected_type)
            
            # Save migrated data
            output_path = output_dir / f"{file_path.stem}_migrated.parquet"
            df_migrated.to_parquet(output_path, index=False)
            report.output_path = output_path
            
            reports[file_path.name] = report
        
        return reports
    
    def _detect_source_type(self, file_path: Path) -> str:
        """Detect source type from filename."""
        name_lower = file_path.stem.lower()
        
        if 'price' in name_lower or 'wfp' in name_lower:
            return 'wfp_prices'
        elif 'exchange' in name_lower or 'rate' in name_lower:
            return 'exchange_rates'
        elif 'control' in name_lower or 'zone' in name_lower:
            return 'control_zones'
        elif 'conflict' in name_lower or 'acled' in name_lower:
            return 'conflict'
        else:
            return 'generic'


class VersionCompatibilityChecker:
    """Check compatibility between data versions."""
    
    @staticmethod
    def check_compatibility(
        old_df: pd.DataFrame,
        new_df: pd.DataFrame
    ) -> Dict[str, Any]:
        """Check compatibility between old and new data formats."""
        report = {
            'compatible': True,
            'issues': [],
            'statistics': {}
        }
        
        # Check row counts
        old_rows = len(old_df)
        new_rows = len(new_df)
        
        if abs(old_rows - new_rows) > old_rows * 0.01:  # >1% difference
            report['issues'].append(
                f"Row count mismatch: old={old_rows}, new={new_rows}"
            )
            report['compatible'] = False
        
        report['statistics']['row_count_change'] = new_rows - old_rows
        report['statistics']['row_count_pct_change'] = (
            (new_rows - old_rows) / old_rows * 100 if old_rows > 0 else 0
        )
        
        # Check for data loss in key columns
        key_columns = ['price', 'exchange_rate', 'market_id', 'commodity']
        
        for col in key_columns:
            if col in old_df.columns and col in new_df.columns:
                old_non_null = old_df[col].notna().sum()
                new_non_null = new_df[col].notna().sum()
                
                if new_non_null < old_non_null * 0.99:  # >1% data loss
                    report['issues'].append(
                        f"Data loss in {col}: {old_non_null - new_non_null} records"
                    )
                    report['compatible'] = False
        
        return report


# Convenience functions
def migrate_wfp_prices(input_path: Path, output_path: Path) -> MigrationReport:
    """Migrate WFP price data file."""
    migrator = DataFormatMigrator()
    df = pd.read_csv(input_path) if input_path.suffix == '.csv' else pd.read_parquet(input_path)
    
    df_migrated, report = migrator.migrate_dataframe(df, 'wfp_prices')
    
    output_path.parent.mkdir(parents=True, exist_ok=True)
    df_migrated.to_parquet(output_path, index=False)
    report.output_path = output_path
    
    return report