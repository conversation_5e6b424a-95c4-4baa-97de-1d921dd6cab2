"""Enhanced climate data processor with SPI/SPEI calculations.

This processor extends the base climate processor to include:
- Standardized Precipitation Index (SPI) calculation
- Standardized Precipitation-Evapotranspiration Index (SPEI)
- Drought severity classification
- Integration with panel builder
"""

import asyncio
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
import numpy as np
import pandas as pd
import rasterio
from scipy import stats
import xarray as xr
from dataclasses import dataclass
import structlog
import aiohttp
import h5py

from src.infrastructure.processors.geo_data_processor import GeoDataProcessor, RasterConfig
from src.infrastructure.processors.base_processor import ProcessorResult, ProcessorConfig
from src.core.domain.climate.entities import ClimateObservation, ClimateMetrics
from src.core.domain.climate.value_objects import (
    ClimateDataSource, DroughtSeverity, RainfallAnomaly, SeasonalCalendar
)
from src.core.domain.market.value_objects import Coordinates
from src.infrastructure.external_services.hdx_enhanced_client import HDXEnhancedClient
from src.infrastructure.data_quality.validation_framework import ValidationLevel, ValidationReport
from src.core.domain.shared.exceptions import DataProcessingError

logger = structlog.get_logger()


@dataclass
class SPIConfig:
    """Configuration for Standardized Precipitation Index calculation."""
    reference_period_months: int = 24
    distribution: str = 'gamma'
    min_data_points: int = 20
    scales: List[int] = None  # [1, 3, 6, 12] month scales
    
    def __post_init__(self):
        if self.scales is None:
            self.scales = [1, 3, 6, 12]


@dataclass 
class ClimateData:
    """Container for different climate data types."""
    market_id: str
    date: datetime
    rainfall_mm: Optional[float] = None
    temperature_c: Optional[float] = None
    ndvi: Optional[float] = None
    evi: Optional[float] = None
    pet_mm: Optional[float] = None  # Potential evapotranspiration
    data_source: str = ""
    quality_flag: str = "good"


class EnhancedClimateProcessor(GeoDataProcessor):
    """Enhanced processor for climate data with drought indices."""
    
    def __init__(
        self,
        hdx_client: HDXEnhancedClient,
        cache_dir: Optional[Path] = None,
        validation_level: ValidationLevel = ValidationLevel.STANDARD,
        spi_config: Optional[SPIConfig] = None
    ):
        super().__init__(
            source_config={'source_id': 'climate_enhanced'},
            cache_manager=None,  # Will be injected
            validator=None,  # Will be injected
            markets=[]  # Will be set during processing
        )
        self.hdx_client = hdx_client
        self.cache_dir = cache_dir or Path("data/cache/climate")
        self.validation_level = validation_level
        self.spi_config = spi_config or SPIConfig()
        self.logger = logger.bind(processor="EnhancedClimateProcessor")
        
    async def process(self, config: ProcessorConfig) -> ProcessorResult:
        """Process all climate data sources and calculate indices."""
        try:
            self._validate_config(config)
            
            # Get market locations
            markets = await self._get_markets(config)
            self.markets = markets
            
            # Update progress
            await self._update_progress("Starting enhanced climate processing", 0)
            
            # Process different climate data types
            rainfall_data = await self._process_chirps(config)
            await self._update_progress("CHIRPS rainfall processed", 25)
            
            ndvi_data = await self._process_modis_ndvi(config)
            await self._update_progress("MODIS NDVI processed", 50)
            
            temperature_data = await self._process_era5_temperature(config)
            await self._update_progress("ERA5 temperature processed", 70)
            
            # Merge all data
            merged_data = self._merge_climate_data(rainfall_data, ndvi_data, temperature_data)
            
            # Calculate drought indices
            climate_observations = await self._calculate_comprehensive_indices(
                merged_data, config
            )
            await self._update_progress("Drought indices calculated", 90)
            
            # Validate results
            validation_result = await self._validate_climate_results(climate_observations)
            
            await self._update_progress("Enhanced climate processing complete", 100)
            
            return ProcessorResult(
                success=True,
                observations=climate_observations,
                metadata={
                    'total_observations': len(climate_observations),
                    'markets_covered': len(set(obs.metadata.get('market_id') for obs in climate_observations)),
                    'temporal_coverage': self._get_temporal_coverage(climate_observations),
                    'spi_scales_calculated': self.spi_config.scales,
                    'validation': validation_result.dict() if hasattr(validation_result, 'dict') else str(validation_result)
                }
            )
            
        except Exception as e:
            self.logger.error(f"Enhanced climate processing failed: {str(e)}")
            return ProcessorResult(
                success=False,
                error=str(e),
                observations=[]
            )
    
    async def _process_chirps(self, config: ProcessorConfig) -> List[ClimateData]:
        """Process CHIRPS rainfall data."""
        rainfall_data = []
        
        # Search for CHIRPS data on HDX
        search_results = await self.hdx_client.search_datasets(
            query="CHIRPS rainfall Yemen precipitation",
            filters={'tags': ['rainfall', 'precipitation', 'chirps', 'climate']}
        )
        
        if not search_results:
            # Try direct download from CHIRPS server
            rainfall_data = await self._download_chirps_direct(config)
        else:
            # Process HDX results
            for dataset in search_results:
                files = await self._download_and_process_dataset(dataset, 'chirps')
                for file_path in files:
                    data = await self._extract_chirps_values(file_path)
                    rainfall_data.extend(data)
        
        return rainfall_data
    
    async def _download_chirps_direct(self, config: ProcessorConfig) -> List[ClimateData]:
        """Download CHIRPS data directly from source."""
        rainfall_data = []
        base_url = "https://data.chc.ucsb.edu/products/CHIRPS-2.0/global_monthly/tifs"
        
        current_date = config.start_date.replace(day=1)
        while current_date <= config.end_date:
            filename = f"chirps-v2.0.{current_date.year}.{current_date.month:02d}.tif"
            url = f"{base_url}/{filename}"
            
            try:
                file_path = await self._download_file(url, filename)
                if file_path:
                    data = await self._extract_chirps_values(file_path)
                    rainfall_data.extend(data)
            except Exception as e:
                self.logger.warning(f"Failed to download CHIRPS for {current_date}: {e}")
            
            # Move to next month
            current_date += timedelta(days=32)
            current_date = current_date.replace(day=1)
        
        return rainfall_data
    
    async def _extract_chirps_values(self, file_path: Path) -> List[ClimateData]:
        """Extract rainfall values from CHIRPS raster."""
        climate_data = []
        
        with rasterio.open(file_path) as src:
            # Extract date from filename
            date = self._parse_date_from_filename(file_path.name)
            
            for market in self.markets:
                try:
                    # Sample raster at market location
                    lon, lat = market.location.longitude, market.location.latitude
                    value = list(src.sample([(lon, lat)]))[0][0]
                    
                    if not np.isnan(value) and value >= 0:
                        climate_data.append(
                            ClimateData(
                                market_id=market.id,
                                date=date,
                                rainfall_mm=float(value),
                                data_source='CHIRPS',
                                quality_flag='good'
                            )
                        )
                except Exception as e:
                    self.logger.warning(f"Failed to extract CHIRPS for {market.id}: {e}")
        
        return climate_data
    
    async def _process_modis_ndvi(self, config: ProcessorConfig) -> List[ClimateData]:
        """Process MODIS NDVI data."""
        ndvi_data = []
        
        # Search for MODIS data
        search_results = await self.hdx_client.search_datasets(
            query="MODIS NDVI Yemen vegetation",
            filters={'tags': ['ndvi', 'vegetation', 'modis']}
        )
        
        for dataset in search_results:
            files = await self._download_and_process_dataset(dataset, 'modis')
            for file_path in files:
                if file_path.suffix == '.hdf':
                    data = await self._extract_modis_hdf5_values(file_path)
                else:
                    data = await self._extract_modis_tiff_values(file_path)
                ndvi_data.extend(data)
        
        return ndvi_data
    
    async def _extract_modis_hdf5_values(self, file_path: Path) -> List[ClimateData]:
        """Extract NDVI values from MODIS HDF5 file."""
        climate_data = []
        
        with h5py.File(file_path, 'r') as f:
            # MODIS structure varies, but typically:
            # /MODIS_Grid_16DAY_250m_500m_VI/Data Fields/250m 16 days NDVI
            ndvi_dataset = None
            for key in f.keys():
                if 'NDVI' in key or 'ndvi' in key:
                    ndvi_dataset = f[key]
                    break
            
            if ndvi_dataset is None:
                # Try nested structure
                try:
                    ndvi_dataset = f['MODIS_Grid_16DAY_250m_500m_VI']['Data Fields']['250m 16 days NDVI']
                except:
                    self.logger.warning(f"Could not find NDVI dataset in {file_path}")
                    return climate_data
            
            # Get georeferencing info
            # This is simplified - real MODIS processing is more complex
            ndvi_array = ndvi_dataset[:]
            
            # Extract date from filename
            date = self._parse_modis_date(file_path.name)
            
            # For now, use simplified extraction
            # In production, would need proper MODIS projection handling
            for market in self.markets:
                try:
                    # Simplified - would need proper geographic transformation
                    value = self._sample_modis_at_location(ndvi_array, market.location)
                    
                    if value is not None:
                        # Scale MODIS NDVI (typically stored as int16, scale 0.0001)
                        ndvi_value = value * 0.0001
                        
                        climate_data.append(
                            ClimateData(
                                market_id=market.id,
                                date=date,
                                ndvi=float(ndvi_value),
                                data_source='MODIS',
                                quality_flag='good'
                            )
                        )
                except Exception as e:
                    self.logger.warning(f"Failed to extract MODIS NDVI for {market.id}: {e}")
        
        return climate_data
    
    async def _process_era5_temperature(self, config: ProcessorConfig) -> List[ClimateData]:
        """Process ERA5 temperature data."""
        temperature_data = []
        
        # Search for ERA5 data
        search_results = await self.hdx_client.search_datasets(
            query="ERA5 temperature Yemen climate",
            filters={'tags': ['temperature', 'climate', 'era5']}
        )
        
        for dataset in search_results:
            files = await self._download_and_process_dataset(dataset, 'era5')
            for file_path in files:
                data = await self._extract_era5_values(file_path)
                temperature_data.extend(data)
        
        return temperature_data
    
    async def _extract_era5_values(self, file_path: Path) -> List[ClimateData]:
        """Extract temperature values from ERA5 NetCDF."""
        climate_data = []
        
        ds = xr.open_dataset(file_path)
        
        # ERA5 typically has 't2m' for 2-meter temperature
        if 't2m' in ds.variables:
            temp_var = ds['t2m']
            
            # Get time values
            times = pd.to_datetime(ds.time.values)
            
            for market in self.markets:
                try:
                    # Select nearest point
                    lon, lat = market.location.longitude, market.location.latitude
                    point_data = temp_var.sel(
                        longitude=lon,
                        latitude=lat,
                        method='nearest'
                    )
                    
                    # Process each time step
                    for t_idx, time in enumerate(times):
                        value = float(point_data.isel(time=t_idx).values)
                        
                        # Convert from Kelvin to Celsius if needed
                        if value > 200:
                            value = value - 273.15
                        
                        climate_data.append(
                            ClimateData(
                                market_id=market.id,
                                date=time,
                                temperature_c=value,
                                data_source='ERA5',
                                quality_flag='good'
                            )
                        )
                except Exception as e:
                    self.logger.warning(f"Failed to extract ERA5 for {market.id}: {e}")
        
        ds.close()
        return climate_data
    
    def _merge_climate_data(
        self,
        rainfall_data: List[ClimateData],
        ndvi_data: List[ClimateData],
        temperature_data: List[ClimateData]
    ) -> pd.DataFrame:
        """Merge all climate data sources into unified DataFrame."""
        # Convert to DataFrames
        dfs = []
        
        if rainfall_data:
            rainfall_df = pd.DataFrame([
                {
                    'market_id': d.market_id,
                    'date': d.date,
                    'rainfall_mm': d.rainfall_mm
                }
                for d in rainfall_data
            ])
            dfs.append(rainfall_df)
        
        if ndvi_data:
            ndvi_df = pd.DataFrame([
                {
                    'market_id': d.market_id,
                    'date': d.date,
                    'ndvi': d.ndvi
                }
                for d in ndvi_data
            ])
            dfs.append(ndvi_df)
        
        if temperature_data:
            temp_df = pd.DataFrame([
                {
                    'market_id': d.market_id,
                    'date': d.date,
                    'temperature_c': d.temperature_c
                }
                for d in temperature_data
            ])
            dfs.append(temp_df)
        
        # Merge all DataFrames
        if not dfs:
            return pd.DataFrame()
        
        merged = dfs[0]
        for df in dfs[1:]:
            merged = pd.merge(
                merged,
                df,
                on=['market_id', 'date'],
                how='outer'
            )
        
        # Sort by market and date
        merged.sort_values(['market_id', 'date'], inplace=True)
        
        return merged
    
    async def _calculate_comprehensive_indices(
        self,
        climate_df: pd.DataFrame,
        config: ProcessorConfig
    ) -> List[ClimateObservation]:
        """Calculate SPI, SPEI, and other drought indices."""
        observations = []
        
        # Group by market for index calculation
        for market_id, market_data in climate_df.groupby('market_id'):
            # Calculate indices for each scale
            for scale in self.spi_config.scales:
                # Calculate SPI
                if 'rainfall_mm' in market_data.columns:
                    spi_values = self._calculate_spi(
                        market_data['rainfall_mm'].values,
                        market_data['date'].values,
                        scale
                    )
                    
                    # Add SPI to data
                    market_data[f'spi_{scale}month'] = market_data['date'].map(spi_values)
                
                # Calculate SPEI (if temperature available for PET calculation)
                if 'temperature_c' in market_data.columns and 'rainfall_mm' in market_data.columns:
                    spei_values = self._calculate_spei(
                        market_data,
                        scale
                    )
                    market_data[f'spei_{scale}month'] = market_data['date'].map(spei_values)
            
            # Create observations
            for _, row in market_data.iterrows():
                # Get market location
                market = next((m for m in self.markets if m.id == market_id), None)
                if not market:
                    continue
                
                # Determine drought severity from indices
                spi_3 = row.get('spi_3month', 0)
                severity = DroughtSeverity.from_spi(spi_3)
                
                # Calculate anomalies
                rainfall_anomaly = None
                if pd.notna(row.get('rainfall_mm')):
                    monthly_mean = market_data[
                        market_data['date'].dt.month == row['date'].month
                    ]['rainfall_mm'].mean()
                    
                    if monthly_mean > 0:
                        anomaly_pct = ((row['rainfall_mm'] - monthly_mean) / monthly_mean) * 100
                        rainfall_anomaly = RainfallAnomaly.calculate(
                            row['rainfall_mm'],
                            monthly_mean
                        )
                
                # Create observation
                obs = ClimateObservation(
                    observation_date=row['date'],
                    location=Coordinates(
                        latitude=market.location.latitude,
                        longitude=market.location.longitude
                    ),
                    rainfall_mm=row.get('rainfall_mm'),
                    temperature_celsius=row.get('temperature_c'),
                    ndvi=row.get('ndvi'),
                    drought_index=spi_3,
                    data_source='Enhanced Climate Processor',
                    metadata={
                        'market_id': market_id,
                        'drought_severity': severity.value,
                        'season': SeasonalCalendar.get_season(row['date'].month),
                        'spi_scales': {
                            f'spi_{scale}month': row.get(f'spi_{scale}month')
                            for scale in self.spi_config.scales
                            if f'spi_{scale}month' in row
                        },
                        'anomaly': rainfall_anomaly.anomaly_percent if rainfall_anomaly else None
                    }
                )
                
                observations.append(obs)
        
        return observations
    
    def _calculate_spi(
        self,
        precipitation: np.ndarray,
        dates: np.ndarray,
        scale: int
    ) -> Dict[datetime, float]:
        """Calculate Standardized Precipitation Index."""
        spi_values = {}
        
        # Need at least scale months of data
        if len(precipitation) < scale:
            return spi_values
        
        # Calculate rolling sum
        precip_series = pd.Series(precipitation, index=dates)
        rolling_sum = precip_series.rolling(window=scale, min_periods=scale).sum()
        
        # Fit gamma distribution to non-zero values
        non_zero = rolling_sum[rolling_sum > 0].values
        if len(non_zero) < self.spi_config.min_data_points:
            return spi_values
        
        # Fit distribution
        if self.spi_config.distribution == 'gamma':
            try:
                alpha, loc, beta = stats.gamma.fit(non_zero, floc=0)
                
                # Calculate SPI for each value
                for date, value in rolling_sum.items():
                    if pd.notna(value):
                        if value == 0:
                            # Handle zero precipitation
                            prob = len(rolling_sum[rolling_sum == 0]) / len(rolling_sum)
                        else:
                            # Calculate cumulative probability
                            prob = stats.gamma.cdf(value, alpha, loc=loc, scale=beta)
                        
                        # Convert to standard normal
                        spi = stats.norm.ppf(prob)
                        # Clip extreme values
                        spi = np.clip(spi, -3, 3)
                        spi_values[date] = float(spi)
            except Exception as e:
                self.logger.warning(f"SPI calculation failed: {e}")
        
        return spi_values
    
    def _calculate_spei(self, climate_data: pd.DataFrame, scale: int) -> Dict[datetime, float]:
        """Calculate Standardized Precipitation-Evapotranspiration Index."""
        spei_values = {}
        
        # Calculate PET using Hargreaves method (simplified)
        if 'temperature_c' in climate_data.columns:
            # Simplified PET calculation
            # In production, would use more sophisticated method
            climate_data['pet_mm'] = 0.0023 * (climate_data['temperature_c'] + 17.8) * np.sqrt(5) * 30
            
            # Calculate water balance
            climate_data['water_balance'] = climate_data['rainfall_mm'] - climate_data['pet_mm']
            
            # Calculate rolling sum of water balance
            wb_series = pd.Series(
                climate_data['water_balance'].values,
                index=climate_data['date'].values
            )
            rolling_wb = wb_series.rolling(window=scale, min_periods=scale).sum()
            
            # Fit distribution (log-logistic for SPEI)
            valid_wb = rolling_wb[pd.notna(rolling_wb)].values
            if len(valid_wb) >= self.spi_config.min_data_points:
                try:
                    # Shift to ensure positive values for log-logistic
                    shift = abs(valid_wb.min()) + 1 if valid_wb.min() < 0 else 0
                    shifted_wb = valid_wb + shift
                    
                    # Fit log-logistic distribution
                    params = stats.fisk.fit(shifted_wb)
                    
                    # Calculate SPEI
                    for date, value in rolling_wb.items():
                        if pd.notna(value):
                            shifted_value = value + shift
                            prob = stats.fisk.cdf(shifted_value, *params)
                            spei = stats.norm.ppf(prob)
                            spei = np.clip(spei, -3, 3)
                            spei_values[date] = float(spei)
                except Exception as e:
                    self.logger.warning(f"SPEI calculation failed: {e}")
        
        return spei_values
    
    async def _download_file(self, url: str, filename: str) -> Optional[Path]:
        """Download file from URL."""
        file_path = self.cache_dir / filename
        
        # Check cache first
        if file_path.exists():
            return file_path
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        file_path.parent.mkdir(parents=True, exist_ok=True)
                        content = await response.read()
                        
                        with open(file_path, 'wb') as f:
                            f.write(content)
                        
                        self.logger.info(f"Downloaded {filename}")
                        return file_path
                    else:
                        self.logger.warning(f"Failed to download {url}: HTTP {response.status}")
        except Exception as e:
            self.logger.error(f"Download error for {url}: {e}")
        
        return None
    
    def _parse_date_from_filename(self, filename: str) -> datetime:
        """Extract date from climate data filename."""
        import re
        
        # Try CHIRPS format: chirps-v2.0.YYYY.MM.tif
        chirps_match = re.search(r'(\d{4})\.(\d{2})', filename)
        if chirps_match:
            year = int(chirps_match.group(1))
            month = int(chirps_match.group(2))
            return datetime(year, month, 1)
        
        # Try YYYYMMDD format
        date_match = re.search(r'(\d{4})(\d{2})(\d{2})', filename)
        if date_match:
            year = int(date_match.group(1))
            month = int(date_match.group(2))
            day = int(date_match.group(3))
            return datetime(year, month, day)
        
        # Default to current date
        return datetime.utcnow()
    
    def _parse_modis_date(self, filename: str) -> datetime:
        """Extract date from MODIS filename."""
        import re
        
        # MODIS format often includes julian day: MOD13Q1.AYYYYDDD
        julian_match = re.search(r'A(\d{4})(\d{3})', filename)
        if julian_match:
            year = int(julian_match.group(1))
            julian_day = int(julian_match.group(2))
            return datetime(year, 1, 1) + timedelta(days=julian_day - 1)
        
        return self._parse_date_from_filename(filename)
    
    def _sample_modis_at_location(self, ndvi_array: np.ndarray, location: Coordinates) -> Optional[float]:
        """Sample MODIS array at geographic location."""
        # This is simplified - real implementation would need:
        # 1. MODIS sinusoidal projection handling
        # 2. Proper coordinate transformation
        # 3. Quality layer checking
        
        # For now, return a placeholder
        # In production, would use pyproj for coordinate transformation
        return None
    
    async def _download_and_process_dataset(self, dataset: Dict, source_type: str) -> List[Path]:
        """Download and process a dataset from HDX."""
        downloaded_files = []
        
        try:
            # Download dataset resources
            resources = await self.hdx_client.get_dataset_resources(dataset['id'])
            
            for resource in resources:
                if resource['format'].lower() in ['tiff', 'geotiff', 'netcdf', 'hdf5']:
                    file_path = await self.hdx_client.download_resource(
                        resource,
                        self.cache_dir / source_type
                    )
                    if file_path:
                        downloaded_files.append(file_path)
        except Exception as e:
            self.logger.error(f"Failed to process dataset {dataset.get('name')}: {e}")
        
        return downloaded_files
    
    async def _get_markets(self, config: ProcessorConfig) -> List[Any]:
        """Get market locations from config or database."""
        # This would typically load from a markets service/repository
        # For now, return example markets
        from src.core.domain.market.entities import Market
        from src.core.domain.market.value_objects import MarketLocation
        
        example_markets = [
            Market(
                id="sana'a_central",
                name="Sana'a Central Market",
                location=MarketLocation(
                    governorate="Sana'a",
                    district="Old City",
                    latitude=15.3694,
                    longitude=44.1910,
                    coordinates=Coordinates(latitude=15.3694, longitude=44.1910)
                ),
                active_since=datetime(2000, 1, 1)
            ),
            Market(
                id="aden_main",
                name="Aden Main Market",
                location=MarketLocation(
                    governorate="Aden",
                    district="Crater",
                    latitude=12.7855,
                    longitude=45.0187,
                    coordinates=Coordinates(latitude=12.7855, longitude=45.0187)
                ),
                active_since=datetime(2000, 1, 1)
            ),
            Market(
                id="taiz_central",
                name="Taiz Central Market",
                location=MarketLocation(
                    governorate="Taiz",
                    district="Al Qahirah",
                    latitude=13.5795,
                    longitude=44.0178,
                    coordinates=Coordinates(latitude=13.5795, longitude=44.0178)
                ),
                active_since=datetime(2000, 1, 1)
            )
        ]
        
        return example_markets
    
    def _get_temporal_coverage(self, observations: List[ClimateObservation]) -> Dict[str, str]:
        """Get temporal coverage statistics."""
        if not observations:
            return {'start': 'N/A', 'end': 'N/A', 'months': 0}
        
        dates = [obs.observation_date for obs in observations]
        return {
            'start': min(dates).strftime('%Y-%m-%d'),
            'end': max(dates).strftime('%Y-%m-%d'),
            'months': len(set((d.year, d.month) for d in dates))
        }
    
    async def _validate_climate_results(self, observations: List[ClimateObservation]) -> str:
        """Validate climate observations."""
        if not observations:
            return "No observations to validate"
        
        # Basic validation checks
        issues = []
        
        # Check temporal coverage
        dates = [obs.observation_date for obs in observations]
        date_range = (max(dates) - min(dates)).days
        if date_range < 365:
            issues.append("Less than 1 year of data")
        
        # Check spatial coverage
        markets_covered = len(set(obs.metadata.get('market_id') for obs in observations))
        if markets_covered < len(self.markets):
            issues.append(f"Only {markets_covered}/{len(self.markets)} markets have data")
        
        # Check data completeness
        rainfall_obs = sum(1 for obs in observations if obs.rainfall_mm is not None)
        if rainfall_obs < len(observations) * 0.8:
            issues.append(f"Rainfall data only {rainfall_obs/len(observations)*100:.1f}% complete")
        
        return "Valid" if not issues else f"Issues: {'; '.join(issues)}"