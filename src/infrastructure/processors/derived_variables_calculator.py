"""
Calculate derived variables and market integration metrics.

This module creates composite indicators and advanced metrics for analysis.
"""

from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
import numpy as np
import pandas as pd
from scipy import stats
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
import structlog

logger = structlog.get_logger()


class DerivedVariablesCalculator:
    """
    Calculate derived variables and composite indicators.
    
    Features:
        - Market integration indices
        - Composite vulnerability scores
        - Food security indicators
        - Economic stress metrics
        - Spatial spillover variables
    """
    
    def __init__(self):
        self.logger = logger.bind(component="DerivedVariablesCalculator")
        self.scaler = StandardScaler()
    
    async def calculate_all_variables(
        self, 
        panel_df: pd.DataFrame
    ) -> pd.DataFrame:
        """Calculate all derived variables."""
        self.logger.info("Calculating derived variables", shape=panel_df.shape)
        
        # Market integration metrics
        panel_df = await self.calculate_integration_metrics(panel_df)
        
        # Vulnerability indices
        panel_df = await self.calculate_vulnerability_indices(panel_df)
        
        # Food security indicators
        panel_df = await self.calculate_food_security_indicators(panel_df)
        
        # Economic stress metrics
        panel_df = await self.calculate_economic_stress_metrics(panel_df)
        
        # Spatial variables
        panel_df = await self.calculate_spatial_variables(panel_df)
        
        # Temporal patterns
        panel_df = await self.calculate_temporal_patterns(panel_df)
        
        self.logger.info(
            "Derived variables calculated",
            n_original_cols=len([c for c in panel_df.columns if not c.startswith('derived_')]),
            n_derived_cols=len([c for c in panel_df.columns if c.startswith('derived_')])
        )
        
        return panel_df
    
    async def calculate_integration_metrics(
        self, 
        df: pd.DataFrame
    ) -> pd.DataFrame:
        """Calculate market integration metrics."""
        
        # Price correlation with reference markets
        if 'usdprice' in df.columns and 'commodity' in df.columns:
            # Calculate correlation with capital market (Sana'a)
            for commodity in df['commodity'].unique():
                comm_df = df[df['commodity'] == commodity].copy()
                
                # Get Sana'a prices as reference
                sanaa_prices = comm_df[comm_df['market_id'].str.contains('sanaa', case=False)]
                if not sanaa_prices.empty:
                    sanaa_series = sanaa_prices.groupby('time_period')['usdprice'].mean()
                    
                    # Calculate rolling correlation
                    def calc_correlation(group):
                        if len(group) < 12:  # Need minimum observations
                            return np.nan
                        
                        merged = pd.merge(
                            group[['time_period', 'usdprice']],
                            sanaa_series.reset_index(),
                            on='time_period',
                            suffixes=('', '_sanaa')
                        )
                        
                        if len(merged) >= 12:
                            return merged['usdprice'].corr(merged['usdprice_sanaa'])
                        return np.nan
                    
                    mask = df['commodity'] == commodity
                    df.loc[mask, 'derived_price_correlation_capital'] = df[mask].groupby('market_id').apply(
                        calc_correlation
                    ).reindex(df[mask]['market_id']).values
        
        # Price convergence speed (error correction coefficient)
        if 'log_price_change' in df.columns:
            # Calculate mean reversion speed
            df['derived_price_convergence_speed'] = df.groupby(['market_id', 'commodity'])[
                'log_price_change'
            ].transform(lambda x: -np.sign(x.shift(1)) * x if len(x) > 1 else np.nan).abs()
        
        # Market efficiency score (based on price predictability)
        if 'price_change' in df.columns:
            # Low autocorrelation = high efficiency
            df['derived_market_efficiency'] = 1 - df.groupby(['market_id', 'commodity'])[
                'price_change'
            ].transform(lambda x: abs(x.autocorr(lag=1)) if len(x) > 5 else np.nan)
        
        return df
    
    async def calculate_vulnerability_indices(
        self, 
        df: pd.DataFrame
    ) -> pd.DataFrame:
        """Calculate composite vulnerability indices."""
        
        # Market Access Vulnerability Index (MAVI)
        mavi_components = []
        
        # Distance components
        if 'distance_to_port' in df.columns:
            mavi_components.append(df['distance_to_port'] / df['distance_to_port'].max())
        if 'distance_to_capital' in df.columns:
            mavi_components.append(df['distance_to_capital'] / df['distance_to_capital'].max())
        
        # Infrastructure components
        if 'road_density' in df.columns:
            mavi_components.append(1 - (df['road_density'] / df['road_density'].max()))
        if 'electricity_access' in df.columns:
            mavi_components.append(1 - df['electricity_access'])
            
        # Conflict component
        if 'conflict_events' in df.columns:
            conflict_norm = df['conflict_events'] / (df['conflict_events'].max() + 1)
            mavi_components.append(conflict_norm)
        
        if mavi_components:
            df['derived_market_access_vulnerability'] = np.mean(mavi_components, axis=0)
        
        # Economic Vulnerability Index (EVI)
        evi_components = []
        
        # Price volatility
        if 'usdprice' in df.columns:
            price_volatility = df.groupby(['market_id', 'commodity'])['usdprice'].transform(
                lambda x: x.rolling(window=6, min_periods=3).std() / x.rolling(window=6, min_periods=3).mean()
            )
            evi_components.append(price_volatility.fillna(0))
        
        # Exchange rate stress
        if 'exchange_rate_deviation' in df.columns:
            evi_components.append(abs(df['exchange_rate_deviation']) / df['exchange_rate_used'])
        
        # Aid dependency
        if 'aid_intensity' in df.columns:
            evi_components.append(df['aid_intensity'])
        
        # Displacement pressure
        if 'displacement_inflow' in df.columns and 'population_25km' in df.columns:
            displacement_pressure = df['displacement_inflow'] / (df['population_25km'] + 1)
            evi_components.append(displacement_pressure)
        
        if evi_components:
            # Standardize components before averaging
            evi_standardized = []
            for comp in evi_components:
                if comp.std() > 0:
                    standardized = (comp - comp.mean()) / comp.std()
                    evi_standardized.append(standardized)
            
            if evi_standardized:
                df['derived_economic_vulnerability'] = np.mean(evi_standardized, axis=0)
        
        # Climate Vulnerability Index (CVI)
        cvi_components = []
        
        # Rainfall variability
        if 'rainfall_variability' in df.columns:
            cvi_components.append(df['rainfall_variability'] / df['rainfall_avg'].replace(0, 1))
        
        # Temperature extremes
        if 'temperature_max' in df.columns and 'temperature_min' in df.columns:
            temp_range = df['temperature_max'] - df['temperature_min']
            cvi_components.append(temp_range / temp_range.max())
        
        # Vegetation stress
        if 'vegetation_index' in df.columns:
            # Low NDVI = high stress
            cvi_components.append(1 - (df['vegetation_index'] / 0.8))  # 0.8 is healthy vegetation
        
        if cvi_components:
            df['derived_climate_vulnerability'] = np.mean([c.fillna(0) for c in cvi_components], axis=0)
        
        # Composite Vulnerability Score
        vuln_indices = [
            col for col in df.columns 
            if col.startswith('derived_') and 'vulnerability' in col
        ]
        
        if vuln_indices:
            df['derived_composite_vulnerability'] = df[vuln_indices].mean(axis=1)
            
            # Vulnerability categories
            vuln_score = df['derived_composite_vulnerability']
            df['derived_vulnerability_category'] = pd.cut(
                vuln_score,
                bins=[-np.inf, np.percentile(vuln_score, 33), 
                      np.percentile(vuln_score, 67), np.inf],
                labels=['Low', 'Medium', 'High']
            )
        
        return df
    
    async def calculate_food_security_indicators(
        self, 
        df: pd.DataFrame
    ) -> pd.DataFrame:
        """Calculate food security indicators."""
        
        # Food Affordability Index
        if 'usdprice' in df.columns and 'commodity' in df.columns:
            # Calculate relative to median prices
            median_prices = df.groupby(['commodity', 'time_period'])['usdprice'].transform('median')
            df['derived_price_stress'] = (df['usdprice'] - median_prices) / median_prices
            
            # Weight by commodity importance (staples more important)
            staple_weights = {
                'wheat': 0.4,
                'rice': 0.3,
                'sugar': 0.15,
                'cooking_oil': 0.15
            }
            
            if 'commodity' in df.columns:
                df['commodity_weight'] = df['commodity'].map(
                    lambda x: staple_weights.get(x.lower(), 0.1)
                )
                
                # Weighted affordability score
                df['derived_food_affordability'] = 1 - (
                    df['derived_price_stress'] * df['commodity_weight']
                ).fillna(0)
        
        # Food Access Score (combines multiple factors)
        access_components = []
        
        # Physical access (infrastructure)
        if 'infrastructure_accessibility' in df.columns:
            access_components.append(df['infrastructure_accessibility'])
        
        # Economic access (prices)
        if 'derived_food_affordability' in df.columns:
            access_components.append(df['derived_food_affordability'])
        
        # Conflict barriers
        if 'has_conflict' in df.columns:
            access_components.append(1 - df['has_conflict'])
        
        # Aid availability
        if 'aid_food_security' in df.columns:
            access_components.append(df['aid_food_security'])
        
        if access_components:
            df['derived_food_access_score'] = np.mean(access_components, axis=0)
        
        # Nutrition Risk Index (based on dietary diversity proxy)
        if 'commodity' in df.columns:
            # Count available commodities per market-month
            commodity_diversity = df.groupby(['market_id', 'time_period'])['commodity'].transform('nunique')
            max_commodities = df['commodity'].nunique()
            
            df['derived_dietary_diversity'] = commodity_diversity / max_commodities
            
            # Combine with IPC if available
            if 'ipc_phase' in df.columns:
                nutrition_risk = (5 - df['ipc_phase']) / 4  # Normalize IPC
                nutrition_risk *= df['derived_dietary_diversity']
                df['derived_nutrition_security'] = nutrition_risk
        
        return df
    
    async def calculate_economic_stress_metrics(
        self, 
        df: pd.DataFrame
    ) -> pd.DataFrame:
        """Calculate economic stress indicators."""
        
        # Currency Fragmentation Impact
        if 'exchange_rate_used' in df.columns and 'currency_zone' in df.columns:
            # Calculate zone divergence
            zone_rates = df.groupby(['currency_zone', 'time_period'])['exchange_rate_used'].transform('mean')
            overall_rate = df.groupby('time_period')['exchange_rate_used'].transform('mean')
            
            df['derived_currency_divergence'] = abs(zone_rates - overall_rate) / overall_rate
            
            # Exchange rate acceleration (second derivative)
            df['derived_fx_acceleration'] = df.groupby('currency_zone')['exchange_rate_used'].transform(
                lambda x: x.diff().diff()
            )
        
        # Inflation Pressure Index
        if 'price_change' in df.columns:
            # Rolling inflation
            df['derived_inflation_3m'] = df.groupby(['market_id', 'commodity'])['price_change'].transform(
                lambda x: x.rolling(window=3, min_periods=1).mean()
            )
            
            df['derived_inflation_6m'] = df.groupby(['market_id', 'commodity'])['price_change'].transform(
                lambda x: x.rolling(window=6, min_periods=3).mean()
            )
            
            # Hyperinflation risk (>50% annual)
            df['derived_hyperinflation_risk'] = (df['derived_inflation_6m'] * 2 > 0.5).astype(int)
        
        # Market Stress Composite
        stress_components = []
        
        if 'derived_currency_divergence' in df.columns:
            stress_components.append(df['derived_currency_divergence'])
            
        if 'price_dispersion' in df.columns:
            stress_components.append(df['price_dispersion'])
            
        if 'derived_inflation_6m' in df.columns:
            stress_components.append(df['derived_inflation_6m'].clip(lower=0))
        
        if stress_components:
            # Use PCA for composite score
            stress_data = pd.concat(stress_components, axis=1).fillna(0)
            
            if stress_data.shape[1] >= 2:
                pca = PCA(n_components=1)
                df['derived_market_stress_index'] = pca.fit_transform(stress_data).flatten()
                
                # Normalize to 0-1 scale
                msi = df['derived_market_stress_index']
                df['derived_market_stress_index'] = (msi - msi.min()) / (msi.max() - msi.min())
        
        return df
    
    async def calculate_spatial_variables(
        self, 
        df: pd.DataFrame
    ) -> pd.DataFrame:
        """Calculate spatial spillover and neighbor effects."""
        
        if all(col in df.columns for col in ['latitude', 'longitude']):
            # Market clusters based on location
            from sklearn.cluster import DBSCAN
            
            coords = df[['latitude', 'longitude']].drop_duplicates()
            
            # Cluster markets within ~50km
            clustering = DBSCAN(eps=0.5, min_samples=2)  # ~50km in degrees
            coords['spatial_cluster'] = clustering.fit_predict(coords[['latitude', 'longitude']])
            
            # Merge back
            df = df.merge(
                coords[['latitude', 'longitude', 'spatial_cluster']],
                on=['latitude', 'longitude'],
                how='left'
            )
            
            # Calculate cluster-level averages
            if 'usdprice' in df.columns:
                df['derived_cluster_avg_price'] = df.groupby(
                    ['spatial_cluster', 'commodity', 'time_period']
                )['usdprice'].transform('mean')
                
                # Price deviation from cluster
                df['derived_spatial_price_deviation'] = (
                    df['usdprice'] - df['derived_cluster_avg_price']
                ) / df['derived_cluster_avg_price']
            
            # Conflict spillovers
            if 'conflict_events' in df.columns:
                df['derived_cluster_conflict'] = df.groupby(
                    ['spatial_cluster', 'time_period']
                )['conflict_events'].transform('sum')
                
                # Exclude own market
                df['derived_neighbor_conflict'] = (
                    df['derived_cluster_conflict'] - df['conflict_events']
                )
        
        # Border effects
        if 'is_border' in df.columns and 'control_authority' in df.columns:
            # Different control across border
            df['derived_cross_border_fragmentation'] = df['is_border'] & (
                df.groupby('spatial_cluster')['control_authority'].transform('nunique') > 1
            )
        
        return df
    
    async def calculate_temporal_patterns(
        self, 
        df: pd.DataFrame
    ) -> pd.DataFrame:
        """Calculate temporal patterns and seasonality."""
        
        # Seasonal components
        if 'month' in df.columns and 'usdprice' in df.columns:
            # Calculate seasonal factors
            monthly_avg = df.groupby(['market_id', 'commodity', 'month'])['usdprice'].transform('mean')
            overall_avg = df.groupby(['market_id', 'commodity'])['usdprice'].transform('mean')
            
            df['derived_seasonal_factor'] = monthly_avg / overall_avg
            
            # Harvest season indicator (simplified)
            harvest_months = {
                'wheat': [4, 5, 6],
                'sorghum': [10, 11],
                'millet': [10, 11]
            }
            
            if 'commodity' in df.columns:
                df['derived_is_harvest_season'] = df.apply(
                    lambda row: row['month'] in harvest_months.get(row['commodity'].lower(), []),
                    axis=1
                ).astype(int)
        
        # Trend components
        if 'time_period' in df.columns and 'usdprice' in df.columns:
            # Time index
            df['time_index'] = df.groupby(['market_id', 'commodity']).cumcount()
            
            # Rolling trend (linear regression slope)
            def calculate_trend(group):
                if len(group) < 6:
                    return np.nan
                
                x = np.arange(len(group))
                y = group.values
                
                if np.all(np.isnan(y)):
                    return np.nan
                
                mask = ~np.isnan(y)
                if mask.sum() < 3:
                    return np.nan
                
                slope, _, _, _, _ = stats.linregress(x[mask], y[mask])
                return slope
            
            df['derived_price_trend'] = df.groupby(['market_id', 'commodity'])['usdprice'].transform(
                lambda x: x.rolling(window=6, min_periods=3).apply(calculate_trend)
            )
        
        # Shock persistence
        if 'price_change' in df.columns:
            # Large price changes (>20%)
            df['derived_price_shock'] = (abs(df['price_change']) > 0.2).astype(int)
            
            # Cumulative shocks
            df['derived_cumulative_shocks'] = df.groupby(['market_id', 'commodity'])[
                'derived_price_shock'
            ].cumsum()
        
        return df
    
    def create_summary_report(
        self, 
        df: pd.DataFrame
    ) -> Dict[str, Any]:
        """Create summary report of derived variables."""
        
        derived_cols = [col for col in df.columns if col.startswith('derived_')]
        
        report = {
            'n_derived_variables': len(derived_cols),
            'categories': {
                'integration': len([c for c in derived_cols if 'integration' in c or 'correlation' in c]),
                'vulnerability': len([c for c in derived_cols if 'vulnerability' in c]),
                'food_security': len([c for c in derived_cols if 'food' in c or 'nutrition' in c]),
                'economic': len([c for c in derived_cols if 'economic' in c or 'inflation' in c]),
                'spatial': len([c for c in derived_cols if 'spatial' in c or 'cluster' in c]),
                'temporal': len([c for c in derived_cols if 'seasonal' in c or 'trend' in c])
            },
            'summary_statistics': {}
        }
        
        # Summary stats for key indicators
        key_indicators = [
            'derived_composite_vulnerability',
            'derived_market_stress_index',
            'derived_food_access_score',
            'derived_price_correlation_capital'
        ]
        
        for indicator in key_indicators:
            if indicator in df.columns:
                report['summary_statistics'][indicator] = {
                    'mean': float(df[indicator].mean()),
                    'std': float(df[indicator].std()),
                    'min': float(df[indicator].min()),
                    'max': float(df[indicator].max()),
                    'missing_pct': float(df[indicator].isna().sum() / len(df) * 100)
                }
        
        return report