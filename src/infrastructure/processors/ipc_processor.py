"""
Processor for Integrated Food Security Phase Classification (IPC) data.

This processor handles:
- IPC acute food insecurity classifications (Phase 1-5)
- Population in need estimates
- Projection periods
- District-level food security analysis
"""

import asyncio
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Optional, Dict, Any, Tuple
import pandas as pd
import numpy as np
import geopandas as gpd
from shapely.geometry import Point
from dataclasses import dataclass
import structlog

from src.infrastructure.processors.base_processor import (
    DataFrameProcessor, SourceConfig, ValidationLevel, ProcessorResult
)
from src.infrastructure.data_quality.validation_framework import (
    ValidationReport, ValidationLevel as ValLevel
)
from src.core.domain.food_security.entities import (
    IPCClassification, FoodSecurityMetrics
)
from src.core.domain.food_security.value_objects import (
    IPCPhase, AnalysisPeriod, DriverCategory
)
from src.core.domain.shared.value_objects import AdministrativeLevel
from src.infrastructure.processors.exceptions import (
    DataQualityException, TransformationException
)
from src.infrastructure.external_services.hdx_enhanced_client import HDXEnhancedClient

logger = structlog.get_logger()


@dataclass
class IPCData:
    """Container for IPC classification data."""
    governorate: str
    district: str
    analysis_date: datetime
    projection_period_start: datetime
    projection_period_end: datetime
    phase: int  # 1-5
    population_phase: Dict[int, int]  # Phase -> Population count
    total_population: int
    drivers: List[str]
    confidence_level: str
    source: str = "IPC"


class IPCProcessor(DataFrameProcessor[IPCClassification]):
    """
    Processor for IPC food security classification data.
    
    Features:
        - Current and projected IPC phases
        - Population in need calculations
        - Multi-level aggregation (district, governorate)
        - Trend analysis
        - Integration with market analysis
    """
    
    # IPC Phase definitions
    IPC_PHASES = {
        1: {
            'name': 'None/Minimal',
            'description': 'Households are able to meet essential food and non-food needs',
            'color': '#C8E6C9'  # Light green
        },
        2: {
            'name': 'Stressed',
            'description': 'Households have minimally adequate food consumption',
            'color': '#FFF176'  # Yellow
        },
        3: {
            'name': 'Crisis',
            'description': 'Households have food consumption gaps or are marginally able to meet minimum',
            'color': '#FF9800'  # Orange
        },
        4: {
            'name': 'Emergency',
            'description': 'Households have large food consumption gaps',
            'color': '#F44336'  # Red
        },
        5: {
            'name': 'Catastrophe/Famine',
            'description': 'Households have extreme food consumption gaps',
            'color': '#6A1B9A'  # Dark purple
        }
    }
    
    # Common drivers of food insecurity in Yemen
    DRIVER_CATEGORIES = {
        'conflict': ['Active conflict', 'Displacement', 'Access constraints'],
        'economic': ['Currency depreciation', 'High prices', 'Loss of livelihoods', 'Reduced remittances'],
        'climate': ['Drought', 'Floods', 'Desert locusts', 'Reduced rainfall'],
        'health': ['Disease outbreak', 'Malnutrition', 'Limited health services'],
        'infrastructure': ['Damaged infrastructure', 'Fuel shortage', 'Market disruption']
    }
    
    def __init__(
        self,
        source_config: SourceConfig,
        cache_manager,
        validator,
        hdx_client: Optional[HDXEnhancedClient] = None,
        admin_boundaries: Optional[gpd.GeoDataFrame] = None
    ):
        super().__init__(source_config, cache_manager, validator)
        self.hdx_client = hdx_client
        self.admin_boundaries = admin_boundaries
        
        self.logger = logger.bind(
            processor="IPCProcessor"
        )
    
    async def download(self) -> List[IPCData]:
        """Download IPC data from HDX or IPC platform."""
        ipc_data = []
        
        if self.hdx_client:
            # Search for IPC data on HDX
            search_results = await self.hdx_client.search_datasets(
                query="IPC Yemen food security classification",
                filters={'tags': ['ipc', 'food security', 'yemen']}
            )
            
            for dataset in search_results:
                resources = await self.hdx_client.get_dataset_resources(dataset['id'])
                
                for resource in resources:
                    if resource['format'].lower() in ['csv', 'xlsx', 'json']:
                        file_path = await self.hdx_client.download_resource(
                            resource,
                            Path("data/cache/ipc")
                        )
                        
                        if file_path:
                            data = await self._parse_ipc_file(file_path)
                            ipc_data.extend(data)
        
        # If no data from HDX, create synthetic data
        if not ipc_data:
            self.logger.warning("No IPC data from HDX, creating synthetic data")
            ipc_data = self._create_synthetic_ipc_data()
        
        return ipc_data
    
    async def _parse_ipc_file(self, file_path: Path) -> List[IPCData]:
        """Parse IPC data file."""
        ipc_data = []
        
        try:
            if file_path.suffix == '.csv':
                df = pd.read_csv(file_path)
            elif file_path.suffix == '.xlsx':
                df = pd.read_excel(file_path)
            else:
                self.logger.warning(f"Unsupported file format: {file_path.suffix}")
                return ipc_data
            
            # Standard IPC columns (may vary)
            required_cols = ['governorate', 'district', 'phase', 'population']
            
            # Parse each row
            for _, row in df.iterrows():
                try:
                    # Extract population by phase
                    population_phase = {}
                    for phase in range(1, 6):
                        phase_col = f'phase{phase}_population'
                        if phase_col in row:
                            population_phase[phase] = int(row[phase_col])
                    
                    ipc = IPCData(
                        governorate=row.get('governorate', 'Unknown'),
                        district=row.get('district', 'Unknown'),
                        analysis_date=pd.to_datetime(row.get('analysis_date', datetime.now())),
                        projection_period_start=pd.to_datetime(
                            row.get('projection_start', datetime.now())
                        ),
                        projection_period_end=pd.to_datetime(
                            row.get('projection_end', datetime.now() + timedelta(days=120))
                        ),
                        phase=int(row.get('phase', 3)),  # Area phase
                        population_phase=population_phase,
                        total_population=int(row.get('total_population', sum(population_phase.values()))),
                        drivers=str(row.get('drivers', '')).split(','),
                        confidence_level=row.get('confidence', 'Medium')
                    )
                    
                    ipc_data.append(ipc)
                    
                except Exception as e:
                    self.logger.warning(f"Failed to parse IPC row: {e}")
        
        except Exception as e:
            self.logger.error(f"Failed to parse IPC file {file_path}: {e}")
        
        return ipc_data
    
    def _create_synthetic_ipc_data(self) -> List[IPCData]:
        """Create synthetic IPC data for testing."""
        ipc_data = []
        
        # Yemen governorates and districts (simplified)
        locations = [
            # North (generally worse food security)
            ("Sa'ada", ["Sa'ada City", "Majz", "Razih"], 4),
            ("Hajjah", ["Hajjah City", "Abs", "Midi"], 4),
            ("Al Hudaydah", ["Al Hudaydah City", "Zabid", "Bajil"], 3),
            ("Sana'a", ["Amanat Al Asimah", "Sanhan", "Bani Matar"], 3),
            
            # South (relatively better)
            ("Aden", ["Crater", "Al Mualla", "Sheikh Othman"], 2),
            ("Lahj", ["Al Hawtah", "Tuban", "Al Milah"], 3),
            ("Hadramaut", ["Mukalla", "Seiyun", "Tarim"], 2),
            
            # Central (mixed)
            ("Taiz", ["Taiz City", "Al Qahirah", "Sabir"], 3),
            ("Ibb", ["Ibb City", "Yarim", "Al Udayn"], 3),
            ("Al Bayda", ["Al Bayda City", "Rada'a", "Mukayras"], 3)
        ]
        
        # Generate data for multiple time periods
        analysis_dates = pd.date_range('2024-01-01', '2024-12-31', freq='Q')
        
        for analysis_date in analysis_dates:
            projection_start = analysis_date + timedelta(days=1)
            projection_end = projection_start + timedelta(days=120)  # 4-month projection
            
            for governorate, districts, base_phase in locations:
                for district in districts:
                    # Vary phase based on season and location
                    phase_variation = 0
                    
                    # Worse during lean season (May-August)
                    if projection_start.month in [5, 6, 7, 8]:
                        phase_variation = 1
                    
                    # Add some randomness
                    phase_variation += np.random.choice([-1, 0, 0, 1], p=[0.1, 0.6, 0.2, 0.1])
                    
                    area_phase = max(1, min(5, base_phase + phase_variation))
                    
                    # Generate population distribution
                    # Assume total district population
                    total_pop = np.random.randint(50000, 300000)
                    
                    # Distribute across phases (worse phases have fewer people)
                    if area_phase >= 4:
                        # Emergency/Catastrophe area
                        phase_dist = {
                            1: 0.05,
                            2: 0.15,
                            3: 0.30,
                            4: 0.35,
                            5: 0.15
                        }
                    elif area_phase == 3:
                        # Crisis area
                        phase_dist = {
                            1: 0.10,
                            2: 0.25,
                            3: 0.40,
                            4: 0.20,
                            5: 0.05
                        }
                    else:
                        # Stressed area
                        phase_dist = {
                            1: 0.30,
                            2: 0.40,
                            3: 0.20,
                            4: 0.08,
                            5: 0.02
                        }
                    
                    population_phase = {
                        phase: int(total_pop * pct)
                        for phase, pct in phase_dist.items()
                    }
                    
                    # Adjust to match total
                    diff = total_pop - sum(population_phase.values())
                    population_phase[2] += diff
                    
                    # Determine drivers based on area
                    drivers = []
                    if governorate in ["Sa'ada", "Hajjah", "Taiz"]:
                        drivers.extend(['Active conflict', 'Displacement'])
                    
                    drivers.extend(['High prices', 'Currency depreciation'])
                    
                    if projection_start.month in [5, 6, 7, 8]:
                        drivers.append('Reduced rainfall')
                    
                    ipc = IPCData(
                        governorate=governorate,
                        district=district,
                        analysis_date=analysis_date,
                        projection_period_start=projection_start,
                        projection_period_end=projection_end,
                        phase=area_phase,
                        population_phase=population_phase,
                        total_population=total_pop,
                        drivers=drivers,
                        confidence_level=np.random.choice(['High', 'Medium'], p=[0.3, 0.7]),
                        source='IPC (Synthetic)'
                    )
                    
                    ipc_data.append(ipc)
        
        return ipc_data
    
    async def validate_specific(self, raw_data: List[IPCData]) -> ValidationReport:
        """Validate IPC data specifics."""
        report = ValidationReport(source=self.config.source_id)
        
        if not raw_data:
            report.add_validation(
                "data",
                ValLevel.ERROR,
                "No IPC data available"
            )
            return report
        
        # Check data quality
        total_records = len(raw_data)
        
        # Check phase validity
        invalid_phases = sum(1 for ipc in raw_data if ipc.phase not in range(1, 6))
        if invalid_phases > 0:
            report.add_validation(
                "phase_validity",
                ValLevel.ERROR,
                f"{invalid_phases} records with invalid IPC phase"
            )
        
        # Check population consistency
        pop_errors = 0
        for ipc in raw_data:
            if ipc.population_phase:
                calculated_total = sum(ipc.population_phase.values())
                if abs(calculated_total - ipc.total_population) > ipc.total_population * 0.05:
                    pop_errors += 1
        
        if pop_errors > 0:
            report.add_validation(
                "population_consistency",
                ValLevel.WARNING,
                f"{pop_errors} records with inconsistent population totals"
            )
        
        # Check temporal coverage
        dates = [ipc.analysis_date for ipc in raw_data]
        date_range = (max(dates) - min(dates)).days
        
        if date_range < 365:
            report.add_validation(
                "temporal_coverage",
                ValLevel.WARNING,
                f"Less than 1 year of IPC data (range: {date_range} days)"
            )
        
        # Check geographic coverage
        governorates = set(ipc.governorate for ipc in raw_data)
        expected_governorates = 22  # Yemen has 22 governorates
        
        if len(governorates) < expected_governorates * 0.8:
            report.add_validation(
                "geographic_coverage",
                ValLevel.WARNING,
                f"Only {len(governorates)} governorates covered (expected ~{expected_governorates})"
            )
        
        return report
    
    async def transform(self, raw_data: List[IPCData]) -> List[IPCClassification]:
        """Transform IPC data to domain entities."""
        classifications = []
        
        for ipc_data in raw_data:
            try:
                # Map string drivers to enum
                driver_categories = []
                for driver in ipc_data.drivers:
                    driver_lower = driver.lower()
                    for category, keywords in self.DRIVER_CATEGORIES.items():
                        if any(keyword.lower() in driver_lower for keyword in keywords):
                            driver_categories.append(DriverCategory[category.upper()])
                            break
                
                # Create analysis period
                period = AnalysisPeriod(
                    start_date=ipc_data.projection_period_start,
                    end_date=ipc_data.projection_period_end,
                    is_projection=True
                )
                
                # Create classification
                classification = IPCClassification(
                    area_name=f"{ipc_data.governorate} - {ipc_data.district}",
                    area_code=f"{ipc_data.governorate}_{ipc_data.district}".lower().replace(' ', '_'),
                    admin_level=AdministrativeLevel.DISTRICT,
                    analysis_period=period,
                    phase=IPCPhase(f"Phase{ipc_data.phase}"),
                    phase_population=ipc_data.population_phase,
                    total_population=ipc_data.total_population,
                    drivers=driver_categories,
                    confidence_level=ipc_data.confidence_level,
                    metadata={
                        'governorate': ipc_data.governorate,
                        'district': ipc_data.district,
                        'analysis_date': ipc_data.analysis_date.isoformat(),
                        'source': ipc_data.source
                    }
                )
                
                classifications.append(classification)
                
            except Exception as e:
                self.logger.warning(
                    f"Failed to transform IPC data for {ipc_data.district}",
                    error=str(e)
                )
        
        return classifications
    
    async def aggregate(self, entities: List[IPCClassification]) -> pd.DataFrame:
        """Aggregate IPC classifications for analysis."""
        if not entities:
            return pd.DataFrame()
        
        # Convert to dataframe
        records = []
        for classification in entities:
            # Calculate key metrics
            phase_3_plus = sum(
                pop for phase, pop in classification.phase_population.items()
                if phase >= 3
            )
            phase_4_plus = sum(
                pop for phase, pop in classification.phase_population.items()
                if phase >= 4
            )
            
            record = {
                'area_code': classification.area_code,
                'area_name': classification.area_name,
                'governorate': classification.metadata.get('governorate'),
                'district': classification.metadata.get('district'),
                'analysis_date': pd.to_datetime(classification.metadata.get('analysis_date')),
                'projection_start': classification.analysis_period.start_date,
                'projection_end': classification.analysis_period.end_date,
                'area_phase': int(classification.phase.value[-1]),  # Extract number from "Phase3"
                'total_population': classification.total_population,
                'phase_3_plus_population': phase_3_plus,
                'phase_4_plus_population': phase_4_plus,
                'phase_3_plus_percent': (phase_3_plus / classification.total_population * 100
                                        if classification.total_population > 0 else 0),
                'phase_4_plus_percent': (phase_4_plus / classification.total_population * 100
                                        if classification.total_population > 0 else 0),
                'n_drivers': len(classification.drivers),
                'has_conflict_driver': any(d == DriverCategory.CONFLICT for d in classification.drivers),
                'has_economic_driver': any(d == DriverCategory.ECONOMIC for d in classification.drivers),
                'confidence_level': classification.confidence_level
            }
            
            # Add population by phase
            for phase in range(1, 6):
                record[f'phase_{phase}_population'] = classification.phase_population.get(phase, 0)
            
            records.append(record)
        
        df = pd.DataFrame(records)
        
        # Add temporal grouping
        df['year'] = df['projection_start'].dt.year
        df['quarter'] = df['projection_start'].dt.quarter
        df['temporal_key'] = df['year'].astype(str) + '-Q' + df['quarter'].astype(str)
        
        # Calculate severity score (weighted average of phases)
        phase_weights = {1: 1, 2: 2, 3: 4, 4: 8, 5: 16}  # Exponential weights
        
        df['severity_score'] = 0
        for phase in range(1, 6):
            df['severity_score'] += (
                df[f'phase_{phase}_population'] * phase_weights[phase]
            )
        df['severity_score'] = df['severity_score'] / df['total_population']
        
        # Sort by date and location
        df.sort_values(['projection_start', 'governorate', 'district'], inplace=True)
        
        return df
    
    async def calculate_metrics(self, data: pd.DataFrame) -> FoodSecurityMetrics:
        """Calculate food security metrics from IPC data."""
        metrics = {}
        
        if data.empty:
            return FoodSecurityMetrics(**metrics)
        
        # Latest data only
        latest_date = data['projection_start'].max()
        latest_data = data[data['projection_start'] == latest_date]
        
        # Overall metrics
        total_population = latest_data['total_population'].sum()
        phase_3_plus_total = latest_data['phase_3_plus_population'].sum()
        phase_4_plus_total = latest_data['phase_4_plus_population'].sum()
        
        metrics['total_population_analyzed'] = int(total_population)
        metrics['population_phase_3_plus'] = int(phase_3_plus_total)
        metrics['population_phase_4_plus'] = int(phase_4_plus_total)
        metrics['percent_phase_3_plus'] = float(phase_3_plus_total / total_population * 100)
        metrics['percent_phase_4_plus'] = float(phase_4_plus_total / total_population * 100)
        
        # Area phase distribution
        phase_counts = latest_data['area_phase'].value_counts().to_dict()
        metrics['areas_by_phase'] = {
            f"phase_{k}": v for k, v in phase_counts.items()
        }
        
        # Most affected areas
        worst_areas = latest_data.nlargest(5, 'severity_score')[
            ['area_name', 'severity_score', 'phase_3_plus_percent']
        ].to_dict('records')
        metrics['most_affected_areas'] = worst_areas
        
        # Governorate summary
        gov_summary = latest_data.groupby('governorate').agg({
            'total_population': 'sum',
            'phase_3_plus_population': 'sum',
            'severity_score': 'mean'
        }).round(2)
        
        gov_summary['phase_3_plus_percent'] = (
            gov_summary['phase_3_plus_population'] / 
            gov_summary['total_population'] * 100
        ).round(1)
        
        metrics['governorate_summary'] = gov_summary.to_dict('index')
        
        # Trend analysis (if multiple time periods)
        if len(data['temporal_key'].unique()) > 1:
            trend = data.groupby('temporal_key').agg({
                'phase_3_plus_population': 'sum',
                'phase_4_plus_population': 'sum',
                'severity_score': 'mean'
            }).round(2)
            
            # Calculate change
            if len(trend) >= 2:
                latest_3plus = trend['phase_3_plus_population'].iloc[-1]
                previous_3plus = trend['phase_3_plus_population'].iloc[-2]
                change_3plus = ((latest_3plus - previous_3plus) / previous_3plus * 100)
                
                metrics['trend_phase_3_plus_change_pct'] = float(change_3plus)
                metrics['trend_direction'] = 'improving' if change_3plus < 0 else 'deteriorating'
        
        # Driver analysis
        driver_counts = {
            'conflict': latest_data['has_conflict_driver'].sum(),
            'economic': latest_data['has_economic_driver'].sum()
        }
        metrics['primary_drivers'] = driver_counts
        
        return FoodSecurityMetrics(**metrics)