"""Wrapper to add resilience to data processors."""

import asyncio
from typing import Any, Dict, List, Optional, TypeVar
from datetime import datetime
import logging

from src.infrastructure.processors.base_processor import BaseProcessor
from src.infrastructure.resilience import (
    CircuitBreaker,
    CircuitBreakerConfig,
    circuit_registry,
    ExponentialBackoffStrategy,
    RetryConfig,
    RetryManager,
    SERVICE_RETRY_CONFIGS
)
from src.infrastructure.queues import DeadLetterQueue
from src.core.domain.base import ValidationResult

logger = logging.getLogger(__name__)

T = TypeVar('T', bound=BaseProcessor)


class ResilientProcessorWrapper:
    """
    Wraps a data processor with resilience features.
    
    Features:
    - Circuit breaker protection
    - Retry with exponential backoff
    - Dead letter queue for failed records
    - Monitoring and metrics
    """
    
    def __init__(
        self,
        processor: T,
        dlq: DeadLetterQueue,
        circuit_config: Optional[CircuitBreakerConfig] = None,
        retry_config: Optional[RetryConfig] = None
    ):
        self.processor = processor
        self.dlq = dlq
        self.processor_name = processor.__class__.__name__
        
        # Setup circuit breaker
        self.circuit_breaker = circuit_registry.register(
            self.processor_name,
            circuit_config or CircuitBreakerConfig(
                failure_threshold=5,
                recovery_timeout=60,
                expected_exception=Exception
            )
        )
        
        # Setup retry manager
        retry_cfg = retry_config or SERVICE_RETRY_CONFIGS.get(
            self.processor_name.lower().replace('processor', ''),
            RetryConfig(max_attempts=3)
        )
        self.retry_manager = RetryManager(
            strategy=ExponentialBackoffStrategy(retry_cfg),
            on_retry=self._on_retry
        )
        
        self._processing_stats = {
            'total_processed': 0,
            'total_failed': 0,
            'dlq_sent': 0,
            'circuit_opens': 0
        }
        
    def _on_retry(self, attempt):
        """Callback for retry attempts."""
        logger.warning(
            f"Retrying {self.processor_name} after failure: "
            f"Attempt {attempt.attempt_number}"
        )
        
    async def process(self, *args, **kwargs) -> Any:
        """
        Process data with resilience features.
        
        Returns:
            Processed data or partial results with failures in DLQ
        """
        try:
            # Execute with circuit breaker and retry
            result = await self.circuit_breaker.call(
                self._process_with_retry,
                *args,
                **kwargs
            )
            
            self._processing_stats['total_processed'] += 1
            return result
            
        except Exception as e:
            logger.error(
                f"{self.processor_name} failed after all attempts: {e}"
            )
            self._processing_stats['total_failed'] += 1
            
            # Check if circuit opened
            if self.circuit_breaker.is_open:
                self._processing_stats['circuit_opens'] += 1
            
            # Send to DLQ if we have input data
            if args or kwargs:
                await self._send_to_dlq(args, kwargs, e)
                
            # Return empty result
            return self._get_empty_result()
            
    async def _process_with_retry(self, *args, **kwargs) -> Any:
        """Process with retry logic."""
        return await self.retry_manager.execute_with_retry(
            self.processor.process,
            *args,
            operation_name=self.processor_name,
            **kwargs
        )
        
    async def _send_to_dlq(
        self,
        args: tuple,
        kwargs: dict,
        error: Exception
    ) -> None:
        """Send failed record to dead letter queue."""
        try:
            record_data = {
                'args': args,
                'kwargs': kwargs,
                'processor': self.processor_name,
                'timestamp': datetime.now().isoformat()
            }
            
            await self.dlq.enqueue(
                source=self.processor_name,
                record_type='processor_input',
                data=record_data,
                error=error,
                metadata={
                    'circuit_state': self.circuit_breaker.state.value,
                    'retry_count': self.retry_manager.strategy.config.max_attempts
                }
            )
            
            self._processing_stats['dlq_sent'] += 1
            
        except Exception as dlq_error:
            logger.error(
                f"Failed to send to DLQ: {dlq_error}",
                exc_info=True
            )
            
    def _get_empty_result(self) -> Any:
        """Get empty result based on processor type."""
        # Try to get empty result from processor
        if hasattr(self.processor, 'get_empty_result'):
            return self.processor.get_empty_result()
            
        # Default empty results by processor type
        if 'dataframe' in self.processor_name.lower():
            import pandas as pd
            return pd.DataFrame()
        elif 'list' in str(type(self.processor.process).__annotations__.get('return', '')).lower():
            return []
        elif 'dict' in str(type(self.processor.process).__annotations__.get('return', '')).lower():
            return {}
        else:
            return None
            
    async def process_batch(
        self,
        items: List[Any],
        batch_size: int = 100
    ) -> Dict[str, Any]:
        """
        Process items in batches with partial failure handling.
        
        Returns:
            Dictionary with successful results and failure statistics
        """
        results = []
        failures = []
        
        # Process in batches
        for i in range(0, len(items), batch_size):
            batch = items[i:i + batch_size]
            
            try:
                # Process batch
                batch_result = await self.process(batch)
                results.extend(batch_result if isinstance(batch_result, list) else [batch_result])
                
            except Exception as e:
                logger.error(f"Batch {i//batch_size + 1} failed: {e}")
                
                # Process items individually
                for item in batch:
                    try:
                        item_result = await self.process(item)
                        results.append(item_result)
                    except Exception as item_error:
                        failures.append({
                            'item': item,
                            'error': str(item_error)
                        })
                        
        return {
            'successful': results,
            'failed': failures,
            'success_rate': len(results) / len(items) if items else 0,
            'stats': self.get_stats()
        }
        
    def get_stats(self) -> Dict[str, Any]:
        """Get processing statistics."""
        circuit_stats = self.circuit_breaker.get_stats()
        retry_stats = self.retry_manager.get_stats()
        
        return {
            'processor': self.processor_name,
            'processing': self._processing_stats,
            'circuit_breaker': circuit_stats,
            'retry': retry_stats
        }
        
    async def health_check(self) -> Dict[str, Any]:
        """Check health of the wrapped processor."""
        health = {
            'processor': self.processor_name,
            'circuit_state': self.circuit_breaker.state.value,
            'is_healthy': self.circuit_breaker.is_closed,
            'stats': self.get_stats()
        }
        
        # Check if processor has its own health check
        if hasattr(self.processor, 'health_check'):
            try:
                processor_health = await self.processor.health_check()
                health['processor_health'] = processor_health
            except Exception as e:
                health['processor_health'] = {
                    'error': str(e),
                    'healthy': False
                }
                
        return health


class ResilientProcessorFactory:
    """Factory for creating resilient processor wrappers."""
    
    def __init__(self, dlq: DeadLetterQueue):
        self.dlq = dlq
        self._processors: Dict[str, ResilientProcessorWrapper] = {}
        
    def wrap_processor(
        self,
        processor: BaseProcessor,
        circuit_config: Optional[CircuitBreakerConfig] = None,
        retry_config: Optional[RetryConfig] = None
    ) -> ResilientProcessorWrapper:
        """Create or get resilient wrapper for processor."""
        processor_name = processor.__class__.__name__
        
        if processor_name not in self._processors:
            self._processors[processor_name] = ResilientProcessorWrapper(
                processor=processor,
                dlq=self.dlq,
                circuit_config=circuit_config,
                retry_config=retry_config
            )
            
        return self._processors[processor_name]
        
    def get_all_stats(self) -> Dict[str, Dict[str, Any]]:
        """Get statistics for all wrapped processors."""
        return {
            name: wrapper.get_stats()
            for name, wrapper in self._processors.items()
        }
        
    async def health_check_all(self) -> Dict[str, Dict[str, Any]]:
        """Health check all wrapped processors."""
        health_results = {}
        
        for name, wrapper in self._processors.items():
            health_results[name] = await wrapper.health_check()
            
        return health_results