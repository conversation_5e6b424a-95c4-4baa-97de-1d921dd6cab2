"""
Processor for population data including WorldPop and IOM DTM displacement data.

This processor handles population density and displacement data extraction for market areas.
"""

import asyncio
from datetime import datetime
from pathlib import Path
from typing import List, Optional, Dict, Any, Tuple
import numpy as np
import pandas as pd
import geopandas as gpd
from shapely.geometry import Point, Polygon
import rasterio
from rasterio.mask import mask
import structlog

from src.infrastructure.processors.geo_processor import GeoDataProcessor
from src.infrastructure.processors.base_processor import (
    SourceConfig, ValidationLevel
)
from src.infrastructure.data_quality.validation_framework import (
    ValidationReport, ValidationLevel as ValLevel
)
from src.core.domain.population.entities import (
    PopulationObservation, DisplacementFlow, PopulationMetrics
)
from src.core.domain.population.value_objects import (
    DisplacementType, PopulationGroup, AssistanceType,
    ReturnIntention, DisplacementReason, PopulationDemographics,
    LocationCoordinates
)
from src.core.domain.market.value_objects import Coordinates
from src.infrastructure.processors.exceptions import (
    DataQualityException, TransformationException
)


logger = structlog.get_logger()


class PopulationDataProcessor(GeoDataProcessor[PopulationObservation]):
    """
    Processor for population data from WorldPop and IOM DTM.
    
    Features:
        - WorldPop raster data processing (100m resolution)
        - IOM DTM displacement tracking
        - Market catchment area population calculation
        - Urban/rural classification based on density
        - Displacement flow analysis
    """
    
    # Population density thresholds (people per km²)
    DENSITY_THRESHOLDS = {
        'urban_core': 1500,      # Dense urban
        'urban_cluster': 300,    # Urban cluster
        'rural': 150,           # Rural threshold
        'sparse': 50            # Very sparse
    }
    
    # Market catchment radii (in km)
    CATCHMENT_RADII = [5, 10, 25, 50]
    
    def __init__(
        self,
        source_config: SourceConfig,
        cache_manager,
        validator,
        data_source: str,  # 'worldpop' or 'iom_dtm'
        market_locations: Optional[gpd.GeoDataFrame] = None,
        admin_boundaries: Optional[gpd.GeoDataFrame] = None
    ):
        super().__init__(source_config, cache_manager, validator)
        self.data_source = data_source.lower()
        self.market_locations = market_locations
        self.admin_boundaries = admin_boundaries
        
        self.logger = logger.bind(
            processor="PopulationDataProcessor",
            source=data_source
        )
    
    async def download(self) -> Any:
        """Download population data from source."""
        if self.data_source == 'worldpop':
            return await self._download_worldpop()
        elif self.data_source == 'iom_dtm':
            return await self._download_iom_dtm()
        else:
            raise ValueError(f"Unknown data source: {self.data_source}")
    
    async def _download_worldpop(self) -> rasterio.DatasetReader:
        """Download WorldPop raster data."""
        # In production, download from:
        # https://www.worldpop.org/geodata/summary?id=49718
        
        # For now, create synthetic raster
        return self._create_synthetic_population_raster()
    
    async def _download_iom_dtm(self) -> pd.DataFrame:
        """Download IOM DTM displacement data."""
        # In production, download from HDX or IOM API
        
        # Create synthetic displacement data
        return self._create_synthetic_displacement_data()
    
    def _create_synthetic_population_raster(self):
        """Create synthetic population density raster."""
        # Yemen bounds with 100m resolution
        west, south, east, north = 42, 12, 54, 20
        
        # Create coordinate arrays
        x_res = 0.00089  # ~100m at this latitude
        y_res = 0.00089
        
        x_coords = np.arange(west, east, x_res)
        y_coords = np.arange(north, south, -y_res)
        
        # Create population density grid
        # Higher density around major cities
        population_grid = np.zeros((len(y_coords), len(x_coords)), dtype=np.float32)
        
        # Major city centers
        cities = {
            'Sanaa': (44.2065, 15.3547, 2500000),
            'Aden': (45.0187, 12.7855, 800000),
            'Taiz': (44.0178, 13.5795, 700000),
            'Hodeidah': (42.9545, 14.7982, 600000),
            'Ibb': (44.1833, 13.9667, 400000)
        }
        
        # Create population distribution
        X, Y = np.meshgrid(x_coords, y_coords)
        
        for city, (lon, lat, pop) in cities.items():
            # Gaussian distribution around city center
            dist = np.sqrt((X - lon)**2 + (Y - lat)**2)
            
            # City core (high density)
            core_mask = dist < 0.05  # ~5km radius
            population_grid[core_mask] += pop * 0.5 / core_mask.sum()
            
            # Urban area (medium density)
            urban_mask = (dist >= 0.05) & (dist < 0.2)  # 5-20km
            if urban_mask.sum() > 0:
                population_grid[urban_mask] += pop * 0.3 / urban_mask.sum()
            
            # Suburban/rural (low density)
            suburban_mask = (dist >= 0.2) & (dist < 0.5)  # 20-50km
            if suburban_mask.sum() > 0:
                population_grid[suburban_mask] += pop * 0.2 / suburban_mask.sum()
        
        # Add rural background population
        rural_pop = 10  # people per pixel
        population_grid[population_grid == 0] = rural_pop
        
        # Convert to people per km²
        pixel_area_km2 = (x_res * 111) * (y_res * 111)  # rough conversion
        population_density = population_grid / pixel_area_km2
        
        # Save to temporary raster file
        import tempfile
        with tempfile.NamedTemporaryFile(suffix='.tif', delete=False) as tmp:
            profile = {
                'driver': 'GTiff',
                'height': len(y_coords),
                'width': len(x_coords),
                'count': 1,
                'dtype': 'float32',
                'crs': 'EPSG:4326',
                'transform': rasterio.transform.from_bounds(
                    west, south, east, north, len(x_coords), len(y_coords)
                )
            }
            
            with rasterio.open(tmp.name, 'w', **profile) as dst:
                dst.write(population_density, 1)
            
            # Open for reading
            return rasterio.open(tmp.name, 'r')
    
    def _create_synthetic_displacement_data(self) -> pd.DataFrame:
        """Create synthetic IOM DTM displacement data."""
        # Create displacement events
        dates = pd.date_range('2024-01-01', '2024-12-31', freq='M')
        
        displacement_data = []
        
        # Major displacement events
        events = [
            {
                'date': '2024-03-15',
                'from_governorate': "Sa'ada",
                'to_governorate': "Sana'a",
                'households': 1200,
                'reason': 'conflict',
                'type': 'new'
            },
            {
                'date': '2024-06-20',
                'from_governorate': 'Hodeidah',
                'to_governorate': 'Ibb',
                'households': 800,
                'reason': 'conflict',
                'type': 'new'
            },
            {
                'date': '2024-09-10',
                'from_governorate': 'Marib',
                'to_governorate': "Sana'a",
                'households': 1500,
                'reason': 'conflict',
                'type': 'new'
            }
        ]
        
        # Add returns
        for event in events[:2]:  # Some return after 3 months
            return_event = event.copy()
            return_event['date'] = pd.to_datetime(event['date']) + pd.DateOffset(months=3)
            return_event['from_governorate'] = event['to_governorate']
            return_event['to_governorate'] = event['from_governorate']
            return_event['households'] = event['households'] * 0.3  # 30% return
            return_event['type'] = 'return'
            displacement_data.append(return_event)
        
        displacement_data.extend(events)
        
        # Add secondary displacements
        for _ in range(20):
            date = np.random.choice(dates)
            from_gov = np.random.choice(['Taiz', 'Hajjah', 'Al Bayda', 'Abyan'])
            to_gov = np.random.choice(["Sana'a", 'Aden', 'Ibb', 'Dhamar'])
            
            displacement_data.append({
                'date': date,
                'from_governorate': from_gov,
                'to_governorate': to_gov,
                'households': np.random.randint(50, 300),
                'reason': np.random.choice(['conflict', 'drought', 'economic']),
                'type': np.random.choice(['new', 'secondary'])
            })
        
        df = pd.DataFrame(displacement_data)
        df['date'] = pd.to_datetime(df['date'])
        df['individuals'] = df['households'] * 7  # Average household size
        
        return df.sort_values('date')
    
    async def validate_specific(self, raw_data: Any) -> ValidationReport:
        """Validate population data specifics."""
        report = ValidationReport(source=self.config.source_id)
        
        if self.data_source == 'worldpop':
            # Validate raster
            if not hasattr(raw_data, 'read'):
                report.add_validation(
                    "data_type",
                    ValLevel.ERROR,
                    "Invalid raster data format"
                )
                return report
            
            # Check CRS
            if raw_data.crs is None:
                report.add_validation(
                    "crs",
                    ValLevel.ERROR,
                    "No coordinate reference system defined"
                )
            
            # Check data values
            data = raw_data.read(1)
            
            # Check for no data values
            nodata_count = np.isnan(data).sum()
            total_pixels = data.size
            nodata_percent = (nodata_count / total_pixels) * 100
            
            if nodata_percent > 50:
                report.add_validation(
                    "nodata",
                    ValLevel.ERROR,
                    f"Too many no-data pixels: {nodata_percent:.1f}%"
                )
            
            # Check for negative values
            if (data[~np.isnan(data)] < 0).any():
                report.add_validation(
                    "negative_values",
                    ValLevel.ERROR,
                    "Negative population values found"
                )
            
            # Check for unrealistic values
            max_density = np.nanmax(data)
            if max_density > 100000:  # people per km²
                report.add_validation(
                    "extreme_values",
                    ValLevel.WARNING,
                    f"Extremely high population density: {max_density:.0f} people/km²"
                )
                
        else:
            # Validate displacement data
            required_cols = ['date', 'from_governorate', 'to_governorate', 
                           'households', 'reason', 'type']
            missing_cols = [col for col in required_cols if col not in raw_data.columns]
            
            if missing_cols:
                report.add_validation(
                    "columns",
                    ValLevel.ERROR,
                    f"Missing required columns: {missing_cols}"
                )
            
            # Check data quality
            if 'households' in raw_data.columns:
                neg_households = (raw_data['households'] < 0).sum()
                if neg_households > 0:
                    report.add_validation(
                        "households",
                        ValLevel.ERROR,
                        f"{neg_households} negative household values"
                    )
            
            # Check date range
            if 'date' in raw_data.columns:
                raw_data['date'] = pd.to_datetime(raw_data['date'])
                future_dates = raw_data['date'] > pd.Timestamp.now()
                if future_dates.any():
                    report.add_validation(
                        "dates",
                        ValLevel.WARNING,
                        f"{future_dates.sum()} future dates found"
                    )
        
        return report
    
    async def transform(self, raw_data: Any) -> List[PopulationObservation]:
        """Transform raw population data to PopulationObservation entities."""
        if self.data_source == 'worldpop':
            return await self._transform_worldpop(raw_data)
        else:
            return await self._transform_displacement(raw_data)
    
    async def _transform_worldpop(self, raster_data) -> List[PopulationObservation]:
        """Transform WorldPop raster to observations."""
        if self.market_locations is None:
            raise ValueError("Market locations required for population extraction")
        
        observations = []
        
        # Read full raster
        population_density = raster_data.read(1)
        transform = raster_data.transform
        
        for _, market in self.market_locations.iterrows():
            try:
                market_id = market.get('market_id', 'unknown')
                location = Coordinates(
                    latitude=market.geometry.y,
                    longitude=market.geometry.x
                )
                
                # Calculate population for different catchment areas
                for radius_km in self.CATCHMENT_RADII:
                    # Create buffer polygon
                    buffer_degrees = radius_km / 111  # rough conversion
                    buffer_poly = market.geometry.buffer(buffer_degrees)
                    
                    # Extract population within buffer
                    population = self._extract_population_in_polygon(
                        population_density,
                        buffer_poly,
                        transform
                    )
                    
                    # Calculate density
                    area_km2 = np.pi * radius_km ** 2
                    density = population / area_km2 if area_km2 > 0 else 0
                    
                    # Classify urban/rural
                    if density > self.DENSITY_THRESHOLDS['urban_core']:
                        pop_type = PopulationType.URBAN_CORE
                    elif density > self.DENSITY_THRESHOLDS['urban_cluster']:
                        pop_type = PopulationType.URBAN_CLUSTER
                    elif density > self.DENSITY_THRESHOLDS['rural']:
                        pop_type = PopulationType.RURAL
                    else:
                        pop_type = PopulationType.SPARSE
                    
                    # Create observation
                    obs = PopulationObservation(
                        observation_date=datetime.now(),  # Static data
                        location=location,
                        population_count=int(population),
                        population_type=pop_type,
                        catchment_radius_km=radius_km,
                        density_per_km2=density,
                        data_source='worldpop',
                        confidence_score=0.85,  # WorldPop confidence
                        metadata={
                            'market_id': market_id,
                            'year': 2024,  # WorldPop year
                            'resolution_m': 100
                        }
                    )
                    
                    observations.append(obs)
                    
            except Exception as e:
                self.logger.warning(
                    "Failed to extract population for market",
                    market_id=market_id,
                    error=str(e)
                )
        
        return observations
    
    async def _transform_displacement(self, df: pd.DataFrame) -> List[DisplacementEvent]:
        """Transform IOM DTM data to DisplacementEvent entities."""
        events = []
        
        for _, row in df.iterrows():
            try:
                # Map reason
                reason_map = {
                    'conflict': DisplacementReason.CONFLICT,
                    'drought': DisplacementReason.CLIMATE,
                    'economic': DisplacementReason.ECONOMIC,
                    'flood': DisplacementReason.CLIMATE,
                    'other': DisplacementReason.OTHER
                }
                reason = reason_map.get(
                    row.get('reason', '').lower(), 
                    DisplacementReason.OTHER
                )
                
                # Map type
                type_map = {
                    'new': DisplacementType.NEW,
                    'secondary': DisplacementType.SECONDARY,
                    'return': DisplacementType.RETURN
                }
                disp_type = type_map.get(
                    row.get('type', '').lower(),
                    DisplacementType.NEW
                )
                
                # Create event
                event = DisplacementEvent(
                    event_date=row['date'],
                    from_location=self._get_governorate_location(row['from_governorate']),
                    to_location=self._get_governorate_location(row['to_governorate']),
                    households_displaced=int(row['households']),
                    individuals_displaced=int(row.get('individuals', row['households'] * 7)),
                    displacement_type=disp_type,
                    displacement_reason=reason,
                    duration_days=row.get('duration_days'),
                    metadata={
                        'from_governorate': row['from_governorate'],
                        'to_governorate': row['to_governorate'],
                        'source': 'IOM DTM'
                    }
                )
                
                events.append(event)
                
            except Exception as e:
                self.logger.warning(
                    "Failed to transform displacement event",
                    row_index=_,
                    error=str(e)
                )
        
        return events
    
    def _extract_population_in_polygon(
        self,
        raster_data: np.ndarray,
        polygon: Polygon,
        transform
    ) -> float:
        """Extract population sum within polygon."""
        # In production, use rasterio.mask
        # For now, simplified extraction
        
        # Get polygon bounds
        minx, miny, maxx, maxy = polygon.bounds
        
        # Convert to pixel coordinates
        col_min, row_max = ~transform * (minx, miny)
        col_max, row_min = ~transform * (maxx, maxy)
        
        # Ensure within raster bounds
        row_min = max(0, int(row_min))
        row_max = min(raster_data.shape[0], int(row_max) + 1)
        col_min = max(0, int(col_min))
        col_max = min(raster_data.shape[1], int(col_max) + 1)
        
        # Extract subset and sum
        subset = raster_data[row_min:row_max, col_min:col_max]
        
        # Simple sum (in production, mask by actual polygon shape)
        population = np.nansum(subset)
        
        return population
    
    def _get_governorate_location(self, governorate: str) -> Coordinates:
        """Get centroid coordinates for governorate."""
        # Governorate centroids
        centroids = {
            "Sana'a": (44.2065, 15.3547),
            'Aden': (45.0187, 12.7855),
            'Taiz': (44.0178, 13.5795),
            'Hodeidah': (42.9545, 14.7982),
            'Ibb': (44.1833, 13.9667),
            "Sa'ada": (43.7612, 16.9251),
            'Marib': (45.3256, 15.4625),
            'Hajjah': (43.6058, 15.6943),
            'Dhamar': (44.4010, 14.5430),
            'Al Bayda': (45.5729, 13.9834),
            'Abyan': (45.8333, 13.5667)
        }
        
        lon, lat = centroids.get(governorate, (44.5, 15.0))
        return Coordinates(latitude=lat, longitude=lon)
    
    async def aggregate(
        self, 
        entities: List[PopulationObservation]
    ) -> pd.DataFrame:
        """Aggregate population observations to market level."""
        if not entities:
            return pd.DataFrame()
        
        # Convert to dataframe
        records = []
        
        if isinstance(entities[0], PopulationObservation):
            # Population data
            for obs in entities:
                record = {
                    'market_id': obs.metadata.get('market_id', 'unknown'),
                    'latitude': obs.location.latitude,
                    'longitude': obs.location.longitude,
                    f'population_{obs.catchment_radius_km}km': obs.population_count,
                    f'density_{obs.catchment_radius_km}km': obs.density_per_km2,
                    f'type_{obs.catchment_radius_km}km': obs.population_type.value,
                    'data_source': obs.data_source,
                    'year': obs.metadata.get('year', 2024)
                }
                records.append(record)
                
        else:
            # Displacement data
            for event in entities:
                record = {
                    'date': event.event_date,
                    'year': event.event_date.year,
                    'month': event.event_date.month,
                    'from_governorate': event.metadata.get('from_governorate'),
                    'to_governorate': event.metadata.get('to_governorate'),
                    'households': event.households_displaced,
                    'individuals': event.individuals_displaced,
                    'type': event.displacement_type.value,
                    'reason': event.displacement_reason.value
                }
                records.append(record)
        
        df = pd.DataFrame(records)
        
        if 'market_id' in df.columns:
            # Aggregate population data by market
            # Pivot to get one row per market with all radii
            aggregated = df.pivot_table(
                index=['market_id', 'latitude', 'longitude', 'data_source', 'year'],
                values=[col for col in df.columns if col.startswith(('population_', 'density_', 'type_'))],
                aggfunc='first'
            ).reset_index()
            
            # Add derived metrics
            if 'population_5km' in aggregated.columns and 'population_10km' in aggregated.columns:
                aggregated['population_growth_5_10km'] = (
                    aggregated['population_10km'] / aggregated['population_5km']
                )
            
            # Market accessibility score based on population
            if 'population_25km' in aggregated.columns:
                aggregated['accessibility_score'] = np.log1p(aggregated['population_25km']) / 10
                
        else:
            # Aggregate displacement by month and route
            aggregated = df.groupby(
                ['year', 'month', 'from_governorate', 'to_governorate', 'type', 'reason']
            ).agg({
                'households': 'sum',
                'individuals': 'sum',
                'date': 'count'
            }).reset_index()
            
            aggregated.rename(columns={'date': 'n_events'}, inplace=True)
            
            # Add temporal key
            aggregated['temporal_key'] = (
                aggregated['year'].astype(str) + '-' + 
                aggregated['month'].astype(str).str.zfill(2)
            )
        
        return aggregated
    
    async def calculate_metrics(self, data: pd.DataFrame) -> PopulationMetrics:
        """Calculate population metrics for the dataset."""
        metrics = {}
        
        if 'market_id' in data.columns:
            # Population metrics
            for radius in self.CATCHMENT_RADII:
                col = f'population_{radius}km'
                if col in data.columns:
                    metrics[f'avg_population_{radius}km'] = float(data[col].mean())
                    metrics[f'total_population_{radius}km'] = int(data[col].sum())
            
            # Urban/rural distribution
            if 'type_5km' in data.columns:
                type_counts = data['type_5km'].value_counts()
                total_markets = len(data)
                
                for pop_type in ['urban_core', 'urban_cluster', 'rural', 'sparse']:
                    count = type_counts.get(pop_type, 0)
                    metrics[f'{pop_type}_percent'] = float(count / total_markets * 100)
            
            # Accessibility
            if 'accessibility_score' in data.columns:
                metrics['avg_accessibility'] = float(data['accessibility_score'].mean())
                
        else:
            # Displacement metrics
            if 'individuals' in data.columns:
                metrics['total_displaced'] = int(data['individuals'].sum())
                metrics['total_households'] = int(data['households'].sum())
                metrics['avg_household_size'] = float(
                    data['individuals'].sum() / data['households'].sum()
                )
            
            # By type
            if 'type' in data.columns:
                type_totals = data.groupby('type')['individuals'].sum()
                for disp_type, total in type_totals.items():
                    metrics[f'{disp_type}_displaced'] = int(total)
            
            # By reason
            if 'reason' in data.columns:
                reason_totals = data.groupby('reason')['individuals'].sum()
                for reason, total in reason_totals.items():
                    metrics[f'{reason}_displaced'] = int(total)
        
        return PopulationMetrics(**metrics)