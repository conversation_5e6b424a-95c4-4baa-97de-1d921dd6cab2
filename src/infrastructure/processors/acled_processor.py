"""ACLED conflict data processor for V2 architecture.

Adapts V1 ACLED processor logic to work with V2 domain models and async patterns.
"""

import asyncio
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Union
import logging

import pandas as pd
import numpy as np
import geopandas as gpd
from shapely.geometry import Point

from ...core.domain.conflict.entities import ConflictEvent
from ...core.domain.conflict.value_objects import ConflictType, ConflictIntensity, ImpactRadius
from ...core.domain.market.entities import Market
from ...core.domain.market.value_objects import Coordinates
from ..external_services.acled_client import ACLEDClient
from ...shared.plugins.interfaces import DataProcessor


logger = logging.getLogger(__name__)


class ACLEDProcessor(DataProcessor):
    """Process ACLED conflict data for Yemen market analysis.

    Handles:
    - Parsing ACLED CSV/API data
    - Extracting conflict events with proper typing
    - Calculating conflict intensity metrics
    - Spatial matching to markets
    - Temporal aggregation for analysis
    - Converting to domain entities
    """

    # Conflict event type mappings
    EVENT_TYPE_MAPPINGS = {
        'Battles': ConflictType.BATTLE,
        'Explosions/Remote violence': ConflictType.EXPLOSIONS_REMOTE_VIOLENCE,
        'Violence against civilians': ConflictType.VIOLENCE_AGAINST_CIVILIANS,
        'Protests': ConflictType.PROTESTS,
        'Riots': ConflictType.RIOTS,
        'Strategic developments': ConflictType.STRATEGIC_DEVELOPMENTS
    }

    # Actors of interest for Yemen conflict
    KEY_ACTORS = [
        'Houthis',
        'Government of Yemen',
        'AQAP',
        'ISIS',
        'STC',
        'Saudi-led Coalition',
        'Military Forces of Yemen',
        'Pro-Hadi forces'
    ]

    def __init__(
        self,
        acled_client: Optional[ACLEDClient] = None,
        buffer_radius_km: float = 20.0
    ):
        """Initialize ACLED processor.

        Args:
            acled_client: Optional ACLED API client
            buffer_radius_km: Default buffer radius for spatial matching
        """
        self.acled_client = acled_client
        self.buffer_radius_km = buffer_radius_km
        self._events_cache: Dict[str, ConflictEvent] = {}

    async def process(
        self,
        data_source: Union[str, pd.DataFrame],
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        bounds: Optional[Tuple[float, float, float, float]] = None
    ) -> List[ConflictEvent]:
        """Process ACLED data and return conflict events.

        Args:
            data_source: Path to CSV file or DataFrame
            start_date: Start date for filtering
            end_date: End date for filtering
            bounds: Optional geographic bounds (minx, miny, maxx, maxy)

        Returns:
            List of ConflictEvent entities
        """
        try:
            # Load data
            if isinstance(data_source, str):
                logger.info(f"Loading ACLED data from {data_source}")
                df = await self._load_csv_async(data_source)
            else:
                df = data_source.copy()

            # Basic preprocessing
            df = self._preprocess_data(df)

            # Filter by date range
            if start_date or end_date:
                df = self._filter_by_date(df, start_date, end_date)

            # Filter by geographic bounds
            if bounds:
                df = self._filter_by_bounds(df, bounds)

            # Extract conflict events
            events = await self._extract_conflict_events(df)

            logger.info(f"Processed {len(events)} ACLED conflict events")

            return events

        except Exception as e:
            logger.error(f"Error processing ACLED data: {e}")
            raise

    async def aggregate_by_market_month(
        self,
        events: List[ConflictEvent],
        markets: List['Market'],
        buffer_radius_km: Optional[float] = None
    ) -> pd.DataFrame:
        """Aggregate conflict events by market and month.

        Args:
            events: List of conflict events
            markets: List of markets to match to
            buffer_radius_km: Buffer radius for spatial matching

        Returns:
            DataFrame with market-month conflict metrics
        """
        if buffer_radius_km is None:
            buffer_radius_km = self.buffer_radius_km

        # Convert to GeoDataFrames for spatial operations
        events_gdf = self._events_to_geodataframe(events)
        markets_gdf = self._markets_to_geodataframe(markets)

        # Buffer markets
        markets_gdf['geometry'] = markets_gdf.geometry.buffer(
            buffer_radius_km / 111  # Approximate km to degrees
        )

        # Spatial join
        joined = gpd.sjoin(
            events_gdf,
            markets_gdf[['market_id', 'geometry']],
            how='inner',
            predicate='within'
        )

        # Extract month
        joined['year_month'] = joined['event_date'].dt.to_period('M')

        # Aggregate metrics
        aggregated = joined.groupby(['market_id', 'year_month']).agg({
            'event_id': 'count',
            'fatalities': 'sum',
            'intensity_score': 'mean',
            'conflict_type': lambda x: x.mode()[0] if len(x) > 0 else None
        }).reset_index()

        aggregated.columns = [
            'market_id', 'year_month', 'event_count',
            'total_fatalities', 'avg_intensity', 'dominant_type'
        ]

        return aggregated

    async def _load_csv_async(self, filepath: str) -> pd.DataFrame:
        """Load CSV file asynchronously."""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, pd.read_csv, filepath)

    def _preprocess_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Preprocess ACLED data with standard cleaning."""
        # Rename columns to standard names
        column_mappings = {
            'event_date': 'event_date',
            'data_id': 'event_id',
            'event_type': 'event_type',
            'sub_event_type': 'sub_event_type',
            'actor1': 'actor1',
            'actor2': 'actor2',
            'fatalities': 'fatalities',
            'latitude': 'latitude',
            'longitude': 'longitude',
            'location': 'location_name',
            'admin1': 'governorate',
            'admin2': 'district',
            'notes': 'notes'
        }

        # Apply mappings for existing columns
        df = df.rename(columns={
            old: new for old, new in column_mappings.items()
            if old in df.columns
        })

        # Convert date
        if 'event_date' in df.columns:
            df['event_date'] = pd.to_datetime(df['event_date'])

        # Convert numeric columns
        numeric_columns = ['fatalities', 'latitude', 'longitude']
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # Remove rows with missing critical data
        critical_columns = ['event_date', 'latitude', 'longitude', 'event_type']
        existing_critical = [col for col in critical_columns if col in df.columns]
        df = df.dropna(subset=existing_critical)

        return df

    def _filter_by_date(
        self,
        df: pd.DataFrame,
        start_date: Optional[datetime],
        end_date: Optional[datetime]
    ) -> pd.DataFrame:
        """Filter dataframe by date range."""
        if 'event_date' not in df.columns:
            return df

        if start_date:
            df = df[df['event_date'] >= start_date]
        if end_date:
            df = df[df['event_date'] <= end_date]

        return df

    def _filter_by_bounds(
        self,
        df: pd.DataFrame,
        bounds: Tuple[float, float, float, float]
    ) -> pd.DataFrame:
        """Filter dataframe by geographic bounds."""
        if not all(col in df.columns for col in ['latitude', 'longitude']):
            return df

        minx, miny, maxx, maxy = bounds
        return df[
            (df['longitude'] >= minx) &
            (df['longitude'] <= maxx) &
            (df['latitude'] >= miny) &
            (df['latitude'] <= maxy)
        ]

    async def _extract_conflict_events(self, df: pd.DataFrame) -> List[ConflictEvent]:
        """Extract conflict events from dataframe."""
        events = []

        for _, row in df.iterrows():
            # Skip if already processed
            event_id = str(row.get('event_id', ''))
            if event_id and event_id in self._events_cache:
                events.append(self._events_cache[event_id])
                continue

            # Create coordinates
            coordinates = Coordinates(
                latitude=float(row['latitude']),
                longitude=float(row['longitude'])
            )

            # Determine conflict type
            conflict_type = self._map_conflict_type(row.get('event_type', ''))

            # Determine intensity
            fatalities = int(row.get('fatalities', 0))
            intensity = ConflictIntensity.from_fatalities(fatalities)

            # Extract actors
            actors = self._extract_actors(row)

            # Create description
            description = self._create_description(row)

            # Create event
            event = ConflictEvent(
                event_date=row['event_date'],
                location=coordinates,
                conflict_type=conflict_type,
                intensity=intensity,
                fatalities=fatalities,
                actors=actors,
                description=description,
                source="ACLED"
            )

            events.append(event)
            if event_id:
                self._events_cache[event_id] = event

        return events

    def _map_conflict_type(self, event_type: str) -> ConflictType:
        """Map ACLED event type to domain conflict type."""
        return self.EVENT_TYPE_MAPPINGS.get(
            event_type,
            ConflictType.OTHER
        )

    def _extract_actors(self, row: pd.Series) -> List[str]:
        """Extract relevant actors from event data."""
        actors = []

        # Primary actors
        for col in ['actor1', 'actor2']:
            if col in row and pd.notna(row[col]):
                actor = str(row[col])
                # Normalize actor names
                for key_actor in self.KEY_ACTORS:
                    if key_actor.lower() in actor.lower():
                        actors.append(key_actor)
                        break
                else:
                    # Add as-is if not a key actor
                    actors.append(actor)

        return list(set(actors))  # Remove duplicates

    def _create_description(self, row: pd.Series) -> str:
        """Create event description from available data."""
        parts = []

        # Event type and sub-type
        if 'event_type' in row:
            parts.append(str(row['event_type']))
        if 'sub_event_type' in row and pd.notna(row['sub_event_type']):
            parts.append(f"({row['sub_event_type']})")

        # Location
        if 'location_name' in row and pd.notna(row['location_name']):
            parts.append(f"at {row['location_name']}")

        # Notes (truncated)
        if 'notes' in row and pd.notna(row['notes']):
            notes = str(row['notes'])[:200]
            if len(row['notes']) > 200:
                notes += "..."
            parts.append(notes)

        return " ".join(parts)

    def _events_to_geodataframe(self, events: List[ConflictEvent]) -> gpd.GeoDataFrame:
        """Convert conflict events to GeoDataFrame."""
        data = []
        for event in events:
            data.append({
                'event_id': id(event),
                'event_date': event.event_date,
                'conflict_type': event.conflict_type.value,
                'intensity_score': event.intensity.value,
                'fatalities': event.fatalities,
                'geometry': Point(event.location.longitude, event.location.latitude)
            })

        return gpd.GeoDataFrame(data, crs='EPSG:4326')

    def _markets_to_geodataframe(self, markets: List['Market']) -> gpd.GeoDataFrame:
        """Convert markets to GeoDataFrame."""
        data = []
        for market in markets:
            if market.coordinates:
                data.append({
                    'market_id': market.market_id.value,
                    'geometry': Point(
                        market.coordinates.longitude,
                        market.coordinates.latitude
                    )
                })

        return gpd.GeoDataFrame(data, crs='EPSG:4326')
