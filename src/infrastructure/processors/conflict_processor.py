"""Processor for ACLED conflict data."""
import asyncio
from datetime import datetime, timed<PERSON><PERSON>
from decimal import Decimal
from typing import Dict, List, Optional, Tuple
import pandas as pd
import numpy as np
from geopy.distance import geodesic
import structlog

from src.core.domain.conflict.entities import ConflictEvent, ConflictMetrics
from src.core.domain.conflict.value_objects import EventType, ActorType, ConflictRadius
from src.core.domain.market.value_objects import Coordinates
from src.core.domain.shared.value_objects import TemporalKey
from src.core.domain.market.entities import Market
from src.infrastructure.processors.base_processor import DataFrameProcessor, SourceConfig
from src.infrastructure.data_quality.validation_framework import ValidationReport
from src.infrastructure.external_services.acled_client import ACLEDClient


logger = structlog.get_logger()


class ConflictProcessor(DataFrameProcessor[ConflictEvent]):
    """
    Processes ACLED conflict data into market-month metrics.
    
    Key features:
    - Spatial buffer calculations (10km, 25km, 50km)
    - Temporal aggregation with lags
    - Actor-specific event counting
    - Intensity index calculation
    """
    
    def __init__(
        self,
        source_config: SourceConfig,
        cache_manager,
        validator,
        acled_client: ACLEDClient,
        markets: List[Market]
    ):
        super().__init__(source_config, cache_manager, validator)
        self.acled_client = acled_client
        self.markets = markets
        self.market_coords = {
            m.id: (m.location.latitude, m.location.longitude)
            for m in markets
        }
        self.radius = ConflictRadius()
    
    async def download(self) -> pd.DataFrame:
        """Download ACLED data for Yemen."""
        self.logger.info("Downloading ACLED conflict data")
        
        # Get data for analysis period
        start_date = datetime(2019, 1, 1)
        end_date = datetime.now()
        
        events = await self.acled_client.get_events(
            country='Yemen',
            start_date=start_date,
            end_date=end_date
        )
        
        return pd.DataFrame(events)
    
    def get_required_columns(self) -> List[str]:
        """Required columns for ACLED data."""
        return [
            'data_id', 'event_date', 'event_type', 'sub_event_type',
            'actor1', 'actor2', 'country', 'admin1', 'admin2',
            'location', 'latitude', 'longitude', 'fatalities',
            'notes', 'source', 'source_scale'
        ]
    
    async def validate_specific(self, raw_data: pd.DataFrame) -> ValidationReport:
        """ACLED-specific validation."""
        report = ValidationReport(is_valid=True)
        
        # Check date range
        raw_data['event_date'] = pd.to_datetime(raw_data['event_date'])
        date_range = raw_data['event_date'].max() - raw_data['event_date'].min()
        
        if date_range.days < 365:
            report.add_warning(f"Limited date range: {date_range.days} days")
        
        # Check geographic coverage
        unique_admin1 = raw_data['admin1'].nunique()
        if unique_admin1 < 10:
            report.add_warning(f"Limited geographic coverage: {unique_admin1} governorates")
        
        # Check event type distribution
        event_types = raw_data['event_type'].value_counts()
        report.metrics['event_type_distribution'] = event_types.to_dict()
        
        # Validate coordinates
        invalid_coords = (
            (raw_data['latitude'].abs() > 90) |
            (raw_data['longitude'].abs() > 180)
        ).sum()
        
        if invalid_coords > 0:
            report.add_error(f"Found {invalid_coords} events with invalid coordinates")
        
        return report
    
    async def transform(self, raw_data: pd.DataFrame) -> List[ConflictEvent]:
        """Transform raw ACLED data to domain entities."""
        events = []
        
        for _, row in raw_data.iterrows():
            try:
                # Map event type
                event_type = self._map_event_type(row['event_type'])
                
                # Extract actors
                actors = self._extract_actors(row)
                
                # Create event
                event = ConflictEvent(
                    event_id=str(row['data_id']),
                    date=row['event_date'],
                    location=Coordinates(
                        latitude=float(row['latitude']),
                        longitude=float(row['longitude'])
                    ),
                    event_type=event_type,
                    actors=actors,
                    fatalities=int(row['fatalities']),
                    description=row.get('notes', '')
                )
                
                events.append(event)
                
            except Exception as e:
                self.logger.warning(
                    "Failed to transform event",
                    event_id=row.get('data_id'),
                    error=str(e)
                )
                continue
        
        self.logger.info(f"Transformed {len(events)} conflict events")
        return events
    
    async def aggregate(self, entities: List[ConflictEvent]) -> pd.DataFrame:
        """Aggregate events to market-month level."""
        # Convert to DataFrame for easier processing
        events_df = pd.DataFrame([
            {
                'event_id': e.event_id,
                'date': e.date,
                'latitude': e.location.latitude,
                'longitude': e.location.longitude,
                'event_type': e.event_type.value,
                'fatalities': e.fatalities,
                'actors': ','.join([a.value for a in e.actors])
            }
            for e in entities
        ])
        
        # Create monthly periods
        events_df['year_month'] = pd.to_datetime(events_df['date']).dt.to_period('M')
        
        # Get all market-month combinations
        all_months = pd.period_range(
            start=events_df['year_month'].min(),
            end=events_df['year_month'].max(),
            freq='M'
        )
        
        market_months = pd.DataFrame([
            {'market_id': market.id, 'year_month': month}
            for market in self.markets
            for month in all_months
        ])
        
        # Calculate metrics for each market-month
        metrics_list = []
        
        for _, row in market_months.iterrows():
            market_id = row['market_id']
            year_month = row['year_month']
            
            # Get events for this month
            month_events = events_df[events_df['year_month'] == year_month]
            
            if len(month_events) == 0:
                # No events - create zero metrics
                metrics = self._create_zero_metrics(market_id, year_month)
            else:
                # Calculate spatial metrics
                metrics = await self._calculate_market_metrics(
                    market_id,
                    year_month,
                    month_events
                )
            
            metrics_list.append(metrics)
        
        # Convert to DataFrame
        metrics_df = pd.DataFrame([
            self._metrics_to_dict(m) for m in metrics_list
        ])
        
        # Calculate temporal lags
        metrics_df = self._add_temporal_lags(metrics_df)
        
        return metrics_df
    
    async def _calculate_market_metrics(
        self,
        market_id: str,
        year_month: pd.Period,
        events_df: pd.DataFrame
    ) -> ConflictMetrics:
        """Calculate conflict metrics for a specific market-month."""
        market_coords = self.market_coords[market_id]
        
        # Calculate distances to all events
        distances = events_df.apply(
            lambda row: geodesic(
                market_coords,
                (row['latitude'], row['longitude'])
            ).kilometers,
            axis=1
        )
        
        # Count events by radius
        events_10km = (distances <= self.radius.inner_km).sum()
        events_25km = (distances <= self.radius.middle_km).sum()
        events_50km = (distances <= self.radius.outer_km).sum()
        
        # Get events within outer radius for detailed metrics
        nearby_events = events_df[distances <= self.radius.outer_km].copy()
        nearby_events['distance_km'] = distances[distances <= self.radius.outer_km]
        
        # Calculate weighted metrics
        if len(nearby_events) > 0:
            # Apply distance weights
            nearby_events['weight'] = nearby_events['distance_km'].apply(
                lambda d: float(self.radius.get_weight(d))
            )
            
            # Event type counts
            event_type_counts = nearby_events['event_type'].value_counts()
            battles = event_type_counts.get('Battles', 0)
            explosions = event_type_counts.get('Explosions/Remote violence', 0)
            violence = event_type_counts.get('Violence against civilians', 0)
            
            # Actor counts
            actor_counts = self._count_actors(nearby_events)
            
            # Weighted fatalities
            weighted_fatalities = (
                nearby_events['fatalities'] * nearby_events['weight']
            ).sum()
            
            # Intensity index
            intensity = self._calculate_intensity_index(
                len(nearby_events),
                nearby_events['fatalities'].sum(),
                weighted_fatalities
            )
            
            # Extract unique actors
            all_actors = set()
            for actors_str in nearby_events['actors']:
                for actor in actors_str.split(','):
                    actor_type = ActorType.from_acled_actor(actor.strip())
                    all_actors.add(actor_type)
            
        else:
            battles = explosions = violence = 0
            actor_counts = {}
            weighted_fatalities = 0
            intensity = Decimal('0')
            all_actors = set()
        
        return ConflictMetrics(
            market_id=market_id,
            date=year_month.to_timestamp(),
            conflict_events=len(nearby_events),
            battle_events=battles,
            explosions_events=explosions,
            violence_against_civilians=violence,
            total_fatalities=nearby_events['fatalities'].sum() if len(nearby_events) > 0 else 0,
            intensity_index=intensity,
            actors_present=list(all_actors),
            events_within_10km=events_10km,
            events_within_25km=events_25km,
            events_within_50km=events_50km,
            government_events=actor_counts.get(ActorType.GOVERNMENT, 0),
            houthi_events=actor_counts.get(ActorType.HOUTHIS, 0),
            aqap_events=actor_counts.get(ActorType.AQAP, 0),
            stc_events=actor_counts.get(ActorType.STC, 0),
            coalition_events=actor_counts.get(ActorType.COALITION, 0)
        )
    
    def _create_zero_metrics(self, market_id: str, year_month: pd.Period) -> ConflictMetrics:
        """Create metrics for market-month with no events."""
        return ConflictMetrics(
            market_id=market_id,
            date=year_month.to_timestamp(),
            conflict_events=0,
            battle_events=0,
            explosions_events=0,
            violence_against_civilians=0,
            total_fatalities=0,
            intensity_index=Decimal('0'),
            actors_present=[],
            events_within_10km=0,
            events_within_25km=0,
            events_within_50km=0
        )
    
    def _map_event_type(self, acled_type: str) -> EventType:
        """Map ACLED event type to our enum."""
        mapping = {
            'Battles': EventType.BATTLES,
            'Explosions/Remote violence': EventType.EXPLOSIONS_REMOTE_VIOLENCE,
            'Violence against civilians': EventType.VIOLENCE_AGAINST_CIVILIANS,
            'Protests': EventType.PROTESTS,
            'Riots': EventType.RIOTS,
            'Strategic developments': EventType.STRATEGIC_DEVELOPMENTS
        }
        
        return mapping.get(acled_type, EventType.STRATEGIC_DEVELOPMENTS)
    
    def _extract_actors(self, row: pd.Series) -> List[ActorType]:
        """Extract actor types from ACLED row."""
        actors = []
        
        for col in ['actor1', 'actor2']:
            if pd.notna(row.get(col)):
                actor_type = ActorType.from_acled_actor(row[col])
                if actor_type not in actors:
                    actors.append(actor_type)
        
        return actors if actors else [ActorType.UNKNOWN]
    
    def _count_actors(self, events_df: pd.DataFrame) -> Dict[ActorType, int]:
        """Count events by actor type."""
        actor_counts = {}
        
        for actors_str in events_df['actors']:
            for actor in actors_str.split(','):
                actor_type = ActorType.from_acled_actor(actor.strip())
                actor_counts[actor_type] = actor_counts.get(actor_type, 0) + 1
        
        return actor_counts
    
    def _calculate_intensity_index(
        self,
        event_count: int,
        total_fatalities: int,
        weighted_fatalities: float
    ) -> Decimal:
        """
        Calculate conflict intensity index.
        
        Formula: log(1 + events) * sqrt(1 + weighted_fatalities)
        """
        events_factor = np.log1p(event_count)
        fatality_factor = np.sqrt(1 + weighted_fatalities)
        
        return Decimal(str(events_factor * fatality_factor))
    
    def _add_temporal_lags(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add lagged conflict metrics."""
        df = df.sort_values(['market_id', 'date'])
        
        # Lag periods
        lag_cols = {
            'conflict_events': 'events',
            'total_fatalities': 'fatalities'
        }
        
        for col, suffix in lag_cols.items():
            for lag_months in [1, 3, 6]:
                lag_name = f'lag_{lag_months}m_{suffix}'
                df[lag_name] = df.groupby('market_id')[col].shift(lag_months)
        
        return df
    
    def _metrics_to_dict(self, metrics: ConflictMetrics) -> dict:
        """Convert metrics to dictionary for DataFrame."""
        return {
            'market_id': metrics.market_id,
            'date': metrics.date,
            'conflict_events': metrics.conflict_events,
            'battle_events': metrics.battle_events,
            'explosions_events': metrics.explosions_events,
            'violence_against_civilians': metrics.violence_against_civilians,
            'total_fatalities': metrics.total_fatalities,
            'intensity_index': float(metrics.intensity_index),
            'events_within_10km': metrics.events_within_10km,
            'events_within_25km': metrics.events_within_25km,
            'events_within_50km': metrics.events_within_50km,
            'government_events': metrics.government_events,
            'houthi_events': metrics.houthi_events,
            'aqap_events': metrics.aqap_events,
            'stc_events': metrics.stc_events,
            'coalition_events': metrics.coalition_events
        }