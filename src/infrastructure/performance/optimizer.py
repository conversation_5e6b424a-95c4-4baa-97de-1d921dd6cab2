"""
Pipeline optimizer with parallel processing and memory optimization strategies.
"""
import asyncio
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
from typing import Dict, List, Optional, Any, Callable, Union
from dataclasses import dataclass
from enum import Enum
import multiprocessing as mp
import pandas as pd
import numpy as np
from src.infrastructure.logging import Logger
from pathlib import Path
import dask.dataframe as dd
import gc

logger = Logger(__name__)


class OptimizationStrategy(Enum):
    """Available optimization strategies."""
    PARALLEL_PROCESSORS = "parallel_processors"
    DATA_PARTITIONING = "data_partitioning"
    MEMORY_CHUNKING = "memory_chunking"
    CACHING = "caching"
    STREAMING = "streaming"
    BATCH_PROCESSING = "batch_processing"


@dataclass
class OptimizationConfig:
    """Configuration for optimization strategies."""
    max_workers: int = mp.cpu_count()
    chunk_size: int = 10000
    max_memory_mb: int = 4000
    enable_caching: bool = True
    partition_by: Optional[str] = None  # e.g., 'governorate'
    batch_size: int = 1000
    use_dask: bool = False


class PipelineOptimizer:
    """
    Optimize pipeline performance through various strategies.
    
    Features:
    - Parallel processor execution
    - Data partitioning by geographic regions
    - Memory-efficient chunking
    - Streaming for large datasets
    - Batch processing
    - Dask integration for out-of-core computation
    """
    
    def __init__(self, config: Optional[OptimizationConfig] = None):
        self.config = config or OptimizationConfig()
        self.logger = logger.bind(component="PipelineOptimizer")
        self._thread_pool = None
        self._process_pool = None
        
    async def optimize_processor(
        self,
        processor_func: Callable,
        data: Union[pd.DataFrame, List[Any]],
        strategy: OptimizationStrategy,
        **kwargs
    ) -> Any:
        """
        Apply optimization strategy to processor.
        
        Args:
            processor_func: Function to optimize
            data: Input data
            strategy: Optimization strategy to apply
            **kwargs: Additional arguments for processor
            
        Returns:
            Optimized results
        """
        self.logger.info(
            "applying_optimization",
            strategy=strategy.value,
            data_size=len(data) if hasattr(data, '__len__') else 'unknown'
        )
        
        if strategy == OptimizationStrategy.PARALLEL_PROCESSORS:
            return await self._parallel_process(processor_func, data, **kwargs)
        elif strategy == OptimizationStrategy.DATA_PARTITIONING:
            return await self._partition_process(processor_func, data, **kwargs)
        elif strategy == OptimizationStrategy.MEMORY_CHUNKING:
            return await self._chunk_process(processor_func, data, **kwargs)
        elif strategy == OptimizationStrategy.STREAMING:
            return await self._stream_process(processor_func, data, **kwargs)
        elif strategy == OptimizationStrategy.BATCH_PROCESSING:
            return await self._batch_process(processor_func, data, **kwargs)
        else:
            # Default: no optimization
            return await processor_func(data, **kwargs)
    
    async def _parallel_process(
        self,
        processor_func: Callable,
        data: pd.DataFrame,
        **kwargs
    ) -> pd.DataFrame:
        """Execute processor in parallel across data partitions."""
        if not isinstance(data, pd.DataFrame):
            # Can't parallelize non-DataFrame data
            return await processor_func(data, **kwargs)
        
        # Determine partition strategy
        if self.config.partition_by and self.config.partition_by in data.columns:
            # Partition by specified column
            groups = data.groupby(self.config.partition_by)
            partitions = [group for _, group in groups]
        else:
            # Partition by rows
            n_partitions = min(self.config.max_workers, max(1, len(data) // self.config.chunk_size))
            partitions = np.array_split(data, n_partitions)
        
        self.logger.info(
            "parallel_processing",
            n_partitions=len(partitions),
            max_workers=self.config.max_workers
        )
        
        # Process partitions in parallel
        tasks = []
        for partition in partitions:
            task = asyncio.create_task(processor_func(partition, **kwargs))
            tasks.append(task)
        
        # Wait for all tasks with concurrency limit
        results = []
        for i in range(0, len(tasks), self.config.max_workers):
            batch = tasks[i:i + self.config.max_workers]
            batch_results = await asyncio.gather(*batch)
            results.extend(batch_results)
        
        # Combine results
        if all(isinstance(r, pd.DataFrame) for r in results):
            return pd.concat(results, ignore_index=True)
        else:
            return results
    
    async def _partition_process(
        self,
        processor_func: Callable,
        data: pd.DataFrame,
        **kwargs
    ) -> pd.DataFrame:
        """Process data by geographic partitions."""
        if not isinstance(data, pd.DataFrame):
            return await processor_func(data, **kwargs)
        
        # Partition by governorate for Yemen data
        if 'governorate' in data.columns:
            partition_col = 'governorate'
        elif 'admin1' in data.columns:
            partition_col = 'admin1'
        else:
            # Fall back to chunk processing
            return await self._chunk_process(processor_func, data, **kwargs)
        
        # Group by partition
        groups = data.groupby(partition_col)
        results = []
        
        for name, group in groups:
            self.logger.info(
                "processing_partition",
                partition=name,
                size=len(group)
            )
            
            # Process partition
            result = await processor_func(group, **kwargs)
            
            # Add partition identifier back
            if isinstance(result, pd.DataFrame):
                result[partition_col] = name
            
            results.append(result)
            
            # Memory cleanup
            if len(group) > self.config.chunk_size:
                gc.collect()
        
        # Combine results
        if all(isinstance(r, pd.DataFrame) for r in results):
            return pd.concat(results, ignore_index=True)
        else:
            return results
    
    async def _chunk_process(
        self,
        processor_func: Callable,
        data: pd.DataFrame,
        **kwargs
    ) -> pd.DataFrame:
        """Process data in memory-efficient chunks."""
        if not isinstance(data, pd.DataFrame):
            return await processor_func(data, **kwargs)
        
        chunk_size = self.config.chunk_size
        n_chunks = (len(data) + chunk_size - 1) // chunk_size
        
        self.logger.info(
            "chunk_processing",
            total_rows=len(data),
            chunk_size=chunk_size,
            n_chunks=n_chunks
        )
        
        results = []
        
        for i in range(0, len(data), chunk_size):
            chunk = data.iloc[i:i + chunk_size]
            
            # Check memory usage
            memory_mb = chunk.memory_usage(deep=True).sum() / 1024 / 1024
            if memory_mb > self.config.max_memory_mb:
                self.logger.warning(
                    "chunk_memory_high",
                    chunk_index=i // chunk_size,
                    memory_mb=memory_mb
                )
            
            # Process chunk
            result = await processor_func(chunk, **kwargs)
            results.append(result)
            
            # Force garbage collection for large chunks
            if memory_mb > self.config.max_memory_mb / 2:
                gc.collect()
        
        # Combine results
        if all(isinstance(r, pd.DataFrame) for r in results):
            return pd.concat(results, ignore_index=True)
        else:
            return results
    
    async def _stream_process(
        self,
        processor_func: Callable,
        data: Union[pd.DataFrame, Path],
        **kwargs
    ) -> Any:
        """Process data in streaming fashion."""
        if isinstance(data, Path) and data.suffix == '.csv':
            # Stream from CSV file
            results = []
            
            async def process_chunk(chunk):
                return await processor_func(chunk, **kwargs)
            
            # Read CSV in chunks
            chunk_iter = pd.read_csv(
                data,
                chunksize=self.config.chunk_size,
                low_memory=False
            )
            
            for chunk in chunk_iter:
                result = await process_chunk(chunk)
                results.append(result)
            
            # Combine results
            if all(isinstance(r, pd.DataFrame) for r in results):
                return pd.concat(results, ignore_index=True)
            else:
                return results
        else:
            # Not streamable, process normally
            return await processor_func(data, **kwargs)
    
    async def _batch_process(
        self,
        processor_func: Callable,
        data: List[Any],
        **kwargs
    ) -> List[Any]:
        """Process data in batches."""
        if not isinstance(data, list):
            return await processor_func(data, **kwargs)
        
        batch_size = self.config.batch_size
        n_batches = (len(data) + batch_size - 1) // batch_size
        
        self.logger.info(
            "batch_processing",
            total_items=len(data),
            batch_size=batch_size,
            n_batches=n_batches
        )
        
        results = []
        
        for i in range(0, len(data), batch_size):
            batch = data[i:i + batch_size]
            result = await processor_func(batch, **kwargs)
            
            if isinstance(result, list):
                results.extend(result)
            else:
                results.append(result)
        
        return results
    
    def optimize_with_dask(
        self,
        data: pd.DataFrame,
        processor_func: Callable,
        **kwargs
    ) -> pd.DataFrame:
        """
        Use Dask for out-of-core computation.
        
        Note: This is synchronous as Dask handles its own parallelism.
        """
        if not self.config.use_dask:
            raise ValueError("Dask optimization not enabled in config")
        
        # Convert to Dask DataFrame
        if 'governorate' in data.columns:
            # Partition by governorate for better performance
            ddf = dd.from_pandas(data, npartitions=data['governorate'].nunique())
            ddf = ddf.set_index('governorate')
        else:
            # Partition by size
            n_partitions = max(1, len(data) // self.config.chunk_size)
            ddf = dd.from_pandas(data, npartitions=n_partitions)
        
        self.logger.info(
            "dask_processing",
            n_partitions=ddf.npartitions,
            columns=list(ddf.columns)
        )
        
        # Apply processor function to each partition
        # Note: processor_func must be a regular function, not async
        result_ddf = ddf.map_partitions(processor_func, **kwargs)
        
        # Compute result
        result = result_ddf.compute()
        
        return result
    
    async def parallel_pipeline(
        self,
        processors: List[Dict[str, Any]],
        initial_data: Any
    ) -> Dict[str, Any]:
        """
        Run multiple processors in parallel where possible.
        
        Args:
            processors: List of processor configs with dependencies
            initial_data: Initial input data
            
        Returns:
            Results from all processors
        """
        # Build dependency graph
        results = {}
        pending = set(range(len(processors)))
        
        while pending:
            # Find processors with satisfied dependencies
            ready = []
            for i in pending:
                proc = processors[i]
                deps = proc.get('depends_on', [])
                if all(dep in results for dep in deps):
                    ready.append(i)
            
            if not ready:
                raise ValueError("Circular dependency in processors")
            
            # Run ready processors in parallel
            tasks = []
            for i in ready:
                proc = processors[i]
                
                # Get input data
                if proc.get('depends_on'):
                    # Use output from dependencies
                    input_data = results[proc['depends_on'][0]]
                else:
                    input_data = initial_data
                
                # Create task
                task = asyncio.create_task(
                    proc['function'](input_data, **proc.get('kwargs', {}))
                )
                tasks.append((proc['name'], task))
                pending.remove(i)
            
            # Wait for tasks to complete
            for name, task in tasks:
                results[name] = await task
        
        return results
    
    def get_optimization_recommendations(
        self,
        data_size: int,
        data_type: str,
        memory_available_mb: int
    ) -> List[OptimizationStrategy]:
        """Get recommended optimization strategies based on data characteristics."""
        recommendations = []
        
        # Large datasets benefit from chunking
        if data_size > 100000:
            recommendations.append(OptimizationStrategy.MEMORY_CHUNKING)
        
        # Geographic data benefits from partitioning
        if data_type in ['spatial', 'panel', 'geographic']:
            recommendations.append(OptimizationStrategy.DATA_PARTITIONING)
        
        # Very large datasets need streaming
        if data_size > 1000000:
            recommendations.append(OptimizationStrategy.STREAMING)
        
        # Multiple processors benefit from parallelism
        if self.config.max_workers > 2:
            recommendations.append(OptimizationStrategy.PARALLEL_PROCESSORS)
        
        # Limited memory requires careful processing
        if memory_available_mb < 4000:
            recommendations.append(OptimizationStrategy.BATCH_PROCESSING)
        
        return recommendations
    
    def cleanup(self):
        """Clean up resources."""
        if self._thread_pool:
            self._thread_pool.shutdown(wait=True)
        if self._process_pool:
            self._process_pool.shutdown(wait=True)
        gc.collect()