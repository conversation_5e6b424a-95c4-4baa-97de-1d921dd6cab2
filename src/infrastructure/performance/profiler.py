"""
Performance profiler for data pipeline processors.

Tracks execution time, memory usage, and identifies bottlenecks.
"""
import asyncio
import time
import psutil
import tracemalloc
from datetime import datetime
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
import pandas as pd
from src.infrastructure.logging import Logger
from pathlib import Path
import json

logger = Logger(__name__)


@dataclass
class ProcessorProfile:
    """Performance profile for a single processor."""
    processor_name: str
    start_time: datetime
    end_time: Optional[datetime] = None
    duration_seconds: float = 0.0
    memory_start_mb: float = 0.0
    memory_peak_mb: float = 0.0
    memory_end_mb: float = 0.0
    cpu_percent_avg: float = 0.0
    records_processed: int = 0
    records_per_second: float = 0.0
    io_read_mb: float = 0.0
    io_write_mb: float = 0.0
    bottlenecks: List[str] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)
    detailed_metrics: Dict[str, Any] = field(default_factory=dict)


class PipelineProfiler:
    """
    Profile pipeline performance and identify bottlenecks.
    
    Features:
    - Execution time tracking
    - Memory usage profiling
    - CPU utilization monitoring
    - I/O performance tracking
    - Bottleneck identification
    - Performance recommendations
    """
    
    def __init__(self, output_dir: Optional[Path] = None):
        self.output_dir = output_dir or Path("profiling_results")
        self.output_dir.mkdir(exist_ok=True)
        self.profiles: Dict[str, ProcessorProfile] = {}
        self.process = psutil.Process()
        self.logger = logger.bind(component="PipelineProfiler")
        
    async def profile_processor(
        self,
        processor_name: str,
        processor_func: Callable,
        *args,
        **kwargs
    ) -> Any:
        """
        Profile a processor execution.
        
        Args:
            processor_name: Name of the processor
            processor_func: Async function to profile
            *args, **kwargs: Arguments for the processor
            
        Returns:
            Result from the processor function
        """
        # Start profiling
        profile = ProcessorProfile(
            processor_name=processor_name,
            start_time=datetime.now()
        )
        
        # Memory tracking
        tracemalloc.start()
        memory_start = self.process.memory_info().rss / 1024 / 1024  # MB
        profile.memory_start_mb = memory_start
        
        # I/O tracking
        io_start = self.process.io_counters()
        
        # CPU tracking
        cpu_samples = []
        cpu_task = asyncio.create_task(self._monitor_cpu(cpu_samples))
        
        try:
            # Execute processor
            start_time = time.time()
            result = await processor_func(*args, **kwargs)
            end_time = time.time()
            
            # Stop CPU monitoring
            cpu_task.cancel()
            try:
                await cpu_task
            except asyncio.CancelledError:
                pass
            
            # Calculate metrics
            profile.end_time = datetime.now()
            profile.duration_seconds = end_time - start_time
            
            # Memory metrics
            current, peak = tracemalloc.get_traced_memory()
            profile.memory_peak_mb = peak / 1024 / 1024
            profile.memory_end_mb = self.process.memory_info().rss / 1024 / 1024
            tracemalloc.stop()
            
            # CPU metrics
            if cpu_samples:
                profile.cpu_percent_avg = sum(cpu_samples) / len(cpu_samples)
            
            # I/O metrics
            io_end = self.process.io_counters()
            profile.io_read_mb = (io_end.read_bytes - io_start.read_bytes) / 1024 / 1024
            profile.io_write_mb = (io_end.write_bytes - io_start.write_bytes) / 1024 / 1024
            
            # Extract record count if available
            if hasattr(result, '__len__'):
                profile.records_processed = len(result)
            elif isinstance(result, tuple) and hasattr(result[0], '__len__'):
                profile.records_processed = len(result[0])
            
            if profile.records_processed > 0 and profile.duration_seconds > 0:
                profile.records_per_second = profile.records_processed / profile.duration_seconds
            
            # Analyze for bottlenecks
            self._identify_bottlenecks(profile)
            
            # Store profile
            self.profiles[processor_name] = profile
            
            self.logger.info(
                "processor_profiled",
                processor=processor_name,
                duration=profile.duration_seconds,
                memory_peak_mb=profile.memory_peak_mb,
                records_per_second=profile.records_per_second
            )
            
            return result
            
        except Exception as e:
            self.logger.error(
                "profiling_failed",
                processor=processor_name,
                error=str(e)
            )
            raise
    
    async def _monitor_cpu(self, samples: List[float]):
        """Monitor CPU usage during execution."""
        while True:
            try:
                cpu_percent = self.process.cpu_percent(interval=0.1)
                samples.append(cpu_percent)
                await asyncio.sleep(0.1)
            except asyncio.CancelledError:
                break
    
    def _identify_bottlenecks(self, profile: ProcessorProfile):
        """Identify performance bottlenecks and generate recommendations."""
        bottlenecks = []
        recommendations = []
        
        # Memory bottlenecks
        memory_increase = profile.memory_peak_mb - profile.memory_start_mb
        if memory_increase > 1000:  # > 1GB increase
            bottlenecks.append(f"High memory usage: {memory_increase:.0f}MB increase")
            recommendations.append("Consider chunking data processing")
        
        if profile.memory_peak_mb > 4000:  # > 4GB peak
            bottlenecks.append(f"Very high peak memory: {profile.memory_peak_mb:.0f}MB")
            recommendations.append("Implement streaming processing")
        
        # CPU bottlenecks
        if profile.cpu_percent_avg > 80:
            bottlenecks.append(f"High CPU usage: {profile.cpu_percent_avg:.1f}%")
            recommendations.append("Consider parallel processing")
        elif profile.cpu_percent_avg < 20 and profile.duration_seconds > 10:
            bottlenecks.append(f"Low CPU utilization: {profile.cpu_percent_avg:.1f}%")
            recommendations.append("Likely I/O bound - optimize data access")
        
        # I/O bottlenecks
        io_total = profile.io_read_mb + profile.io_write_mb
        if io_total > 0 and profile.duration_seconds > 0:
            io_rate = io_total / profile.duration_seconds
            if io_rate < 10:  # < 10 MB/s
                bottlenecks.append(f"Slow I/O: {io_rate:.1f} MB/s")
                recommendations.append("Consider caching or faster storage")
        
        # Processing speed
        if profile.records_per_second > 0 and profile.records_per_second < 1000:
            bottlenecks.append(f"Low throughput: {profile.records_per_second:.0f} records/sec")
            recommendations.append("Profile record processing logic")
        
        # Duration
        if profile.duration_seconds > 300:  # > 5 minutes
            bottlenecks.append(f"Long execution time: {profile.duration_seconds:.0f} seconds")
            recommendations.append("Consider breaking into smaller tasks")
        
        profile.bottlenecks = bottlenecks
        profile.recommendations = recommendations
    
    def get_summary_report(self) -> pd.DataFrame:
        """Generate summary report of all profiles."""
        data = []
        for name, profile in self.profiles.items():
            data.append({
                'processor': name,
                'duration_seconds': profile.duration_seconds,
                'memory_peak_mb': profile.memory_peak_mb,
                'memory_increase_mb': profile.memory_peak_mb - profile.memory_start_mb,
                'cpu_percent_avg': profile.cpu_percent_avg,
                'records_processed': profile.records_processed,
                'records_per_second': profile.records_per_second,
                'io_read_mb': profile.io_read_mb,
                'io_write_mb': profile.io_write_mb,
                'bottleneck_count': len(profile.bottlenecks)
            })
        
        return pd.DataFrame(data)
    
    def identify_critical_path(self) -> List[str]:
        """Identify the critical path (slowest processors)."""
        # Sort by duration
        sorted_profiles = sorted(
            self.profiles.items(),
            key=lambda x: x[1].duration_seconds,
            reverse=True
        )
        
        # Top 20% are critical path
        critical_count = max(1, len(sorted_profiles) // 5)
        return [name for name, _ in sorted_profiles[:critical_count]]
    
    def generate_optimization_plan(self) -> Dict[str, List[str]]:
        """Generate optimization plan based on profiling results."""
        plan = {}
        
        for name, profile in self.profiles.items():
            if profile.recommendations:
                plan[name] = profile.recommendations
        
        # Add general recommendations
        total_duration = sum(p.duration_seconds for p in self.profiles.values())
        if total_duration > 1800:  # > 30 minutes
            if 'general' not in plan:
                plan['general'] = []
            plan['general'].append("Total pipeline time exceeds 30 minutes - consider parallel execution")
        
        total_memory = max(p.memory_peak_mb for p in self.profiles.values())
        if total_memory > 8000:  # > 8GB
            if 'general' not in plan:
                plan['general'] = []
            plan['general'].append("Peak memory usage exceeds 8GB - implement memory-efficient processing")
        
        return plan
    
    def save_results(self, filename: Optional[str] = None):
        """Save profiling results to file."""
        if filename is None:
            filename = f"profile_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        filepath = self.output_dir / filename
        
        results = {
            'timestamp': datetime.now().isoformat(),
            'profiles': {},
            'summary': self.get_summary_report().to_dict('records'),
            'critical_path': self.identify_critical_path(),
            'optimization_plan': self.generate_optimization_plan()
        }
        
        # Convert profiles to dict
        for name, profile in self.profiles.items():
            results['profiles'][name] = {
                'duration_seconds': profile.duration_seconds,
                'memory_start_mb': profile.memory_start_mb,
                'memory_peak_mb': profile.memory_peak_mb,
                'memory_end_mb': profile.memory_end_mb,
                'cpu_percent_avg': profile.cpu_percent_avg,
                'records_processed': profile.records_processed,
                'records_per_second': profile.records_per_second,
                'io_read_mb': profile.io_read_mb,
                'io_write_mb': profile.io_write_mb,
                'bottlenecks': profile.bottlenecks,
                'recommendations': profile.recommendations
            }
        
        with open(filepath, 'w') as f:
            json.dump(results, f, indent=2)
        
        self.logger.info("profiling_results_saved", filepath=str(filepath))
        
        return filepath
    
    def print_summary(self):
        """Print summary to console."""
        from rich.console import Console
        from rich.table import Table
        from rich.panel import Panel
        
        console = Console()
        
        # Summary table
        table = Table(title="Pipeline Performance Profile")
        table.add_column("Processor", style="cyan")
        table.add_column("Duration (s)", justify="right")
        table.add_column("Memory Peak (MB)", justify="right")
        table.add_column("CPU Avg %", justify="right")
        table.add_column("Records/sec", justify="right")
        table.add_column("Bottlenecks", justify="center")
        
        for name, profile in self.profiles.items():
            table.add_row(
                name,
                f"{profile.duration_seconds:.1f}",
                f"{profile.memory_peak_mb:.0f}",
                f"{profile.cpu_percent_avg:.1f}",
                f"{profile.records_per_second:.0f}",
                "⚠️" if profile.bottlenecks else "✅"
            )
        
        console.print(table)
        
        # Critical path
        critical_path = self.identify_critical_path()
        if critical_path:
            console.print(Panel(
                "\n".join(critical_path),
                title="Critical Path (Slowest Processors)",
                border_style="red"
            ))
        
        # Optimization plan
        plan = self.generate_optimization_plan()
        if plan:
            recommendations = []
            for processor, recs in plan.items():
                recommendations.append(f"\n[bold]{processor}:[/bold]")
                for rec in recs:
                    recommendations.append(f"  • {rec}")
            
            console.print(Panel(
                "\n".join(recommendations),
                title="Optimization Recommendations",
                border_style="yellow"
            ))