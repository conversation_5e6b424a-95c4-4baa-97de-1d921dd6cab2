"""Dead letter queue implementation for failed records."""

import asyncio
import json
import pickle
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional, Callable, TypeVar
from enum import Enum
import logging
from asyncio import Queue, Lock
import aiofiles

logger = logging.getLogger(__name__)

T = TypeVar('T')


class RecordStatus(Enum):
    """Status of a DLQ record."""
    PENDING = "pending"
    RETRYING = "retrying"
    FAILED = "failed"
    EXPIRED = "expired"
    PROCESSED = "processed"


@dataclass
class DLQRecord:
    """Dead letter queue record."""
    id: str
    source: str  # Which processor/service failed
    record_type: str  # Type of data
    data: Any  # The actual failed data
    error: str  # Error message
    error_type: str  # Exception type
    timestamp: datetime
    retry_count: int = 0
    max_retries: int = 3
    status: RecordStatus = RecordStatus.PENDING
    last_retry: Optional[datetime] = None
    ttl_hours: int = 168  # 7 days default
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
            
    @property
    def is_expired(self) -> bool:
        """Check if record has expired."""
        expiry = self.timestamp + timedelta(hours=self.ttl_hours)
        return datetime.now() > expiry
        
    @property
    def can_retry(self) -> bool:
        """Check if record can be retried."""
        return (
            self.status == RecordStatus.PENDING and
            self.retry_count < self.max_retries and
            not self.is_expired
        )
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'id': self.id,
            'source': self.source,
            'record_type': self.record_type,
            'data': self.data,
            'error': self.error,
            'error_type': self.error_type,
            'timestamp': self.timestamp.isoformat(),
            'retry_count': self.retry_count,
            'max_retries': self.max_retries,
            'status': self.status.value,
            'last_retry': self.last_retry.isoformat() if self.last_retry else None,
            'ttl_hours': self.ttl_hours,
            'metadata': self.metadata
        }
        
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DLQRecord':
        """Create from dictionary."""
        data['timestamp'] = datetime.fromisoformat(data['timestamp'])
        if data.get('last_retry'):
            data['last_retry'] = datetime.fromisoformat(data['last_retry'])
        data['status'] = RecordStatus(data['status'])
        return cls(**data)


class DeadLetterQueue:
    """
    Dead letter queue for handling failed records.
    
    Features:
    - Persistent storage of failed records
    - Automatic retry with configurable limits
    - TTL-based expiration
    - Monitoring and statistics
    - Different retry strategies per source
    """
    
    def __init__(
        self,
        storage_path: Path,
        max_queue_size: int = 10000,
        cleanup_interval: int = 3600  # 1 hour
    ):
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(parents=True, exist_ok=True)
        self.max_queue_size = max_queue_size
        self.cleanup_interval = cleanup_interval
        
        self._queue: Queue[DLQRecord] = Queue(maxsize=max_queue_size)
        self._lock = Lock()
        self._retry_handlers: Dict[str, Callable] = {}
        self._stats: Dict[str, int] = {
            'total_enqueued': 0,
            'total_retried': 0,
            'total_succeeded': 0,
            'total_failed': 0,
            'total_expired': 0
        }
        self._running = False
        self._tasks: List[asyncio.Task] = []
        
    async def start(self) -> None:
        """Start the DLQ processing."""
        self._running = True
        
        # Load existing records from storage
        await self._load_from_storage()
        
        # Start background tasks
        self._tasks.append(
            asyncio.create_task(self._retry_loop())
        )
        self._tasks.append(
            asyncio.create_task(self._cleanup_loop())
        )
        
        logger.info(f"Dead letter queue started with {self._queue.qsize()} records")
        
    async def stop(self) -> None:
        """Stop the DLQ processing."""
        self._running = False
        
        # Cancel tasks
        for task in self._tasks:
            task.cancel()
            
        # Wait for tasks to complete
        await asyncio.gather(*self._tasks, return_exceptions=True)
        
        # Save remaining records
        await self._save_to_storage()
        
        logger.info("Dead letter queue stopped")
        
    async def enqueue(
        self,
        source: str,
        record_type: str,
        data: Any,
        error: Exception,
        metadata: Optional[Dict[str, Any]] = None,
        ttl_hours: Optional[int] = None
    ) -> str:
        """
        Add a failed record to the dead letter queue.
        
        Args:
            source: Source processor/service that failed
            record_type: Type of data record
            data: The failed data
            error: The exception that caused failure
            metadata: Additional metadata
            ttl_hours: Time to live in hours
            
        Returns:
            Record ID
        """
        async with self._lock:
            # Check queue size
            if self._queue.qsize() >= self.max_queue_size:
                # Remove oldest expired records to make room
                await self._cleanup_expired()
                
                if self._queue.qsize() >= self.max_queue_size:
                    raise RuntimeError(
                        f"Dead letter queue full ({self.max_queue_size} records)"
                    )
                    
            # Create record
            record_id = f"{source}_{record_type}_{datetime.now().timestamp()}"
            record = DLQRecord(
                id=record_id,
                source=source,
                record_type=record_type,
                data=data,
                error=str(error),
                error_type=type(error).__name__,
                timestamp=datetime.now(),
                metadata=metadata or {},
                ttl_hours=ttl_hours or 168  # 7 days default
            )
            
            # Add to queue
            await self._queue.put(record)
            self._stats['total_enqueued'] += 1
            
            # Persist to storage
            await self._save_record(record)
            
            logger.warning(
                f"Added record to DLQ: {record_id} from {source} "
                f"({type(error).__name__}: {str(error)})"
            )
            
            return record_id
            
    def register_retry_handler(
        self,
        source: str,
        handler: Callable[[Any], T]
    ) -> None:
        """
        Register a retry handler for a specific source.
        
        Args:
            source: Source name
            handler: Async function to handle retry
        """
        self._retry_handlers[source] = handler
        logger.info(f"Registered retry handler for source: {source}")
        
    async def _retry_loop(self) -> None:
        """Background loop for retrying records."""
        while self._running:
            try:
                # Get records ready for retry
                records_to_retry = await self._get_retry_candidates()
                
                for record in records_to_retry:
                    if not self._running:
                        break
                        
                    await self._retry_record(record)
                    
                # Wait before next retry cycle
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                logger.error(f"Error in retry loop: {e}", exc_info=True)
                await asyncio.sleep(60)
                
    async def _retry_record(self, record: DLQRecord) -> bool:
        """
        Retry a single record.
        
        Returns:
            True if successful, False otherwise
        """
        # Check if handler registered
        if record.source not in self._retry_handlers:
            logger.warning(
                f"No retry handler registered for source: {record.source}"
            )
            return False
            
        handler = self._retry_handlers[record.source]
        
        try:
            # Update record status
            record.status = RecordStatus.RETRYING
            record.retry_count += 1
            record.last_retry = datetime.now()
            
            # Attempt retry
            logger.info(
                f"Retrying DLQ record {record.id} "
                f"(attempt {record.retry_count}/{record.max_retries})"
            )
            
            await handler(record.data)
            
            # Success
            record.status = RecordStatus.PROCESSED
            self._stats['total_succeeded'] += 1
            
            logger.info(f"Successfully processed DLQ record: {record.id}")
            
            # Remove from storage
            await self._remove_record(record)
            
            return True
            
        except Exception as e:
            logger.error(
                f"Retry failed for record {record.id}: "
                f"{type(e).__name__}: {str(e)}"
            )
            
            # Check if we've exhausted retries
            if record.retry_count >= record.max_retries:
                record.status = RecordStatus.FAILED
                self._stats['total_failed'] += 1
                logger.error(
                    f"Record {record.id} permanently failed after "
                    f"{record.retry_count} attempts"
                )
            else:
                # Reset to pending for next retry
                record.status = RecordStatus.PENDING
                
            self._stats['total_retried'] += 1
            
            # Update storage
            await self._save_record(record)
            
            return False
            
    async def _cleanup_loop(self) -> None:
        """Background loop for cleaning up expired records."""
        while self._running:
            try:
                await asyncio.sleep(self.cleanup_interval)
                await self._cleanup_expired()
                
            except Exception as e:
                logger.error(f"Error in cleanup loop: {e}", exc_info=True)
                
    async def _cleanup_expired(self) -> int:
        """Remove expired records."""
        async with self._lock:
            removed = 0
            
            # Get all records from queue
            all_records = []
            while not self._queue.empty():
                try:
                    record = self._queue.get_nowait()
                    if not record.is_expired:
                        all_records.append(record)
                    else:
                        removed += 1
                        self._stats['total_expired'] += 1
                        await self._remove_record(record)
                except asyncio.QueueEmpty:
                    break
                    
            # Put non-expired records back
            for record in all_records:
                await self._queue.put(record)
                
            if removed > 0:
                logger.info(f"Cleaned up {removed} expired DLQ records")
                
            return removed
            
    async def _get_retry_candidates(self) -> List[DLQRecord]:
        """Get records ready for retry."""
        candidates = []
        
        async with self._lock:
            # Get all records
            all_records = []
            while not self._queue.empty():
                try:
                    record = self._queue.get_nowait()
                    all_records.append(record)
                except asyncio.QueueEmpty:
                    break
                    
            # Find candidates and put records back
            for record in all_records:
                if record.can_retry:
                    candidates.append(record)
                await self._queue.put(record)
                
        return candidates
        
    async def _save_record(self, record: DLQRecord) -> None:
        """Save record to persistent storage."""
        file_path = self.storage_path / f"{record.id}.json"
        
        async with aiofiles.open(file_path, 'w') as f:
            await f.write(json.dumps(record.to_dict(), default=str))
            
    async def _remove_record(self, record: DLQRecord) -> None:
        """Remove record from persistent storage."""
        file_path = self.storage_path / f"{record.id}.json"
        if file_path.exists():
            file_path.unlink()
            
    async def _load_from_storage(self) -> None:
        """Load records from persistent storage."""
        for file_path in self.storage_path.glob("*.json"):
            try:
                async with aiofiles.open(file_path, 'r') as f:
                    data = json.loads(await f.read())
                    record = DLQRecord.from_dict(data)
                    
                    if not record.is_expired:
                        await self._queue.put(record)
                    else:
                        file_path.unlink()
                        
            except Exception as e:
                logger.error(f"Error loading DLQ record from {file_path}: {e}")
                
    async def _save_to_storage(self) -> None:
        """Save all queue records to storage."""
        while not self._queue.empty():
            try:
                record = self._queue.get_nowait()
                await self._save_record(record)
            except asyncio.QueueEmpty:
                break
                
    def get_stats(self) -> Dict[str, Any]:
        """Get DLQ statistics."""
        return {
            'queue_size': self._queue.qsize(),
            'max_size': self.max_queue_size,
            'stats': self._stats.copy(),
            'retry_handlers': list(self._retry_handlers.keys())
        }
        
    async def get_records(
        self,
        source: Optional[str] = None,
        status: Optional[RecordStatus] = None,
        limit: int = 100
    ) -> List[DLQRecord]:
        """Get records from the queue (for monitoring)."""
        records = []
        
        async with self._lock:
            # Get all records
            all_records = []
            while not self._queue.empty():
                try:
                    record = self._queue.get_nowait()
                    all_records.append(record)
                except asyncio.QueueEmpty:
                    break
                    
            # Filter and collect
            for record in all_records:
                if (
                    (source is None or record.source == source) and
                    (status is None or record.status == status) and
                    len(records) < limit
                ):
                    records.append(record)
                    
            # Put all records back
            for record in all_records:
                await self._queue.put(record)
                
        return records