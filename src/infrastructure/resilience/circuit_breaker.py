"""Circuit breaker pattern implementation for external service calls."""

import asyncio
import time
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Callable, Dict, Optional, TypeVar, Generic
import logging
from asyncio import Lock

logger = logging.getLogger(__name__)

T = TypeVar('T')


class CircuitState(Enum):
    """Circuit breaker states."""
    CLOSED = "closed"  # Normal operation
    OPEN = "open"      # Failures exceeded threshold
    HALF_OPEN = "half_open"  # Testing recovery


@dataclass
class CircuitBreakerConfig:
    """Configuration for circuit breaker."""
    failure_threshold: int = 5
    recovery_timeout: int = 60  # seconds
    expected_exception: type = Exception
    success_threshold: int = 2  # Successes needed to close from half-open
    time_window: int = 60  # Window for counting failures (seconds)


@dataclass
class CircuitBreakerStats:
    """Statistics for circuit breaker monitoring."""
    total_calls: int = 0
    successful_calls: int = 0
    failed_calls: int = 0
    last_failure_time: Optional[datetime] = None
    last_success_time: Optional[datetime] = None
    consecutive_failures: int = 0
    consecutive_successes: int = 0
    state_changes: list = field(default_factory=list)
    failure_reasons: Dict[str, int] = field(default_factory=dict)


class CircuitBreaker(Generic[T]):
    """
    Circuit breaker implementation for protecting external service calls.
    
    Example:
        ```python
        hdx_breaker = CircuitBreaker[Dict](
            name="hdx_api",
            config=CircuitBreakerConfig(
                failure_threshold=3,
                recovery_timeout=30
            )
        )
        
        @hdx_breaker
        async def fetch_hdx_data():
            return await hdx_client.fetch()
        ```
    """
    
    def __init__(
        self,
        name: str,
        config: Optional[CircuitBreakerConfig] = None
    ):
        self.name = name
        self.config = config or CircuitBreakerConfig()
        self._state = CircuitState.CLOSED
        self._failure_count = 0
        self._last_failure_time: Optional[float] = None
        self._lock = Lock()
        self.stats = CircuitBreakerStats()
        self._failure_timestamps: list[float] = []
        
    @property
    def state(self) -> CircuitState:
        """Get current circuit state."""
        return self._state
        
    @property
    def is_closed(self) -> bool:
        """Check if circuit is closed (normal operation)."""
        return self._state == CircuitState.CLOSED
        
    @property
    def is_open(self) -> bool:
        """Check if circuit is open (blocking calls)."""
        return self._state == CircuitState.OPEN
        
    def _clean_old_failures(self) -> None:
        """Remove failures outside the time window."""
        if not self._failure_timestamps:
            return
            
        cutoff_time = time.time() - self.config.time_window
        self._failure_timestamps = [
            ts for ts in self._failure_timestamps if ts > cutoff_time
        ]
        self._failure_count = len(self._failure_timestamps)
        
    async def _record_success(self) -> None:
        """Record successful call."""
        async with self._lock:
            self.stats.total_calls += 1
            self.stats.successful_calls += 1
            self.stats.last_success_time = datetime.now()
            self.stats.consecutive_successes += 1
            self.stats.consecutive_failures = 0
            
            if self._state == CircuitState.HALF_OPEN:
                if self.stats.consecutive_successes >= self.config.success_threshold:
                    await self._transition_to_closed()
                    
    async def _record_failure(self, error: Exception) -> None:
        """Record failed call."""
        async with self._lock:
            self.stats.total_calls += 1
            self.stats.failed_calls += 1
            self.stats.last_failure_time = datetime.now()
            self.stats.consecutive_failures += 1
            self.stats.consecutive_successes = 0
            
            # Track failure reason
            error_type = type(error).__name__
            self.stats.failure_reasons[error_type] = \
                self.stats.failure_reasons.get(error_type, 0) + 1
            
            # Add to failure timestamps
            current_time = time.time()
            self._failure_timestamps.append(current_time)
            self._last_failure_time = current_time
            
            # Clean old failures
            self._clean_old_failures()
            
            # Check if we should open the circuit
            if self._state == CircuitState.CLOSED:
                if self._failure_count >= self.config.failure_threshold:
                    await self._transition_to_open()
            elif self._state == CircuitState.HALF_OPEN:
                await self._transition_to_open()
                
    async def _transition_to_open(self) -> None:
        """Transition to open state."""
        self._state = CircuitState.OPEN
        self.stats.state_changes.append({
            'from': 'closed' if len(self.stats.state_changes) == 0 else self._state.value,
            'to': 'open',
            'timestamp': datetime.now(),
            'reason': f'Failures exceeded threshold ({self._failure_count}/{self.config.failure_threshold})'
        })
        logger.warning(
            f"Circuit breaker '{self.name}' opened due to "
            f"{self._failure_count} failures"
        )
        
    async def _transition_to_half_open(self) -> None:
        """Transition to half-open state."""
        self._state = CircuitState.HALF_OPEN
        self.stats.state_changes.append({
            'from': 'open',
            'to': 'half_open',
            'timestamp': datetime.now(),
            'reason': 'Recovery timeout elapsed'
        })
        logger.info(f"Circuit breaker '{self.name}' half-opened for testing")
        
    async def _transition_to_closed(self) -> None:
        """Transition to closed state."""
        self._state = CircuitState.CLOSED
        self._failure_count = 0
        self._failure_timestamps = []
        self.stats.consecutive_failures = 0
        self.stats.state_changes.append({
            'from': 'half_open',
            'to': 'closed',
            'timestamp': datetime.now(),
            'reason': f'Success threshold reached ({self.stats.consecutive_successes})'
        })
        logger.info(f"Circuit breaker '{self.name}' closed - service recovered")
        
    async def _should_attempt_reset(self) -> bool:
        """Check if we should attempt to reset the circuit."""
        return (
            self._state == CircuitState.OPEN and
            self._last_failure_time and
            time.time() - self._last_failure_time >= self.config.recovery_timeout
        )
        
    async def call(self, func: Callable[..., T], *args, **kwargs) -> T:
        """
        Execute function with circuit breaker protection.
        
        Args:
            func: Function to execute
            *args: Function arguments
            **kwargs: Function keyword arguments
            
        Returns:
            Function result
            
        Raises:
            CircuitOpenError: If circuit is open
            Original exception: If function fails
        """
        # Check if we should transition from open to half-open
        if await self._should_attempt_reset():
            await self._transition_to_half_open()
            
        # If circuit is open, fail fast
        if self._state == CircuitState.OPEN:
            raise CircuitOpenError(
                f"Circuit breaker '{self.name}' is open - "
                f"service unavailable"
            )
            
        # Try to execute the function
        try:
            if asyncio.iscoroutinefunction(func):
                result = await func(*args, **kwargs)
            else:
                result = func(*args, **kwargs)
                
            await self._record_success()
            return result
            
        except self.config.expected_exception as e:
            await self._record_failure(e)
            raise
            
    def __call__(self, func: Callable[..., T]) -> Callable[..., T]:
        """Decorator usage of circuit breaker."""
        async def wrapper(*args, **kwargs):
            return await self.call(func, *args, **kwargs)
        return wrapper
        
    def get_stats(self) -> Dict[str, Any]:
        """Get circuit breaker statistics."""
        return {
            'name': self.name,
            'state': self._state.value,
            'total_calls': self.stats.total_calls,
            'successful_calls': self.stats.successful_calls,
            'failed_calls': self.stats.failed_calls,
            'success_rate': (
                self.stats.successful_calls / self.stats.total_calls 
                if self.stats.total_calls > 0 else 0
            ),
            'consecutive_failures': self.stats.consecutive_failures,
            'consecutive_successes': self.stats.consecutive_successes,
            'last_failure': self.stats.last_failure_time.isoformat() if self.stats.last_failure_time else None,
            'last_success': self.stats.last_success_time.isoformat() if self.stats.last_success_time else None,
            'failure_reasons': self.stats.failure_reasons,
            'state_changes': self.stats.state_changes[-10:]  # Last 10 state changes
        }


class CircuitOpenError(Exception):
    """Raised when circuit breaker is open."""
    pass


class CircuitBreakerRegistry:
    """Registry for managing multiple circuit breakers."""
    
    def __init__(self):
        self._breakers: Dict[str, CircuitBreaker] = {}
        
    def register(
        self,
        name: str,
        config: Optional[CircuitBreakerConfig] = None
    ) -> CircuitBreaker:
        """Register a new circuit breaker."""
        if name in self._breakers:
            return self._breakers[name]
            
        breaker = CircuitBreaker(name, config)
        self._breakers[name] = breaker
        return breaker
        
    def get(self, name: str) -> Optional[CircuitBreaker]:
        """Get circuit breaker by name."""
        return self._breakers.get(name)
        
    def get_all_stats(self) -> Dict[str, Dict[str, Any]]:
        """Get statistics for all circuit breakers."""
        return {
            name: breaker.get_stats()
            for name, breaker in self._breakers.items()
        }
        
    def reset(self, name: str) -> None:
        """Reset a circuit breaker to closed state."""
        if breaker := self._breakers.get(name):
            breaker._state = CircuitState.CLOSED
            breaker._failure_count = 0
            breaker._failure_timestamps = []
            breaker.stats.consecutive_failures = 0


# Global registry
circuit_registry = CircuitBreakerRegistry()