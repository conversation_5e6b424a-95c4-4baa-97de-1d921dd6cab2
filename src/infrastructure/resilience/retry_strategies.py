"""Retry strategies with exponential backoff and jitter."""

import asyncio
import random
import time
from dataclasses import dataclass
from datetime import datetime
from typing import Any, Callable, Dict, List, Optional, Set, Type, TypeVar, Union
import logging
from functools import wraps

logger = logging.getLogger(__name__)

T = TypeVar('T')


@dataclass
class RetryConfig:
    """Configuration for retry behavior."""
    max_attempts: int = 3
    initial_delay: float = 1.0  # seconds
    max_delay: float = 60.0  # seconds
    exponential_base: float = 2.0
    jitter: bool = True
    jitter_range: tuple[float, float] = (0.8, 1.2)  # 80% to 120% of calculated delay
    retryable_exceptions: Set[Type[Exception]] = None
    non_retryable_exceptions: Set[Type[Exception]] = None
    
    def __post_init__(self):
        if self.retryable_exceptions is None:
            self.retryable_exceptions = {Exception}
        if self.non_retryable_exceptions is None:
            self.non_retryable_exceptions = set()


@dataclass
class RetryAttempt:
    """Information about a retry attempt."""
    attempt_number: int
    delay: float
    exception: Exception
    timestamp: datetime


class RetryError(Exception):
    """Raised when all retry attempts are exhausted."""
    def __init__(self, message: str, attempts: List[RetryAttempt]):
        super().__init__(message)
        self.attempts = attempts


class RetryStrategy:
    """Base class for retry strategies."""
    
    def __init__(self, config: Optional[RetryConfig] = None):
        self.config = config or RetryConfig()
        
    def calculate_delay(self, attempt: int) -> float:
        """Calculate delay for given attempt number."""
        raise NotImplementedError
        
    def should_retry(self, exception: Exception) -> bool:
        """Determine if exception is retryable."""
        # Check non-retryable first
        if any(isinstance(exception, exc_type) for exc_type in self.config.non_retryable_exceptions):
            return False
            
        # Check retryable
        return any(isinstance(exception, exc_type) for exc_type in self.config.retryable_exceptions)


class ExponentialBackoffStrategy(RetryStrategy):
    """Exponential backoff with optional jitter."""
    
    def calculate_delay(self, attempt: int) -> float:
        """Calculate exponential backoff delay."""
        # Base delay calculation
        delay = min(
            self.config.initial_delay * (self.config.exponential_base ** (attempt - 1)),
            self.config.max_delay
        )
        
        # Apply jitter if configured
        if self.config.jitter:
            jitter_min, jitter_max = self.config.jitter_range
            delay *= random.uniform(jitter_min, jitter_max)
            
        return delay


class LinearBackoffStrategy(RetryStrategy):
    """Linear backoff with optional jitter."""
    
    def calculate_delay(self, attempt: int) -> float:
        """Calculate linear backoff delay."""
        delay = min(
            self.config.initial_delay * attempt,
            self.config.max_delay
        )
        
        if self.config.jitter:
            jitter_min, jitter_max = self.config.jitter_range
            delay *= random.uniform(jitter_min, jitter_max)
            
        return delay


class FixedDelayStrategy(RetryStrategy):
    """Fixed delay between retries."""
    
    def calculate_delay(self, attempt: int) -> float:
        """Return fixed delay."""
        return self.config.initial_delay


class RetryManager:
    """Manages retry logic with configurable strategies."""
    
    def __init__(
        self,
        strategy: Optional[RetryStrategy] = None,
        on_retry: Optional[Callable[[RetryAttempt], None]] = None
    ):
        self.strategy = strategy or ExponentialBackoffStrategy()
        self.on_retry = on_retry
        self._retry_stats: Dict[str, int] = {}
        
    async def execute_with_retry(
        self,
        func: Callable[..., T],
        *args,
        operation_name: Optional[str] = None,
        **kwargs
    ) -> T:
        """
        Execute function with retry logic.
        
        Args:
            func: Function to execute
            *args: Function arguments
            operation_name: Name for tracking/logging
            **kwargs: Function keyword arguments
            
        Returns:
            Function result
            
        Raises:
            RetryError: When all attempts exhausted
        """
        attempts: List[RetryAttempt] = []
        operation = operation_name or func.__name__
        
        for attempt_num in range(1, self.strategy.config.max_attempts + 1):
            try:
                # Execute function
                if asyncio.iscoroutinefunction(func):
                    result = await func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)
                    
                # Success - update stats
                self._retry_stats[f"{operation}_success"] = \
                    self._retry_stats.get(f"{operation}_success", 0) + 1
                    
                if attempt_num > 1:
                    logger.info(
                        f"Operation '{operation}' succeeded after "
                        f"{attempt_num} attempts"
                    )
                    
                return result
                
            except Exception as e:
                # Check if we should retry
                if not self.strategy.should_retry(e):
                    logger.error(
                        f"Operation '{operation}' failed with non-retryable "
                        f"exception: {type(e).__name__}: {str(e)}"
                    )
                    raise
                    
                # Record attempt
                attempt = RetryAttempt(
                    attempt_number=attempt_num,
                    delay=0,
                    exception=e,
                    timestamp=datetime.now()
                )
                attempts.append(attempt)
                
                # Check if we have more attempts
                if attempt_num >= self.strategy.config.max_attempts:
                    self._retry_stats[f"{operation}_exhausted"] = \
                        self._retry_stats.get(f"{operation}_exhausted", 0) + 1
                    raise RetryError(
                        f"Operation '{operation}' failed after "
                        f"{self.strategy.config.max_attempts} attempts",
                        attempts
                    )
                    
                # Calculate delay
                delay = self.strategy.calculate_delay(attempt_num)
                attempt.delay = delay
                
                # Log retry
                logger.warning(
                    f"Operation '{operation}' failed (attempt {attempt_num}/"
                    f"{self.strategy.config.max_attempts}): {type(e).__name__}: "
                    f"{str(e)}. Retrying in {delay:.2f}s..."
                )
                
                # Call retry callback if provided
                if self.on_retry:
                    self.on_retry(attempt)
                    
                # Update stats
                self._retry_stats[f"{operation}_retry"] = \
                    self._retry_stats.get(f"{operation}_retry", 0) + 1
                    
                # Wait before retry
                await asyncio.sleep(delay)
                
    def get_stats(self) -> Dict[str, int]:
        """Get retry statistics."""
        return self._retry_stats.copy()


def with_retry(
    strategy: Optional[RetryStrategy] = None,
    operation_name: Optional[str] = None
) -> Callable:
    """
    Decorator for adding retry logic to functions.
    
    Example:
        ```python
        @with_retry(
            strategy=ExponentialBackoffStrategy(
                RetryConfig(max_attempts=5, initial_delay=2.0)
            )
        )
        async def fetch_data():
            return await api_client.get("/data")
        ```
    """
    retry_manager = RetryManager(strategy)
    
    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            return await retry_manager.execute_with_retry(
                func, *args, operation_name=operation_name or func.__name__, **kwargs
            )
            
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            # For sync functions, we need to run in event loop
            loop = asyncio.get_event_loop()
            return loop.run_until_complete(
                retry_manager.execute_with_retry(
                    func, *args, operation_name=operation_name or func.__name__, **kwargs
                )
            )
            
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
            
    return decorator


# Pre-configured strategies for common use cases
AGGRESSIVE_RETRY = ExponentialBackoffStrategy(
    RetryConfig(
        max_attempts=10,
        initial_delay=0.5,
        max_delay=300,
        exponential_base=2
    )
)

GENTLE_RETRY = ExponentialBackoffStrategy(
    RetryConfig(
        max_attempts=3,
        initial_delay=2.0,
        max_delay=10,
        exponential_base=1.5
    )
)

RAPID_RETRY = FixedDelayStrategy(
    RetryConfig(
        max_attempts=5,
        initial_delay=0.1
    )
)


# Service-specific configurations
SERVICE_RETRY_CONFIGS = {
    'hdx': RetryConfig(
        max_attempts=5,
        initial_delay=2.0,
        max_delay=60,
        retryable_exceptions={ConnectionError, TimeoutError},
        non_retryable_exceptions={ValueError, TypeError}
    ),
    'acled': RetryConfig(
        max_attempts=3,
        initial_delay=5.0,
        max_delay=30,
        retryable_exceptions={ConnectionError, TimeoutError}
    ),
    'worldbank': RetryConfig(
        max_attempts=4,
        initial_delay=3.0,
        max_delay=45
    )
}