"""Data quality monitoring dashboard with anomaly detection."""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import numpy as np
import pandas as pd
from scipy import stats
import plotly.graph_objs as go
import plotly.express as px
from plotly.subplots import make_subplots
import dash
from dash import dcc, html, Input, Output
import dash_bootstrap_components as dbc
from sklearn.ensemble import IsolationForest
from sklearn.preprocessing import StandardScaler
import logging

logger = logging.getLogger(__name__)


class DataQualityDashboard:
    """
    Dashboard for monitoring data quality and detecting anomalies.
    
    Features:
    - Real-time quality metrics
    - Anomaly detection using statistical and ML methods
    - Historical quality trends
    - Source-specific quality monitoring
    - Alert configuration and visualization
    """
    
    def __init__(
        self,
        storage_path: str,
        update_interval: int = 10000  # 10 seconds
    ):
        self.storage_path = storage_path
        self.update_interval = update_interval
        
        # Initialize anomaly detectors
        self.isolation_forest = IsolationForest(
            contamination=0.1,
            random_state=42
        )
        self.scaler = StandardScaler()
        
        # Initialize Dash app
        self.app = dash.Dash(
            __name__,
            external_stylesheets=[dbc.themes.BOOTSTRAP]
        )
        
        # Setup layout and callbacks
        self._setup_layout()
        self._setup_callbacks()
        
    def _setup_layout(self):
        """Setup dashboard layout."""
        self.app.layout = dbc.Container([
            dbc.Row([
                dbc.Col([
                    html.H1("Data Quality Monitor", className="text-center mb-4"),
                    html.Hr()
                ])
            ]),
            
            # Quality score cards
            dbc.Row(id="quality-scores", className="mb-4"),
            
            # Main tabs
            dbc.Tabs([
                dbc.Tab(label="Quality Metrics", tab_id="metrics"),
                dbc.Tab(label="Anomaly Detection", tab_id="anomalies"),
                dbc.Tab(label="Data Profiling", tab_id="profiling"),
                dbc.Tab(label="Validation Rules", tab_id="validation"),
                dbc.Tab(label="Quality Trends", tab_id="trends")
            ], id="tabs", active_tab="metrics"),
            
            html.Div(id="tab-content", className="mt-4"),
            
            # Auto-refresh
            dcc.Interval(
                id="interval-component",
                interval=self.update_interval,
                n_intervals=0
            ),
            
            # Hidden stores
            html.Div(id="quality-data-store", style={"display": "none"}),
            html.Div(id="anomaly-data-store", style={"display": "none"})
        ], fluid=True)
        
    def _setup_callbacks(self):
        """Setup dashboard callbacks."""
        
        @self.app.callback(
            [
                Output("quality-data-store", "children"),
                Output("anomaly-data-store", "children"),
                Output("quality-scores", "children")
            ],
            [Input("interval-component", "n_intervals")]
        )
        def update_data(n):
            """Update quality data and scores."""
            # Get quality metrics
            quality_data = asyncio.run(self._calculate_quality_metrics())
            
            # Detect anomalies
            anomalies = asyncio.run(self._detect_anomalies(quality_data))
            
            # Create score cards
            score_cards = self._create_score_cards(quality_data)
            
            return (
                quality_data.to_json(orient='split'),
                pd.DataFrame(anomalies).to_json(orient='split'),
                score_cards
            )
            
        @self.app.callback(
            Output("tab-content", "children"),
            [
                Input("tabs", "active_tab"),
                Input("quality-data-store", "children"),
                Input("anomaly-data-store", "children")
            ]
        )
        def render_tab_content(active_tab, quality_json, anomaly_json):
            """Render tab content."""
            if not quality_json:
                return html.Div("Loading...")
                
            quality_df = pd.read_json(quality_json, orient='split')
            anomaly_df = pd.read_json(anomaly_json, orient='split') if anomaly_json else pd.DataFrame()
            
            if active_tab == "metrics":
                return self._render_metrics(quality_df)
            elif active_tab == "anomalies":
                return self._render_anomalies(anomaly_df)
            elif active_tab == "profiling":
                return self._render_profiling(quality_df)
            elif active_tab == "validation":
                return self._render_validation(quality_df)
            elif active_tab == "trends":
                return self._render_trends(quality_df)
                
            return html.Div()
            
    def _create_score_cards(self, quality_data: pd.DataFrame) -> List[dbc.Col]:
        """Create quality score cards."""
        cards = []
        
        # Overall quality score
        overall_score = quality_data['quality_score'].mean()
        cards.append(self._create_single_score_card(
            "Overall Quality",
            f"{overall_score:.1f}%",
            self._get_score_color(overall_score),
            "database"
        ))
        
        # Completeness
        completeness = quality_data['completeness'].mean()
        cards.append(self._create_single_score_card(
            "Completeness",
            f"{completeness:.1f}%",
            self._get_score_color(completeness),
            "check-circle"
        ))
        
        # Validity
        validity = quality_data['validity'].mean()
        cards.append(self._create_single_score_card(
            "Validity",
            f"{validity:.1f}%",
            self._get_score_color(validity),
            "shield-check"
        ))
        
        # Timeliness
        timeliness = quality_data['timeliness'].mean()
        cards.append(self._create_single_score_card(
            "Timeliness",
            f"{timeliness:.1f}%",
            self._get_score_color(timeliness),
            "clock"
        ))
        
        return cards
        
    def _create_single_score_card(
        self,
        title: str,
        value: str,
        color: str,
        icon: str
    ) -> dbc.Col:
        """Create a single score card."""
        return dbc.Col([
            dbc.Card([
                dbc.CardBody([
                    html.H5(title, className="card-title"),
                    html.H2(value, className=f"text-{color}"),
                    html.I(className=f"fas fa-{icon} fa-2x text-muted")
                ])
            ], className="text-center")
        ], width=3)
        
    def _get_score_color(self, score: float) -> str:
        """Get color based on score."""
        if score >= 90:
            return "success"
        elif score >= 70:
            return "warning"
        else:
            return "danger"
            
    def _render_metrics(self, quality_df: pd.DataFrame) -> html.Div:
        """Render quality metrics tab."""
        # Create metrics by source
        source_fig = px.bar(
            quality_df,
            x='source',
            y=['completeness', 'validity', 'consistency', 'timeliness'],
            title='Quality Metrics by Data Source',
            barmode='group'
        )
        
        # Create radar chart for dimensions
        categories = ['Completeness', 'Validity', 'Consistency', 'Timeliness', 'Uniqueness']
        
        fig_radar = go.Figure()
        
        for source in quality_df['source'].unique():
            source_data = quality_df[quality_df['source'] == source].iloc[0]
            values = [
                source_data['completeness'],
                source_data['validity'],
                source_data['consistency'],
                source_data['timeliness'],
                source_data['uniqueness']
            ]
            
            fig_radar.add_trace(go.Scatterpolar(
                r=values,
                theta=categories,
                fill='toself',
                name=source
            ))
            
        fig_radar.update_layout(
            polar=dict(
                radialaxis=dict(
                    visible=True,
                    range=[0, 100]
                )
            ),
            showlegend=True,
            title="Quality Dimensions by Source"
        )
        
        # Create detailed metrics table
        metrics_table = dbc.Table.from_dataframe(
            quality_df[['source', 'records', 'quality_score', 'issues']],
            striped=True,
            bordered=True,
            hover=True
        )
        
        return html.Div([
            dbc.Row([
                dbc.Col([dcc.Graph(figure=source_fig)], width=6),
                dbc.Col([dcc.Graph(figure=fig_radar)], width=6)
            ]),
            html.Hr(),
            html.H4("Detailed Metrics"),
            metrics_table
        ])
        
    def _render_anomalies(self, anomaly_df: pd.DataFrame) -> html.Div:
        """Render anomaly detection tab."""
        if anomaly_df.empty:
            return html.Div([
                html.H3("No Anomalies Detected"),
                html.P("All data quality metrics are within normal ranges.", 
                      className="text-success")
            ])
            
        # Create anomaly scatter plot
        fig_scatter = px.scatter(
            anomaly_df,
            x='timestamp',
            y='metric_value',
            color='anomaly_type',
            size='severity_score',
            hover_data=['source', 'metric', 'description'],
            title='Detected Anomalies Timeline'
        )
        
        # Create anomaly distribution
        fig_dist = px.histogram(
            anomaly_df,
            x='anomaly_type',
            color='severity',
            title='Anomaly Distribution by Type'
        )
        
        # Create anomaly alerts
        alerts = []
        for _, anomaly in anomaly_df.head(5).iterrows():
            severity_color = {
                'low': 'info',
                'medium': 'warning',
                'high': 'danger',
                'critical': 'danger'
            }.get(anomaly['severity'], 'secondary')
            
            alert = dbc.Alert([
                html.H5(f"{anomaly['source']} - {anomaly['anomaly_type']}"),
                html.P(anomaly['description']),
                html.Small(f"Detected at {anomaly['timestamp']}")
            ], color=severity_color, dismissable=True)
            alerts.append(alert)
            
        return html.Div([
            html.H3("Recent Anomalies"),
            html.Div(alerts),
            html.Hr(),
            dbc.Row([
                dbc.Col([dcc.Graph(figure=fig_scatter)], width=12)
            ]),
            dbc.Row([
                dbc.Col([dcc.Graph(figure=fig_dist)], width=12)
            ])
        ])
        
    def _render_profiling(self, quality_df: pd.DataFrame) -> html.Div:
        """Render data profiling tab."""
        # Get sample data for profiling
        profile_data = asyncio.run(self._get_data_profile())
        
        profile_cards = []
        for source, profile in profile_data.items():
            card = dbc.Card([
                dbc.CardHeader(html.H4(source)),
                dbc.CardBody([
                    html.P(f"Total Records: {profile['total_records']:,}"),
                    html.P(f"Columns: {profile['num_columns']}"),
                    html.P(f"Missing Values: {profile['missing_percentage']:.1f}%"),
                    html.P(f"Duplicate Rows: {profile['duplicate_rows']}"),
                    html.Hr(),
                    html.H6("Column Types"),
                    html.Ul([
                        html.Li(f"{col_type}: {count}")
                        for col_type, count in profile['column_types'].items()
                    ])
                ])
            ])
            profile_cards.append(dbc.Col([card], width=4))
            
        return html.Div([
            html.H3("Data Profiling"),
            dbc.Row(profile_cards)
        ])
        
    def _render_validation(self, quality_df: pd.DataFrame) -> html.Div:
        """Render validation rules tab."""
        # Get validation rules and results
        validation_results = asyncio.run(self._get_validation_results())
        
        # Create rules table
        rules_data = []
        for rule in validation_results:
            rules_data.append({
                'Rule': rule['name'],
                'Type': rule['type'],
                'Source': rule['source'],
                'Passed': f"{rule['passed']:,}",
                'Failed': f"{rule['failed']:,}",
                'Success Rate': f"{rule['success_rate']:.1%}",
                'Status': '✓' if rule['success_rate'] > 0.95 else '✗'
            })
            
        rules_df = pd.DataFrame(rules_data)
        rules_table = dbc.Table.from_dataframe(
            rules_df,
            striped=True,
            bordered=True,
            hover=True
        )
        
        # Create validation heatmap
        pivot_data = pd.pivot_table(
            pd.DataFrame(validation_results),
            values='success_rate',
            index='source',
            columns='type',
            aggfunc='mean'
        )
        
        fig_heatmap = go.Figure(data=go.Heatmap(
            z=pivot_data.values,
            x=pivot_data.columns,
            y=pivot_data.index,
            colorscale='RdYlGn',
            text=np.round(pivot_data.values * 100, 1),
            texttemplate='%{text}%',
            textfont={"size": 12}
        ))
        
        fig_heatmap.update_layout(
            title="Validation Success Rates by Source and Type",
            xaxis_title="Validation Type",
            yaxis_title="Data Source"
        )
        
        return html.Div([
            html.H3("Validation Rules"),
            rules_table,
            html.Hr(),
            dcc.Graph(figure=fig_heatmap)
        ])
        
    def _render_trends(self, quality_df: pd.DataFrame) -> html.Div:
        """Render quality trends tab."""
        # Get historical quality data
        history_df = asyncio.run(self._get_quality_history())
        
        # Create multi-line trend chart
        fig_trends = make_subplots(
            rows=2, cols=2,
            subplot_titles=(
                'Overall Quality Score',
                'Completeness Trend',
                'Validation Failures',
                'Processing Time'
            )
        )
        
        # Overall quality
        for source in history_df['source'].unique():
            source_data = history_df[history_df['source'] == source]
            fig_trends.add_trace(
                go.Scatter(
                    x=source_data['timestamp'],
                    y=source_data['quality_score'],
                    mode='lines',
                    name=source
                ),
                row=1, col=1
            )
            
        # Completeness
        for source in history_df['source'].unique():
            source_data = history_df[history_df['source'] == source]
            fig_trends.add_trace(
                go.Scatter(
                    x=source_data['timestamp'],
                    y=source_data['completeness'],
                    mode='lines',
                    name=source,
                    showlegend=False
                ),
                row=1, col=2
            )
            
        # Validation failures
        fig_trends.add_trace(
            go.Scatter(
                x=history_df.groupby('timestamp').sum().index,
                y=history_df.groupby('timestamp')['validation_failures'].sum(),
                mode='lines+markers',
                name='Total Failures',
                line=dict(color='red')
            ),
            row=2, col=1
        )
        
        # Processing time
        fig_trends.add_trace(
            go.Scatter(
                x=history_df.groupby('timestamp').mean().index,
                y=history_df.groupby('timestamp')['processing_time'].mean(),
                mode='lines',
                name='Avg Time',
                line=dict(color='green')
            ),
            row=2, col=2
        )
        
        fig_trends.update_layout(height=800)
        
        # Create quality degradation alerts
        degradation_alerts = self._detect_quality_degradation(history_df)
        
        alerts = []
        for alert in degradation_alerts:
            alerts.append(dbc.Alert(
                f"{alert['source']}: Quality decreased by {alert['decrease']:.1f}% "
                f"over the last {alert['period']}",
                color="warning"
            ))
            
        return html.Div([
            html.H3("Quality Trends"),
            html.Div(alerts) if alerts else None,
            dcc.Graph(figure=fig_trends)
        ])
        
    async def _calculate_quality_metrics(self) -> pd.DataFrame:
        """Calculate current quality metrics."""
        # This would integrate with your actual data sources
        # For demonstration, creating mock data
        sources = ['wfp', 'acled', 'climate', 'population']
        
        metrics = []
        for source in sources:
            metrics.append({
                'source': source,
                'timestamp': datetime.now(),
                'records': np.random.randint(1000, 10000),
                'completeness': np.random.uniform(85, 98),
                'validity': np.random.uniform(80, 99),
                'consistency': np.random.uniform(75, 95),
                'timeliness': np.random.uniform(70, 100),
                'uniqueness': np.random.uniform(90, 100),
                'quality_score': np.random.uniform(80, 95),
                'issues': np.random.randint(0, 50)
            })
            
        return pd.DataFrame(metrics)
        
    async def _detect_anomalies(self, quality_df: pd.DataFrame) -> List[Dict]:
        """Detect anomalies in quality metrics."""
        anomalies = []
        
        # Statistical anomaly detection
        for _, row in quality_df.iterrows():
            # Check for low quality scores
            if row['quality_score'] < 70:
                anomalies.append({
                    'timestamp': row['timestamp'],
                    'source': row['source'],
                    'metric': 'quality_score',
                    'metric_value': row['quality_score'],
                    'anomaly_type': 'Low Quality',
                    'severity': 'high',
                    'severity_score': 90,
                    'description': f"Quality score {row['quality_score']:.1f}% is below threshold"
                })
                
            # Check for completeness issues
            if row['completeness'] < 80:
                anomalies.append({
                    'timestamp': row['timestamp'],
                    'source': row['source'],
                    'metric': 'completeness',
                    'metric_value': row['completeness'],
                    'anomaly_type': 'Incomplete Data',
                    'severity': 'medium',
                    'severity_score': 70,
                    'description': f"Data completeness {row['completeness']:.1f}% is concerning"
                })
                
        # ML-based anomaly detection
        if len(quality_df) > 10:
            features = quality_df[['completeness', 'validity', 'consistency', 'timeliness']].values
            scaled_features = self.scaler.fit_transform(features)
            
            # Fit and predict
            predictions = self.isolation_forest.fit_predict(scaled_features)
            
            # Add ML-detected anomalies
            for idx, pred in enumerate(predictions):
                if pred == -1:  # Anomaly
                    row = quality_df.iloc[idx]
                    anomalies.append({
                        'timestamp': row['timestamp'],
                        'source': row['source'],
                        'metric': 'overall',
                        'metric_value': row['quality_score'],
                        'anomaly_type': 'Pattern Anomaly',
                        'severity': 'medium',
                        'severity_score': 60,
                        'description': 'Unusual pattern detected in quality metrics'
                    })
                    
        return anomalies
        
    async def _get_data_profile(self) -> Dict[str, Dict]:
        """Get data profiling information."""
        # Mock data profiling
        return {
            'wfp': {
                'total_records': 33926,
                'num_columns': 15,
                'missing_percentage': 3.2,
                'duplicate_rows': 12,
                'column_types': {
                    'numeric': 8,
                    'string': 5,
                    'datetime': 2
                }
            },
            'acled': {
                'total_records': 15432,
                'num_columns': 22,
                'missing_percentage': 5.1,
                'duplicate_rows': 0,
                'column_types': {
                    'numeric': 10,
                    'string': 10,
                    'datetime': 2
                }
            }
        }
        
    async def _get_validation_results(self) -> List[Dict]:
        """Get validation rule results."""
        # Mock validation results
        rules = [
            {
                'name': 'Price Range Check',
                'type': 'Range',
                'source': 'wfp',
                'passed': 32845,
                'failed': 1081,
                'success_rate': 0.968
            },
            {
                'name': 'Date Format Validation',
                'type': 'Format',
                'source': 'wfp',
                'passed': 33926,
                'failed': 0,
                'success_rate': 1.0
            },
            {
                'name': 'Coordinate Validation',
                'type': 'Spatial',
                'source': 'acled',
                'passed': 15200,
                'failed': 232,
                'success_rate': 0.985
            }
        ]
        return rules
        
    async def _get_quality_history(self) -> pd.DataFrame:
        """Get historical quality data."""
        # Generate mock historical data
        history = []
        sources = ['wfp', 'acled', 'climate']
        
        for hours_ago in range(24, 0, -1):
            timestamp = datetime.now() - timedelta(hours=hours_ago)
            for source in sources:
                history.append({
                    'timestamp': timestamp,
                    'source': source,
                    'quality_score': 90 + np.random.normal(0, 5),
                    'completeness': 95 + np.random.normal(0, 3),
                    'validation_failures': np.random.poisson(5),
                    'processing_time': 30 + np.random.normal(0, 10)
                })
                
        return pd.DataFrame(history)
        
    def _detect_quality_degradation(self, history_df: pd.DataFrame) -> List[Dict]:
        """Detect quality degradation trends."""
        alerts = []
        
        for source in history_df['source'].unique():
            source_data = history_df[history_df['source'] == source].sort_values('timestamp')
            
            # Compare last 6 hours to previous 6 hours
            recent = source_data.tail(6)['quality_score'].mean()
            previous = source_data.iloc[-12:-6]['quality_score'].mean()
            
            decrease = previous - recent
            if decrease > 5:
                alerts.append({
                    'source': source,
                    'decrease': decrease,
                    'period': '6 hours'
                })
                
        return alerts
        
    def run(self, debug: bool = False, port: int = 8051):
        """Run the dashboard server."""
        self.app.run_server(debug=debug, port=port)