"""Real-time pipeline monitoring dashboard using Plotly Dash."""

import asyncio
import json
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional
import pandas as pd
import plotly.graph_objs as go
import plotly.express as px
from plotly.subplots import make_subplots
import dash
from dash import dcc, html, Input, Output, State, callback_context
import dash_bootstrap_components as dbc
from dash.exceptions import PreventUpdate
import logging

from src.infrastructure.monitoring.metrics_collector import MetricsCollector
from src.infrastructure.monitoring.health_checker import <PERSON>Checker
from src.infrastructure.resilience.circuit_breaker import circuit_registry
from src.infrastructure.queues.dead_letter_queue import DeadLetterQueue, RecordStatus
from src.application.services.data_pipeline_orchestrator import (
    DataPipelineOrchestrator,
    PipelineState
)

logger = logging.getLogger(__name__)


class PipelineDashboard:
    """
    Real-time monitoring dashboard for the data pipeline.
    
    Features:
    - Pipeline state visualization
    - Processing metrics and throughput
    - Circuit breaker status
    - Dead letter queue monitoring
    - Data quality indicators
    - Historical trends
    """
    
    def __init__(
        self,
        orchestrator: DataPipelineOrchestrator,
        metrics_collector: MetricsCollector,
        health_checker: HealthChecker,
        dlq: DeadLetterQueue,
        update_interval: int = 5000  # 5 seconds
    ):
        self.orchestrator = orchestrator
        self.metrics = metrics_collector
        self.health = health_checker
        self.dlq = dlq
        self.update_interval = update_interval
        
        # Initialize Dash app
        self.app = dash.Dash(
            __name__,
            external_stylesheets=[dbc.themes.BOOTSTRAP],
            suppress_callback_exceptions=True
        )
        
        # Setup layout
        self._setup_layout()
        
        # Setup callbacks
        self._setup_callbacks()
        
    def _setup_layout(self):
        """Setup the dashboard layout."""
        self.app.layout = dbc.Container([
            dbc.Row([
                dbc.Col([
                    html.H1("Yemen Data Pipeline Monitor", className="text-center mb-4"),
                    html.Hr()
                ])
            ]),
            
            # Status cards row
            dbc.Row([
                dbc.Col([
                    self._create_status_card("pipeline-state", "Pipeline State", "hourglass")
                ], width=3),
                dbc.Col([
                    self._create_status_card("health-status", "Health Status", "heart")
                ], width=3),
                dbc.Col([
                    self._create_status_card("throughput", "Throughput", "speedometer")
                ], width=3),
                dbc.Col([
                    self._create_status_card("errors", "Error Rate", "exclamation-triangle")
                ], width=3),
            ], className="mb-4"),
            
            # Main content tabs
            dbc.Tabs([
                dbc.Tab(label="Pipeline Overview", tab_id="overview"),
                dbc.Tab(label="Data Quality", tab_id="quality"),
                dbc.Tab(label="Circuit Breakers", tab_id="circuits"),
                dbc.Tab(label="Dead Letter Queue", tab_id="dlq"),
                dbc.Tab(label="Historical Trends", tab_id="trends"),
                dbc.Tab(label="Anomaly Detection", tab_id="anomalies")
            ], id="tabs", active_tab="overview"),
            
            html.Div(id="tab-content", className="mt-4"),
            
            # Auto-refresh interval
            dcc.Interval(
                id="interval-component",
                interval=self.update_interval,
                n_intervals=0
            ),
            
            # Hidden div to store data
            html.Div(id="metrics-store", style={"display": "none"})
        ], fluid=True)
        
    def _create_status_card(self, card_id: str, title: str, icon: str) -> dbc.Card:
        """Create a status card component."""
        return dbc.Card([
            dbc.CardBody([
                html.H4(title, className="card-title"),
                html.H2(id=f"{card_id}-value", children="--"),
                html.P(id=f"{card_id}-delta", className="text-muted")
            ])
        ], color="light", outline=True)
        
    def _setup_callbacks(self):
        """Setup dashboard callbacks."""
        
        @self.app.callback(
            [
                Output("metrics-store", "children"),
                Output("pipeline-state-value", "children"),
                Output("health-status-value", "children"),
                Output("throughput-value", "children"),
                Output("errors-value", "children"),
            ],
            [Input("interval-component", "n_intervals")]
        )
        def update_metrics(n):
            """Update metrics data."""
            # Get current metrics
            current_metrics = asyncio.run(self._get_current_metrics())
            
            # Extract key values
            pipeline_state = current_metrics.get("pipeline_state", "Unknown")
            health_status = "Healthy" if current_metrics.get("is_healthy", False) else "Unhealthy"
            throughput = f"{current_metrics.get('throughput', 0):.0f}/min"
            error_rate = f"{current_metrics.get('error_rate', 0):.1%}"
            
            return (
                json.dumps(current_metrics),
                pipeline_state,
                health_status,
                throughput,
                error_rate
            )
            
        @self.app.callback(
            Output("tab-content", "children"),
            [Input("tabs", "active_tab"), Input("metrics-store", "children")]
        )
        def render_tab_content(active_tab, metrics_json):
            """Render content based on active tab."""
            if not metrics_json:
                return html.Div("Loading...")
                
            metrics = json.loads(metrics_json)
            
            if active_tab == "overview":
                return self._render_overview(metrics)
            elif active_tab == "quality":
                return self._render_quality(metrics)
            elif active_tab == "circuits":
                return self._render_circuits(metrics)
            elif active_tab == "dlq":
                return self._render_dlq(metrics)
            elif active_tab == "trends":
                return self._render_trends(metrics)
            elif active_tab == "anomalies":
                return self._render_anomalies(metrics)
                
            return html.Div()
            
    def _render_overview(self, metrics: Dict[str, Any]) -> html.Div:
        """Render pipeline overview tab."""
        # Create state transition diagram
        state_fig = self._create_state_diagram(metrics.get("state_history", []))
        
        # Create processor status grid
        processor_fig = self._create_processor_status(metrics.get("processor_metrics", {}))
        
        # Create throughput timeline
        throughput_fig = self._create_throughput_timeline(metrics.get("throughput_history", []))
        
        return html.Div([
            dbc.Row([
                dbc.Col([
                    html.H3("Pipeline State Flow"),
                    dcc.Graph(figure=state_fig)
                ], width=6),
                dbc.Col([
                    html.H3("Processor Status"),
                    dcc.Graph(figure=processor_fig)
                ], width=6)
            ]),
            dbc.Row([
                dbc.Col([
                    html.H3("Throughput Timeline"),
                    dcc.Graph(figure=throughput_fig)
                ])
            ])
        ])
        
    def _render_quality(self, metrics: Dict[str, Any]) -> html.Div:
        """Render data quality tab."""
        quality_metrics = metrics.get("quality_metrics", {})
        
        # Create quality score gauges
        quality_figs = []
        for source, scores in quality_metrics.items():
            fig = go.Figure(go.Indicator(
                mode="gauge+number+delta",
                value=scores.get("overall", 0),
                domain={'x': [0, 1], 'y': [0, 1]},
                title={'text': source},
                delta={'reference': scores.get("previous", 0)},
                gauge={
                    'axis': {'range': [None, 100]},
                    'bar': {'color': "darkblue"},
                    'steps': [
                        {'range': [0, 50], 'color': "lightgray"},
                        {'range': [50, 80], 'color': "gray"}
                    ],
                    'threshold': {
                        'line': {'color': "red", 'width': 4},
                        'thickness': 0.75,
                        'value': 90
                    }
                }
            ))
            quality_figs.append(dbc.Col([dcc.Graph(figure=fig)], width=4))
            
        # Create validation issues table
        issues_df = pd.DataFrame(metrics.get("validation_issues", []))
        issues_table = dbc.Table.from_dataframe(
            issues_df,
            striped=True,
            bordered=True,
            hover=True
        ) if not issues_df.empty else html.P("No validation issues detected")
        
        return html.Div([
            html.H3("Data Quality Scores"),
            dbc.Row(quality_figs),
            html.Hr(),
            html.H3("Validation Issues"),
            issues_table
        ])
        
    def _render_circuits(self, metrics: Dict[str, Any]) -> html.Div:
        """Render circuit breakers tab."""
        circuits = circuit_registry.get_all_stats()
        
        # Create circuit status cards
        circuit_cards = []
        for name, stats in circuits.items():
            color = {
                'closed': 'success',
                'open': 'danger',
                'half_open': 'warning'
            }.get(stats['state'], 'secondary')
            
            card = dbc.Col([
                dbc.Card([
                    dbc.CardHeader(html.H4(name)),
                    dbc.CardBody([
                        dbc.Badge(stats['state'].upper(), color=color, className="mb-2"),
                        html.P(f"Success Rate: {stats['success_rate']:.1%}"),
                        html.P(f"Total Calls: {stats['total_calls']}"),
                        html.P(f"Consecutive Failures: {stats['consecutive_failures']}"),
                        html.Small(f"Last Failure: {stats.get('last_failure', 'Never')}")
                    ])
                ], color=color, outline=True)
            ], width=4)
            circuit_cards.append(card)
            
        # Create failure reasons chart
        failure_data = []
        for name, stats in circuits.items():
            for reason, count in stats.get('failure_reasons', {}).items():
                failure_data.append({
                    'Circuit': name,
                    'Reason': reason,
                    'Count': count
                })
                
        if failure_data:
            failure_df = pd.DataFrame(failure_data)
            failure_fig = px.bar(
                failure_df,
                x='Circuit',
                y='Count',
                color='Reason',
                title='Circuit Breaker Failure Reasons'
            )
        else:
            failure_fig = go.Figure()
            
        return html.Div([
            html.H3("Circuit Breaker Status"),
            dbc.Row(circuit_cards),
            html.Hr(),
            dcc.Graph(figure=failure_fig)
        ])
        
    def _render_dlq(self, metrics: Dict[str, Any]) -> html.Div:
        """Render dead letter queue tab."""
        dlq_stats = asyncio.run(self._get_dlq_stats())
        
        # Create DLQ overview cards
        overview_cards = [
            dbc.Col([
                dbc.Card([
                    dbc.CardBody([
                        html.H4("Queue Size"),
                        html.H2(dlq_stats['queue_size']),
                        html.P(f"of {dlq_stats['max_size']} max")
                    ])
                ])
            ], width=3),
            dbc.Col([
                dbc.Card([
                    dbc.CardBody([
                        html.H4("Total Enqueued"),
                        html.H2(dlq_stats['stats']['total_enqueued'])
                    ])
                ])
            ], width=3),
            dbc.Col([
                dbc.Card([
                    dbc.CardBody([
                        html.H4("Success Rate"),
                        html.H2(f"{dlq_stats.get('success_rate', 0):.1%}")
                    ])
                ])
            ], width=3),
            dbc.Col([
                dbc.Card([
                    dbc.CardBody([
                        html.H4("Expired"),
                        html.H2(dlq_stats['stats']['total_expired'])
                    ])
                ])
            ], width=3)
        ]
        
        # Create records table
        records = asyncio.run(self.dlq.get_records(limit=50))
        records_data = [{
            'ID': r.id[:20] + '...',
            'Source': r.source,
            'Type': r.record_type,
            'Status': r.status.value,
            'Retries': f"{r.retry_count}/{r.max_retries}",
            'Age': str(datetime.now() - r.timestamp).split('.')[0],
            'Error': r.error[:50] + '...'
        } for r in records]
        
        records_df = pd.DataFrame(records_data)
        records_table = dbc.Table.from_dataframe(
            records_df,
            striped=True,
            bordered=True,
            hover=True,
            size='sm'
        ) if records_data else html.P("No records in dead letter queue")
        
        return html.Div([
            html.H3("Dead Letter Queue Overview"),
            dbc.Row(overview_cards),
            html.Hr(),
            html.H3("Recent Records"),
            records_table
        ])
        
    def _render_trends(self, metrics: Dict[str, Any]) -> html.Div:
        """Render historical trends tab."""
        # Get historical data
        history = asyncio.run(self.metrics.get_time_series(
            metrics=['throughput', 'error_rate', 'processing_time'],
            start_time=datetime.now() - timedelta(hours=24)
        ))
        
        # Create multi-line chart
        fig = make_subplots(
            rows=3, cols=1,
            subplot_titles=('Throughput', 'Error Rate', 'Processing Time'),
            shared_xaxes=True
        )
        
        # Add throughput
        fig.add_trace(
            go.Scatter(
                x=history['timestamps'],
                y=history['throughput'],
                mode='lines',
                name='Throughput'
            ),
            row=1, col=1
        )
        
        # Add error rate
        fig.add_trace(
            go.Scatter(
                x=history['timestamps'],
                y=history['error_rate'],
                mode='lines',
                name='Error Rate',
                line=dict(color='red')
            ),
            row=2, col=1
        )
        
        # Add processing time
        fig.add_trace(
            go.Scatter(
                x=history['timestamps'],
                y=history['processing_time'],
                mode='lines',
                name='Processing Time',
                line=dict(color='green')
            ),
            row=3, col=1
        )
        
        fig.update_layout(height=800, showlegend=False)
        
        return html.Div([
            html.H3("24-Hour Historical Trends"),
            dcc.Graph(figure=fig)
        ])
        
    def _render_anomalies(self, metrics: Dict[str, Any]) -> html.Div:
        """Render anomaly detection tab."""
        anomalies = metrics.get("anomalies", [])
        
        if not anomalies:
            return html.Div([
                html.H3("Anomaly Detection"),
                html.P("No anomalies detected in the last 24 hours", className="text-success")
            ])
            
        # Create anomaly timeline
        anomaly_df = pd.DataFrame(anomalies)
        fig = px.scatter(
            anomaly_df,
            x='timestamp',
            y='metric',
            size='severity',
            color='type',
            hover_data=['description'],
            title='Detected Anomalies Timeline'
        )
        
        # Create anomaly cards
        anomaly_cards = []
        for idx, anomaly in enumerate(anomalies[:5]):  # Show latest 5
            severity_color = {
                'low': 'info',
                'medium': 'warning',
                'high': 'danger'
            }.get(anomaly.get('severity', 'low'), 'secondary')
            
            card = dbc.Alert([
                html.H5(f"{anomaly['type']} - {anomaly['metric']}"),
                html.P(anomaly['description']),
                html.Small(f"Detected at {anomaly['timestamp']}")
            ], color=severity_color)
            anomaly_cards.append(card)
            
        return html.Div([
            html.H3("Anomaly Detection"),
            dbc.Row([dbc.Col(anomaly_cards)]),
            html.Hr(),
            dcc.Graph(figure=fig)
        ])
        
    def _create_state_diagram(self, state_history: List[Dict]) -> go.Figure:
        """Create pipeline state transition diagram."""
        if not state_history:
            return go.Figure()
            
        # Extract states and timestamps
        states = [s['state'] for s in state_history]
        timestamps = [s['timestamp'] for s in state_history]
        
        # Create Gantt-like chart
        fig = go.Figure()
        
        for i in range(len(states) - 1):
            color = {
                PipelineState.IDLE.value: 'lightgray',
                PipelineState.INITIALIZING.value: 'yellow',
                PipelineState.RUNNING.value: 'green',
                PipelineState.PAUSED.value: 'orange',
                PipelineState.ERROR.value: 'red',
                PipelineState.COMPLETED.value: 'blue'
            }.get(states[i], 'gray')
            
            fig.add_trace(go.Scatter(
                x=[timestamps[i], timestamps[i+1]],
                y=[0, 0],
                mode='lines',
                line=dict(color=color, width=20),
                name=states[i],
                showlegend=i == 0
            ))
            
        fig.update_layout(
            title="Pipeline State Timeline",
            xaxis_title="Time",
            yaxis_visible=False,
            height=200
        )
        
        return fig
        
    def _create_processor_status(self, processor_metrics: Dict) -> go.Figure:
        """Create processor status heatmap."""
        processors = list(processor_metrics.keys())
        metrics = ['status', 'records', 'errors', 'duration']
        
        # Create data matrix
        data = []
        for metric in metrics:
            row = []
            for processor in processors:
                value = processor_metrics.get(processor, {}).get(metric, 0)
                if metric == 'status':
                    value = 1 if value == 'success' else 0
                row.append(value)
            data.append(row)
            
        fig = go.Figure(data=go.Heatmap(
            z=data,
            x=processors,
            y=metrics,
            colorscale='RdYlGn'
        ))
        
        fig.update_layout(
            title="Processor Status Matrix",
            height=300
        )
        
        return fig
        
    def _create_throughput_timeline(self, throughput_history: List[Dict]) -> go.Figure:
        """Create throughput timeline chart."""
        if not throughput_history:
            return go.Figure()
            
        df = pd.DataFrame(throughput_history)
        
        fig = go.Figure()
        fig.add_trace(go.Scatter(
            x=df['timestamp'],
            y=df['records_per_minute'],
            mode='lines+markers',
            name='Throughput',
            line=dict(color='blue', width=2)
        ))
        
        # Add moving average
        df['ma'] = df['records_per_minute'].rolling(window=5).mean()
        fig.add_trace(go.Scatter(
            x=df['timestamp'],
            y=df['ma'],
            mode='lines',
            name='5-min Average',
            line=dict(color='orange', width=1, dash='dash')
        ))
        
        fig.update_layout(
            title="Processing Throughput",
            xaxis_title="Time",
            yaxis_title="Records/Minute",
            height=300
        )
        
        return fig
        
    async def _get_current_metrics(self) -> Dict[str, Any]:
        """Get current metrics snapshot."""
        # Get pipeline state
        pipeline_metrics = await self.orchestrator.get_metrics()
        
        # Get health status
        health_status = await self.health.check_health()
        
        # Get DLQ stats
        dlq_stats = self.dlq.get_stats()
        
        # Calculate derived metrics
        total_processed = sum(
            m.get('records_processed', 0)
            for m in pipeline_metrics.get('processor_metrics', {}).values()
        )
        total_errors = sum(
            m.get('errors', 0)
            for m in pipeline_metrics.get('processor_metrics', {}).values()
        )
        
        error_rate = total_errors / total_processed if total_processed > 0 else 0
        
        # Get throughput from last 5 minutes
        recent_metrics = await self.metrics.get_time_series(
            metrics=['records_processed'],
            start_time=datetime.now() - timedelta(minutes=5)
        )
        throughput = len(recent_metrics.get('records_processed', [])) * 12  # per minute
        
        return {
            'pipeline_state': pipeline_metrics.get('current_state', 'Unknown'),
            'is_healthy': health_status['overall_health'],
            'throughput': throughput,
            'error_rate': error_rate,
            'processor_metrics': pipeline_metrics.get('processor_metrics', {}),
            'quality_metrics': await self._get_quality_metrics(),
            'state_history': pipeline_metrics.get('state_history', [])[-20:],
            'throughput_history': await self._get_throughput_history(),
            'validation_issues': await self._get_validation_issues(),
            'anomalies': await self._detect_anomalies()
        }
        
    async def _get_dlq_stats(self) -> Dict[str, Any]:
        """Get DLQ statistics with success rate."""
        stats = self.dlq.get_stats()
        
        # Calculate success rate
        total_processed = (
            stats['stats']['total_succeeded'] +
            stats['stats']['total_failed']
        )
        success_rate = (
            stats['stats']['total_succeeded'] / total_processed
            if total_processed > 0 else 0
        )
        
        stats['success_rate'] = success_rate
        return stats
        
    async def _get_quality_metrics(self) -> Dict[str, Dict[str, float]]:
        """Get data quality metrics by source."""
        # This would integrate with your data quality validation
        # For now, returning mock data
        return {
            'wfp': {'overall': 92.5, 'previous': 91.0},
            'acled': {'overall': 88.0, 'previous': 87.5},
            'climate': {'overall': 95.0, 'previous': 95.0}
        }
        
    async def _get_throughput_history(self) -> List[Dict]:
        """Get throughput history."""
        # This would query your metrics store
        # For now, returning mock data
        history = []
        base_time = datetime.now() - timedelta(hours=1)
        for i in range(60):
            history.append({
                'timestamp': base_time + timedelta(minutes=i),
                'records_per_minute': 100 + (i % 20) * 5
            })
        return history
        
    async def _get_validation_issues(self) -> List[Dict]:
        """Get recent validation issues."""
        # This would query your validation logs
        # For now, returning mock data
        return [
            {
                'source': 'wfp',
                'issue': 'Missing exchange rate',
                'severity': 'medium',
                'count': 12,
                'last_seen': datetime.now() - timedelta(minutes=5)
            }
        ]
        
    async def _detect_anomalies(self) -> List[Dict]:
        """Detect anomalies in metrics."""
        # This would use statistical methods or ML
        # For now, returning mock data
        return [
            {
                'type': 'Spike',
                'metric': 'error_rate',
                'severity': 'high',
                'description': 'Error rate increased by 300%',
                'timestamp': datetime.now() - timedelta(minutes=30)
            }
        ]
        
    def run(self, debug: bool = False, port: int = 8050):
        """Run the dashboard server."""
        self.app.run_server(debug=debug, port=port)