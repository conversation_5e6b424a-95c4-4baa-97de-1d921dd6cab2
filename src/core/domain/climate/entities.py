from dataclasses import dataclass
from datetime import datetime
from typing import Optional, Dict, Any
from .value_objects import ClimateVariable, TemporalResolution, DroughtIndicator

@dataclass
class ClimateObservation:
    market_id: str
    timestamp: datetime
    variable: ClimateVariable
    value: float
    unit: str
    source: str
    location: Any  # Geometry
    resolution: TemporalResolution
    drought_indicator: Optional[DroughtIndicator] = None
    anomaly: Optional[float] = None
    anomaly_pct: Optional[float] = None

@dataclass
class ClimateMetrics:
    variable: ClimateVariable
    period_start: datetime
    period_end: datetime
    mean_value: float
    std_value: float
    min_value: float
    max_value: float
    coverage: float
    drought_affected_markets: int
    extreme_events_count: int
    observations_count: Optional[int] = None
    markets_count: Optional[int] = None