from enum import Enum
from dataclasses import dataclass

class ClimateVariable(Enum):
    RAINFALL = "rainfall"
    PRECIPITATION = "precipitation"
    NDVI = "ndvi"
    TEMPERATURE = "temperature"
    EVI = "evi"
    OTHER = "other"

class TemporalResolution(Enum):
    DAILY = "daily"
    WEEKLY = "weekly"
    SIXTEEN_DAY = "16-day"
    MONTHLY = "monthly"

@dataclass
class DroughtIndicator:
    spi: float
    category: str
    severity: float