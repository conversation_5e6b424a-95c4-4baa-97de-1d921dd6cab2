"""Food security domain entities."""

from dataclasses import dataclass
from datetime import datetime
from typing import Dict, List, Optional, Any
from src.core.domain.shared.value_objects import AdministrativeLevel
from .value_objects import IPCPhase, AnalysisPeriod, DriverCategory


@dataclass
class IPCClassification:
    """
    IPC (Integrated Food Security Phase Classification) for an area.
    
    Represents the food security situation in a specific geographic area
    during a specific time period.
    """
    area_name: str
    area_code: str
    admin_level: AdministrativeLevel
    analysis_period: AnalysisPeriod
    phase: IPCPhase  # Area phase (1-5)
    phase_population: Dict[int, int]  # Phase -> Population count
    total_population: int
    drivers: List[DriverCategory]
    confidence_level: str  # High, Medium, Low
    metadata: Dict[str, Any]
    
    @property
    def population_in_need(self) -> int:
        """Population in IPC Phase 3 or above (Crisis or worse)."""
        return sum(
            pop for phase, pop in self.phase_population.items()
            if phase >= 3
        )
    
    @property
    def population_in_emergency(self) -> int:
        """Population in IPC Phase 4 or above (Emergency or worse)."""
        return sum(
            pop for phase, pop in self.phase_population.items()
            if phase >= 4
        )
    
    @property
    def percent_in_need(self) -> float:
        """Percentage of population in Phase 3+."""
        if self.total_population == 0:
            return 0.0
        return (self.population_in_need / self.total_population) * 100
    
    @property
    def severity_score(self) -> float:
        """
        Calculate severity score based on population distribution.
        
        Uses exponential weighting to emphasize higher phases.
        """
        if self.total_population == 0:
            return 0.0
        
        weights = {1: 1, 2: 2, 3: 4, 4: 8, 5: 16}
        weighted_sum = sum(
            self.phase_population.get(phase, 0) * weights[phase]
            for phase in range(1, 6)
        )
        
        return weighted_sum / self.total_population
    
    def has_driver(self, driver: DriverCategory) -> bool:
        """Check if classification has specific driver."""
        return driver in self.drivers
    
    def get_phase_name(self) -> str:
        """Get human-readable phase name."""
        phase_names = {
            'Phase1': 'None/Minimal',
            'Phase2': 'Stressed',
            'Phase3': 'Crisis',
            'Phase4': 'Emergency',
            'Phase5': 'Catastrophe/Famine'
        }
        return phase_names.get(self.phase.value, 'Unknown')


@dataclass
class FoodSecurityMetrics:
    """Aggregated food security metrics for a dataset."""
    total_population_analyzed: Optional[int] = None
    population_phase_3_plus: Optional[int] = None
    population_phase_4_plus: Optional[int] = None
    percent_phase_3_plus: Optional[float] = None
    percent_phase_4_plus: Optional[float] = None
    areas_by_phase: Optional[Dict[str, int]] = None
    most_affected_areas: Optional[List[Dict[str, Any]]] = None
    governorate_summary: Optional[Dict[str, Dict[str, float]]] = None
    trend_phase_3_plus_change_pct: Optional[float] = None
    trend_direction: Optional[str] = None
    primary_drivers: Optional[Dict[str, int]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert metrics to dictionary."""
        return {
            k: v for k, v in self.__dict__.items()
            if v is not None
        }