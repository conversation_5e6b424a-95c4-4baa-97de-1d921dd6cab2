"""Food security value objects."""

from enum import Enum
from dataclasses import dataclass
from datetime import datetime
from typing import Optional


class IPCPhase(Enum):
    """IPC acute food insecurity phase classification."""
    Phase1 = "Phase1"  # None/Minimal
    Phase2 = "Phase2"  # Stressed
    Phase3 = "Phase3"  # Crisis
    Phase4 = "Phase4"  # Emergency
    Phase5 = "Phase5"  # Catastrophe/Famine
    
    @property
    def numeric_value(self) -> int:
        """Get numeric phase value (1-5)."""
        return int(self.value[-1])
    
    @property
    def is_crisis_or_worse(self) -> bool:
        """Check if phase is Crisis (3) or worse."""
        return self.numeric_value >= 3
    
    @property
    def is_emergency_or_worse(self) -> bool:
        """Check if phase is Emergency (4) or worse."""
        return self.numeric_value >= 4
    
    def get_color_code(self) -> str:
        """Get standard IPC color code."""
        colors = {
            "Phase1": "#C8E6C9",  # Light green
            "Phase2": "#FFF176",  # Yellow
            "Phase3": "#FF9800",  # Orange
            "Phase4": "#F44336",  # Red
            "Phase5": "#6A1B9A"   # Dark purple
        }
        return colors.get(self.value, "#000000")


class DriverCategory(Enum):
    """Categories of food insecurity drivers."""
    CONFLICT = "conflict"
    ECONOMIC = "economic"
    CLIMATE = "climate"
    HEALTH = "health"
    INFRASTRUCTURE = "infrastructure"
    OTHER = "other"
    
    @classmethod
    def from_string(cls, value: str) -> 'DriverCategory':
        """Create from string, handling common variations."""
        value_lower = value.lower().strip()
        
        # Map common variations
        mapping = {
            'conflict': cls.CONFLICT,
            'war': cls.CONFLICT,
            'violence': cls.CONFLICT,
            'economic': cls.ECONOMIC,
            'economy': cls.ECONOMIC,
            'prices': cls.ECONOMIC,
            'climate': cls.CLIMATE,
            'weather': cls.CLIMATE,
            'drought': cls.CLIMATE,
            'health': cls.HEALTH,
            'disease': cls.HEALTH,
            'infrastructure': cls.INFRASTRUCTURE,
            'roads': cls.INFRASTRUCTURE
        }
        
        for key, category in mapping.items():
            if key in value_lower:
                return category
        
        return cls.OTHER


@dataclass
class AnalysisPeriod:
    """Period covered by food security analysis."""
    start_date: datetime
    end_date: datetime
    is_projection: bool = False
    
    @property
    def duration_days(self) -> int:
        """Get duration in days."""
        return (self.end_date - self.start_date).days
    
    @property
    def duration_months(self) -> float:
        """Get approximate duration in months."""
        return self.duration_days / 30.44
    
    def contains_date(self, date: datetime) -> bool:
        """Check if date falls within period."""
        return self.start_date <= date <= self.end_date
    
    def overlaps_with(self, other: 'AnalysisPeriod') -> bool:
        """Check if periods overlap."""
        return not (self.end_date < other.start_date or 
                   self.start_date > other.end_date)