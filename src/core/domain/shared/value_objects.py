"""Shared value objects for domain modeling."""

from dataclasses import dataclass
from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import Any, Dict, Optional, Union

from .entities import ValueObject
from .exceptions import ValidationException


class AlertLevel(Enum):
    """Alert severity levels."""

    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class Country(Enum):
    """Enumeration of countries for cross-country validation."""
    YEMEN = "yemen"
    SYRIA = "syria"
    LEBANON = "lebanon"
    SOMALIA = "somalia"
    AFGHANISTAN = "afghanistan"


@dataclass(frozen=True)
class ValidationResult:
    """Result of a validation test."""
    test_name: str
    passed: bool
    score: float
    message: str
    details: Optional[Dict[str, Any]] = None


@dataclass(frozen=True)
class Money(ValueObject):
    """Money value object with currency."""

    amount: Decimal
    currency: str = "USD"

    def __post_init__(self) -> None:
        """Validate money value."""
        if self.amount < 0:
            raise ValidationException("Money amount cannot be negative")
        if not self.currency or len(self.currency) != 3:
            raise ValidationException("Currency must be a 3-letter code")

    def __add__(self, other: 'Money') -> 'Money':
        """Add two money values."""
        if self.currency != other.currency:
            raise ValidationException("Cannot add different currencies")
        return Money(self.amount + other.amount, self.currency)

    def __sub__(self, other: 'Money') -> 'Money':
        """Subtract two money values."""
        if self.currency != other.currency:
            raise ValidationException("Cannot subtract different currencies")
        return Money(self.amount - other.amount, self.currency)

    def __mul__(self, multiplier: Union[int, float, Decimal]) -> 'Money':
        """Multiply money by a number."""
        return Money(self.amount * Decimal(str(multiplier)), self.currency)

    def __truediv__(self, divisor: Union[int, float, Decimal]) -> 'Money':
        """Divide money by a number."""
        if divisor == 0:
            raise ValidationException("Cannot divide by zero")
        return Money(self.amount / Decimal(str(divisor)), self.currency)


@dataclass(frozen=True)
class TemporalKey(ValueObject):
    """Temporal key for time-based aggregations."""

    year: int
    month: int
    week: Optional[int] = None
    date: Optional[datetime] = None

    def __post_init__(self) -> None:
        """Validate temporal key."""
        if self.year < 2010 or self.year > 2030:
            raise ValidationException("Year must be between 2010 and 2030")
        if self.month < 1 or self.month > 12:
            raise ValidationException("Month must be between 1 and 12")
        if self.week is not None and (self.week < 1 or self.week > 53):
            raise ValidationException("Week must be between 1 and 53")

    @property
    def period_string(self) -> str:
        """Get string representation of period."""
        if self.week:
            return f"{self.year}-W{self.week:02d}"
        else:
            return f"{self.year}-{self.month:02d}"


@dataclass(frozen=True)
class Percentage(ValueObject):
    """Percentage value object."""

    value: float

    def __post_init__(self) -> None:
        """Validate percentage."""
        if not isinstance(self.value, (int, float)):
            raise ValidationException("Percentage value must be numeric")

    @property
    def as_decimal(self) -> float:
        """Get percentage as decimal (e.g., 50% -> 0.5)."""
        return self.value / 100.0

    @property
    def as_basis_points(self) -> int:
        """Get percentage as basis points (e.g., 1% -> 100 bp)."""
        return int(self.value * 100)

    def __str__(self) -> str:
        """String representation."""
        return f"{self.value}%"


@dataclass(frozen=True)
class Coordinates(ValueObject):
    """Geographic coordinates value object."""

    latitude: float
    longitude: float

    def __post_init__(self) -> None:
        """Validate coordinates."""
        if not (-90 <= self.latitude <= 90):
            raise ValidationException("Latitude must be between -90 and 90")
        if not (-180 <= self.longitude <= 180):
            raise ValidationException("Longitude must be between -180 and 180")

    def distance_to(self, other: 'Coordinates') -> float:
        """Calculate distance to another coordinate in kilometers using Haversine formula."""
        import math

        # Convert to radians
        lat1, lon1 = math.radians(self.latitude), math.radians(self.longitude)
        lat2, lon2 = math.radians(other.latitude), math.radians(other.longitude)

        # Haversine formula
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
        c = 2 * math.asin(math.sqrt(a))

        # Earth's radius in kilometers
        r = 6371
        return r * c


@dataclass(frozen=True)
class EmailAddress(ValueObject):
    """Email address value object."""

    address: str

    def __post_init__(self) -> None:
        """Validate email address."""
        if not self.address or "@" not in self.address:
            raise ValidationException("Invalid email address format")

        parts = self.address.split("@")
        if len(parts) != 2 or not parts[0] or not parts[1]:
            raise ValidationException("Invalid email address format")

    @property
    def domain(self) -> str:
        """Get email domain."""
        return self.address.split("@")[1]

    @property
    def local_part(self) -> str:
        """Get local part of email."""
        return self.address.split("@")[0]
