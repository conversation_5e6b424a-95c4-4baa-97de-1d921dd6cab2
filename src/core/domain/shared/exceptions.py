class YemenMarketException(Exception):
    """Base exception for Yemen Market Integration project."""
    pass

class DomainException(YemenMarketException):
    """Base domain exception."""
    pass

class DataProcessingException(YemenMarketException):
    """Raised when data processing fails."""
    pass

class ValidationException(YemenMarketException):
    """Raised when data validation fails."""
    pass

class BusinessRuleViolation(DomainException):
    """Raised when a business rule is violated."""
    pass

class InvariantViolation(DomainException):
    """Raised when a domain invariant is violated."""
    pass

class ProcessorException(YemenMarketException):
    """Base exception for processor errors."""
    pass

class DataAccessException(YemenMarketException):
    """Raised when data access operations fail."""
    pass