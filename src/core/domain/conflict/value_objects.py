"""Value objects for conflict domain."""
from enum import Enum
from dataclasses import dataclass
from decimal import Decimal


class EventType(Enum):
    """Types of conflict events from ACLED."""
    BATTLES = "Battles"
    EXPLOSIONS_REMOTE_VIOLENCE = "Explosions/Remote violence"
    VIOLENCE_AGAINST_CIVILIANS = "Violence against civilians"
    PROTESTS = "Protests"
    RIOTS = "Riots"
    STRATEGIC_DEVELOPMENTS = "Strategic developments"


class ActorType(Enum):
    """Types of conflict actors."""
    GOVERNMENT = "Government Forces"
    HOUTHIS = "Houthis"
    STC = "Southern Transitional Council"
    AQAP = "Al-Qaeda in the Arabian Peninsula"
    ISIS = "Islamic State"
    COALITION = "Saudi-led Coalition"
    TRIBAL = "Tribal Forces"
    UNKNOWN = "Unknown"

    @classmethod
    def from_acled_actor(cls, actor_name: str) -> 'ActorType':
        """Map ACLED actor names to our types."""
        actor_lower = actor_name.lower()

        if 'houthi' in actor_lower or 'ansar allah' in actor_lower:
            return cls.HOUTHIS
        elif 'government' in actor_lower or 'hadi' in actor_lower:
            return cls.GOVERNMENT
        elif 'stc' in actor_lower or 'southern transitional' in actor_lower:
            return cls.STC
        elif 'aqap' in actor_lower or 'al-qaeda' in actor_lower:
            return cls.AQAP
        elif 'isis' in actor_lower or 'islamic state' in actor_lower:
            return cls.ISIS
        elif 'saudi' in actor_lower or 'coalition' in actor_lower:
            return cls.COALITION
        elif 'tribal' in actor_lower:
            return cls.TRIBAL
        else:
            return cls.UNKNOWN


class IntensityLevel(Enum):
    """Conflict intensity levels."""
    NONE = "none"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    EXTREME = "extreme"


@dataclass(frozen=True)
class ConflictRadius:
    """Radius for conflict impact calculations."""
    inner_km: int = 10   # High impact zone
    middle_km: int = 25  # Medium impact zone
    outer_km: int = 50   # Low impact zone

    def get_weight(self, distance_km: float) -> Decimal:
        """Get impact weight based on distance."""
        if distance_km <= self.inner_km:
            return Decimal("1.0")
        elif distance_km <= self.middle_km:
            return Decimal("0.5")
        elif distance_km <= self.outer_km:
            return Decimal("0.25")
        else:
            return Decimal("0.0")


# Aliases for backward compatibility
ConflictIntensity = IntensityLevel
ConflictType = EventType
ImpactRadius = ConflictRadius
