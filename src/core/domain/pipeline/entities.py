"""Pipeline domain entities."""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, List, Optional, Set
from uuid import UUID, uuid4

from .value_objects import (
    PipelineState,
    ProcessorStatus,
    ResourceMetrics,
    ExecutionMetrics,
    DependencyType
)


@dataclass
class ProcessorDependency:
    """Represents a dependency between processors."""
    
    processor_id: str
    dependency_type: DependencyType
    required: bool = True
    
    def __hash__(self):
        return hash((self.processor_id, self.dependency_type))


@dataclass
class ProcessorNode:
    """Node in the processor dependency graph."""
    
    processor_id: str
    processor_type: str
    dependencies: Set[ProcessorDependency] = field(default_factory=set)
    status: ProcessorStatus = ProcessorStatus.PENDING
    metrics: Optional[ExecutionMetrics] = None
    retry_count: int = 0
    max_retries: int = 3
    
    def add_dependency(self, processor_id: str, dependency_type: DependencyType = DependencyType.REQUIRES):
        """Add a dependency to this processor."""
        self.dependencies.add(ProcessorDependency(processor_id, dependency_type))
    
    def can_run(self, completed_processors: Set[str]) -> bool:
        """Check if this processor can run based on dependencies."""
        for dep in self.dependencies:
            if dep.required and dep.dependency_type == DependencyType.REQUIRES:
                if dep.processor_id not in completed_processors:
                    return False
        return True
    
    def should_retry(self) -> bool:
        """Check if processor should be retried."""
        return self.status == ProcessorStatus.FAILED and self.retry_count < self.max_retries


@dataclass
class PipelineCheckpoint:
    """Checkpoint for pipeline resume functionality."""
    
    checkpoint_id: UUID = field(default_factory=uuid4)
    pipeline_id: UUID = None
    state: PipelineState = None
    timestamp: datetime = field(default_factory=datetime.utcnow)
    completed_processors: Set[str] = field(default_factory=set)
    processor_states: Dict[str, ProcessorStatus] = field(default_factory=dict)
    data_snapshots: Dict[str, str] = field(default_factory=dict)  # processor_id -> file_path
    metrics: Optional[ExecutionMetrics] = None
    metadata: Dict[str, any] = field(default_factory=dict)
    
    def is_valid(self) -> bool:
        """Check if checkpoint is valid for resume."""
        # Check all required data exists
        for processor_id, file_path in self.data_snapshots.items():
            from pathlib import Path
            if not Path(file_path).exists():
                return False
        return True


@dataclass
class PipelineExecution:
    """Represents a single pipeline execution."""
    
    execution_id: UUID = field(default_factory=uuid4)
    pipeline_id: UUID = None
    state: PipelineState = PipelineState.PENDING
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    # Processor tracking
    processor_graph: Dict[str, ProcessorNode] = field(default_factory=dict)
    execution_order: List[List[str]] = field(default_factory=list)  # Parallel groups
    current_group_index: int = 0
    
    # Metrics
    resource_metrics: List[ResourceMetrics] = field(default_factory=list)
    execution_metrics: Optional[ExecutionMetrics] = None
    
    # Checkpoints
    checkpoints: List[PipelineCheckpoint] = field(default_factory=list)
    last_checkpoint: Optional[PipelineCheckpoint] = None
    
    # Error tracking
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    
    def transition_to(self, new_state: PipelineState) -> bool:
        """Transition to a new state if valid."""
        if self.state.can_transition_to(new_state):
            self.state = new_state
            if new_state == PipelineState.COMPLETED:
                self.completed_at = datetime.utcnow()
            return True
        return False
    
    def add_processor(self, processor_node: ProcessorNode):
        """Add a processor to the execution graph."""
        self.processor_graph[processor_node.processor_id] = processor_node
    
    def get_runnable_processors(self) -> List[ProcessorNode]:
        """Get processors that can currently run."""
        completed = {
            proc_id for proc_id, node in self.processor_graph.items()
            if node.status == ProcessorStatus.COMPLETED
        }
        
        runnable = []
        for proc_id, node in self.processor_graph.items():
            if node.status == ProcessorStatus.PENDING and node.can_run(completed):
                runnable.append(node)
        
        return runnable
    
    def create_checkpoint(self) -> PipelineCheckpoint:
        """Create a checkpoint of current execution state."""
        checkpoint = PipelineCheckpoint(
            pipeline_id=self.pipeline_id,
            state=self.state,
            completed_processors={
                proc_id for proc_id, node in self.processor_graph.items()
                if node.status == ProcessorStatus.COMPLETED
            },
            processor_states={
                proc_id: node.status
                for proc_id, node in self.processor_graph.items()
            },
            metrics=self.execution_metrics
        )
        self.checkpoints.append(checkpoint)
        self.last_checkpoint = checkpoint
        return checkpoint
    
    def restore_from_checkpoint(self, checkpoint: PipelineCheckpoint):
        """Restore execution state from checkpoint."""
        if not checkpoint.is_valid():
            raise ValueError("Invalid checkpoint")
        
        self.state = checkpoint.state
        for proc_id, status in checkpoint.processor_states.items():
            if proc_id in self.processor_graph:
                self.processor_graph[proc_id].status = status
        
        self.last_checkpoint = checkpoint


@dataclass
class Pipeline:
    """Pipeline configuration and metadata."""
    
    pipeline_id: UUID = field(default_factory=uuid4)
    name: str = ""
    description: str = ""
    version: str = "1.0.0"
    
    # Configuration
    processors: List[str] = field(default_factory=list)
    dependencies: Dict[str, List[str]] = field(default_factory=dict)
    
    # Settings
    enable_checkpoints: bool = True
    checkpoint_interval_seconds: int = 300  # 5 minutes
    enable_resource_monitoring: bool = True
    resource_check_interval_seconds: int = 30
    max_parallel_processors: int = 4
    
    # Thresholds
    max_memory_mb: float = 8192  # 8GB
    max_cpu_percent: float = 90
    health_check_timeout_seconds: int = 60
    
    def create_execution(self) -> PipelineExecution:
        """Create a new execution instance."""
        execution = PipelineExecution(pipeline_id=self.pipeline_id)
        
        # Build processor graph
        for processor_id in self.processors:
            node = ProcessorNode(
                processor_id=processor_id,
                processor_type=processor_id.split("_")[0]  # Extract type from ID
            )
            
            # Add dependencies
            if processor_id in self.dependencies:
                for dep_id in self.dependencies[processor_id]:
                    node.add_dependency(dep_id)
            
            execution.add_processor(node)
        
        # Calculate execution order (topological sort)
        execution.execution_order = self._calculate_execution_order(execution.processor_graph)
        
        return execution
    
    def _calculate_execution_order(self, processor_graph: Dict[str, ProcessorNode]) -> List[List[str]]:
        """Calculate parallel execution groups using topological sort."""
        # Implementation of Kahn's algorithm for topological sorting with levels
        in_degree = {proc_id: 0 for proc_id in processor_graph}
        
        # Calculate in-degrees
        for proc_id, node in processor_graph.items():
            for dep in node.dependencies:
                if dep.dependency_type == DependencyType.REQUIRES:
                    in_degree[dep.processor_id] = in_degree.get(dep.processor_id, 0) + 1
        
        # Find nodes with no dependencies
        queue = [proc_id for proc_id, degree in in_degree.items() if degree == 0]
        execution_order = []
        
        while queue:
            # Process all nodes at current level in parallel
            current_level = queue[:]
            execution_order.append(current_level)
            queue = []
            
            # Remove processed nodes and update in-degrees
            for proc_id in current_level:
                node = processor_graph[proc_id]
                for dep in node.dependencies:
                    if dep.dependency_type == DependencyType.REQUIRES:
                        in_degree[dep.processor_id] -= 1
                        if in_degree[dep.processor_id] == 0:
                            queue.append(dep.processor_id)
        
        return execution_order