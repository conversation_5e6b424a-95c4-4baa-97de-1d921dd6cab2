"""Value objects for pipeline domain."""

from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import Dict, Optional


class PipelineState(Enum):
    """States in the pipeline state machine."""
    
    PENDING = "pending"
    INITIALIZING = "initializing"
    COLLECTING = "collecting"
    PROCESSING = "processing"
    INTEGRATING = "integrating"
    VALIDATING = "validating"
    BUILDING = "building"
    COMPLETED = "completed"
    FAILED = "failed"
    PAUSED = "paused"
    RESUMING = "resuming"
    CANCELLED = "cancelled"
    
    @property
    def is_terminal(self) -> bool:
        """Check if state is terminal (no further transitions)."""
        return self in {self.COMPLETED, self.FAILED, self.CANCELLED}
    
    @property
    def is_active(self) -> bool:
        """Check if state represents active processing."""
        return self in {
            self.INITIALIZING, self.COLLECTING, self.PROCESSING,
            self.INTEGRATING, self.VALIDATING, self.BUILDING, self.RESUMING
        }
    
    def can_transition_to(self, target: "PipelineState") -> bool:
        """Check if transition to target state is valid."""
        valid_transitions = {
            self.PENDING: {self.INITIALIZING, self.CANCELLED},
            self.INITIALIZING: {self.COLLECTING, self.FAILED, self.PAUSED},
            self.COLLECTING: {self.PROCESSING, self.FAILED, self.PAUSED},
            self.PROCESSING: {self.INTEGRATING, self.FAILED, self.PAUSED},
            self.INTEGRATING: {self.VALIDATING, self.FAILED, self.PAUSED},
            self.VALIDATING: {self.BUILDING, self.FAILED, self.PAUSED},
            self.BUILDING: {self.COMPLETED, self.FAILED, self.PAUSED},
            self.PAUSED: {self.RESUMING, self.CANCELLED},
            self.RESUMING: {self.COLLECTING, self.PROCESSING, self.INTEGRATING, 
                           self.VALIDATING, self.BUILDING, self.FAILED},
            self.FAILED: {self.RESUMING},
            self.COMPLETED: set(),
            self.CANCELLED: set()
        }
        return target in valid_transitions.get(self, set())


class ProcessorStatus(Enum):
    """Status of individual processor execution."""
    
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"
    RETRYING = "retrying"


class DependencyType(Enum):
    """Types of dependencies between processors."""
    
    REQUIRES = "requires"  # Must complete before starting
    OPTIONAL = "optional"  # Nice to have but not required
    PARALLEL = "parallel"  # Can run in parallel
    EXCLUSIVE = "exclusive"  # Cannot run at same time


@dataclass(frozen=True)
class ResourceMetrics:
    """Resource usage metrics."""
    
    cpu_percent: float
    memory_mb: float
    memory_percent: float
    disk_io_mb: float
    network_io_mb: float
    timestamp: datetime
    
    @property
    def is_high_load(self) -> bool:
        """Check if resource usage is high."""
        return self.cpu_percent > 80 or self.memory_percent > 85
    
    def to_prometheus_format(self) -> Dict[str, float]:
        """Convert to Prometheus metrics format."""
        return {
            "pipeline_cpu_usage_percent": self.cpu_percent,
            "pipeline_memory_usage_mb": self.memory_mb,
            "pipeline_memory_usage_percent": self.memory_percent,
            "pipeline_disk_io_mb": self.disk_io_mb,
            "pipeline_network_io_mb": self.network_io_mb
        }


@dataclass(frozen=True)
class ExecutionMetrics:
    """Execution metrics for a pipeline or processor."""
    
    started_at: datetime
    completed_at: Optional[datetime]
    duration_seconds: Optional[float]
    records_processed: int
    records_failed: int
    error_count: int
    warning_count: int
    retry_count: int
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate."""
        total = self.records_processed + self.records_failed
        if total == 0:
            return 0.0
        return self.records_processed / total
    
    @property
    def is_successful(self) -> bool:
        """Check if execution was successful."""
        return self.error_count == 0 and self.records_failed == 0
    
    def to_dict(self) -> Dict[str, any]:
        """Convert to dictionary for serialization."""
        return {
            "started_at": self.started_at.isoformat(),
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "duration_seconds": self.duration_seconds,
            "records_processed": self.records_processed,
            "records_failed": self.records_failed,
            "error_count": self.error_count,
            "warning_count": self.warning_count,
            "retry_count": self.retry_count,
            "success_rate": self.success_rate
        }