from dataclasses import dataclass
from datetime import datetime
from typing import List, Optional, Dict, Any
from .value_objects import DisplacementType, PopulationGroup

@dataclass
class PopulationObservation:
    location_id: str
    timestamp: datetime
    population_total: int
    population_density: float
    data_source: str
    confidence_level: float
    demographic_breakdown: Dict[str, Any]
    location_type: str
    coordinates: Dict[str, float]

@dataclass
class DisplacementFlow:
    flow_id: str
    timestamp: datetime
    origin_location: str
    destination_location: str
    displaced_individuals: int
    displaced_households: int
    displacement_type: DisplacementType
    population_groups: List[PopulationGroup]
    duration_days: Optional[int]
    return_intention: str
    assistance_needed: List[str]
    origin_coordinates: Optional[Dict[str, float]] = None
    destination_coordinates: Optional[Dict[str, float]] = None

@dataclass
class PopulationMetrics:
    total_population: int
    total_displaced: int
    population_density_avg: float
    urban_population_pct: float
    displacement_rate: float
    coverage_pct: float
    total_idp_sites: Optional[int] = None
    avg_household_size: Optional[float] = None
    total_displaced_households: Optional[int] = None
    displacement_by_type: Optional[Dict[str, int]] = None