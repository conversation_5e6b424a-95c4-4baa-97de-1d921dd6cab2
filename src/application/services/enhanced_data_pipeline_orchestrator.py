"""Enhanced data pipeline orchestrator with state machine and monitoring.

This enhanced version adds:
- State machine for pipeline execution tracking
- Checkpoint/resume functionality  
- Dependency graph for processor execution
- Resource monitoring and health checks
- Prometheus metrics export
"""

import asyncio
import json
from dataclasses import dataclass
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple, Callable
from uuid import UUID

from ...core.domain.pipeline.entities import (
    Pipeline, PipelineExecution, PipelineCheckpoint, ProcessorNode
)
from ...core.domain.pipeline.value_objects import (
    PipelineState, ProcessorStatus, ExecutionMetrics, DependencyType
)
from ...core.domain.shared.exceptions import DataPipelineException
from ...core.utils.logging import get_logger
from ...infrastructure.monitoring import (
    MetricsCollector, get_metrics_collector,
    ResourceMonitor, HealthChecker, AlertManager,
    PrometheusExporter, AlertLevel
)
from ..commands.run_three_tier_analysis import RunThreeTierAnalysisCommand
from .data_ingestion_service import DataIngestionService, IngestionResult
from .ingestion_orchestrator import IngestionOrchestrator, IngestionPriority
from .panel_builder_service import PanelBuilderService
from .data_pipeline_orchestrator import PipelineConfig, PipelineStatus


logger = get_logger(__name__)


class EnhancedDataPipelineOrchestrator:
    """Enhanced orchestrator with state machine and monitoring capabilities."""
    
    def __init__(
        self,
        ingestion_service: DataIngestionService,
        ingestion_orchestrator: IngestionOrchestrator,
        panel_builder_service: PanelBuilderService,
        checkpoint_dir: Optional[Path] = None,
        enable_monitoring: bool = True,
        enable_checkpoints: bool = True
    ):
        """Initialize enhanced orchestrator.
        
        Args:
            ingestion_service: Service for data ingestion
            ingestion_orchestrator: Orchestrator for ingestion jobs
            panel_builder_service: Service for panel building
            checkpoint_dir: Directory for saving checkpoints
            enable_monitoring: Enable resource monitoring and health checks
            enable_checkpoints: Enable checkpoint/resume functionality
        """
        self.ingestion_service = ingestion_service
        self.ingestion_orchestrator = ingestion_orchestrator
        self.panel_builder_service = panel_builder_service
        self.logger = logger
        
        # Configuration
        self.checkpoint_dir = checkpoint_dir or Path("data/checkpoints")
        self.checkpoint_dir.mkdir(parents=True, exist_ok=True)
        self.enable_monitoring = enable_monitoring
        self.enable_checkpoints = enable_checkpoints
        
        # Pipeline tracking
        self._pipelines: Dict[UUID, Pipeline] = {}
        self._executions: Dict[UUID, PipelineExecution] = {}
        
        # Monitoring components
        self.metrics_collector = get_metrics_collector()
        self.resource_monitor = ResourceMonitor() if enable_monitoring else None
        self.health_checker = HealthChecker() if enable_monitoring else None
        self.alert_manager = AlertManager() if enable_monitoring else None
        self.prometheus_exporter = PrometheusExporter(self.metrics_collector)
        
        # Setup alert handlers
        if self.alert_manager:
            self._setup_alert_handlers()
        
        # Processor registry
        self._processor_handlers: Dict[str, Callable] = {
            "wfp_collection": self._process_wfp_collection,
            "acled_collection": self._process_acled_collection,
            "acaps_collection": self._process_acaps_collection,
            "wfp_processing": self._process_wfp_data,
            "acled_processing": self._process_acled_data,
            "acaps_processing": self._process_acaps_data,
            "spatial_integration": self._process_spatial_integration,
            "panel_building": self._process_panel_building,
            "validation": self._process_validation
        }
    
    def _setup_alert_handlers(self):
        """Setup alert handlers for different alert levels."""
        # Critical alerts - log and potentially stop pipeline
        self.alert_manager.add_handler(
            AlertLevel.CRITICAL,
            self._handle_critical_alert
        )
        
        # Error alerts - log and record
        self.alert_manager.add_handler(
            AlertLevel.ERROR,
            self._handle_error_alert
        )
    
    async def _handle_critical_alert(self, alert):
        """Handle critical alerts."""
        self.logger.critical(f"CRITICAL ALERT: {alert.message}")
        # Could implement pipeline pausing here
    
    async def _handle_error_alert(self, alert):
        """Handle error alerts."""
        self.logger.error(f"ERROR ALERT: {alert.message}")
    
    async def initialize(self):
        """Initialize monitoring components."""
        if self.enable_monitoring:
            # Start resource monitoring
            self.resource_monitor.start_monitoring()
            
            # Add resource callback
            self.resource_monitor.add_callback(self._on_resource_update)
            
            # Start health checker
            await self.health_checker.start()
            
            # Start alert manager
            await self.alert_manager.start()
            
            # Register pipeline-specific health checks
            self._register_pipeline_health_checks()
            
        self.logger.info("Enhanced pipeline orchestrator initialized")
    
    async def shutdown(self):
        """Shutdown monitoring components."""
        if self.enable_monitoring:
            self.resource_monitor.stop_monitoring()
            await self.health_checker.stop()
            await self.alert_manager.stop()
        
        self.logger.info("Enhanced pipeline orchestrator shutdown")
    
    def _register_pipeline_health_checks(self):
        """Register pipeline-specific health checks."""
        # Check for stalled pipelines
        self.health_checker.register_component(
            "pipeline_progress",
            self._check_pipeline_progress,
            timeout_seconds=10
        )
        
        # Check checkpoint validity
        self.health_checker.register_component(
            "checkpoints",
            self._check_checkpoints,
            timeout_seconds=5
        )
    
    async def _check_pipeline_progress(self):
        """Check if any pipelines are stalled."""
        from ...infrastructure.monitoring import HealthCheckResult, HealthStatus
        
        stalled_pipelines = []
        for exec_id, execution in self._executions.items():
            if execution.state.is_active:
                # Check if no progress in last 10 minutes
                last_progress = getattr(execution, 'last_progress_time', None)
                if last_progress:
                    if (datetime.utcnow() - last_progress) > timedelta(minutes=10):
                        stalled_pipelines.append(str(exec_id))
        
        if stalled_pipelines:
            return HealthCheckResult(
                component="pipeline_progress",
                status=HealthStatus.DEGRADED,
                message=f"Pipelines stalled: {', '.join(stalled_pipelines)}",
                details={"stalled_pipelines": stalled_pipelines}
            )
        
        return HealthCheckResult(
            component="pipeline_progress",
            status=HealthStatus.HEALTHY,
            message="All pipelines progressing normally"
        )
    
    async def _check_checkpoints(self):
        """Check checkpoint directory health."""
        from ...infrastructure.monitoring import HealthCheckResult, HealthStatus
        
        if not self.checkpoint_dir.exists():
            return HealthCheckResult(
                component="checkpoints",
                status=HealthStatus.UNHEALTHY,
                message="Checkpoint directory missing"
            )
        
        # Check disk space for checkpoints
        import shutil
        usage = shutil.disk_usage(str(self.checkpoint_dir))
        free_gb = usage.free / (1024 ** 3)
        
        if free_gb < 1:
            return HealthCheckResult(
                component="checkpoints",
                status=HealthStatus.UNHEALTHY,
                message=f"Low disk space for checkpoints: {free_gb:.1f}GB"
            )
        
        return HealthCheckResult(
            component="checkpoints",
            status=HealthStatus.HEALTHY,
            message="Checkpoint system healthy"
        )
    
    def _on_resource_update(self, metrics):
        """Callback for resource metric updates."""
        # Update execution metrics if high load
        if metrics.is_high_load:
            for execution in self._executions.values():
                if execution.state.is_active:
                    execution.resource_metrics.append(metrics)
    
    def create_pipeline(
        self,
        name: str,
        description: str = "",
        processors: Optional[List[str]] = None,
        dependencies: Optional[Dict[str, List[str]]] = None
    ) -> Pipeline:
        """Create a pipeline configuration with dependency graph."""
        if processors is None:
            # Default pipeline processors
            processors = [
                "wfp_collection", "acled_collection", "acaps_collection",
                "wfp_processing", "acled_processing", "acaps_processing",
                "spatial_integration", "panel_building", "validation"
            ]
        
        if dependencies is None:
            # Default dependencies
            dependencies = {
                "wfp_processing": ["wfp_collection"],
                "acled_processing": ["acled_collection"],
                "acaps_processing": ["acaps_collection"],
                "spatial_integration": ["wfp_processing", "acaps_processing"],
                "panel_building": ["spatial_integration", "acled_processing"],
                "validation": ["panel_building"]
            }
        
        pipeline = Pipeline(
            name=name,
            description=description,
            processors=processors,
            dependencies=dependencies,
            enable_checkpoints=self.enable_checkpoints,
            enable_resource_monitoring=self.enable_monitoring
        )
        
        self._pipelines[pipeline.pipeline_id] = pipeline
        return pipeline
    
    async def execute_pipeline(
        self,
        pipeline: Pipeline,
        config: PipelineConfig,
        resume_from_checkpoint: Optional[UUID] = None,
        progress_callback: Optional[Callable] = None
    ) -> PipelineExecution:
        """Execute a pipeline with state machine and monitoring.
        
        Args:
            pipeline: Pipeline configuration
            config: Pipeline execution config
            resume_from_checkpoint: Checkpoint ID to resume from
            progress_callback: Progress callback function
            
        Returns:
            PipelineExecution with results
        """
        # Create or restore execution
        if resume_from_checkpoint:
            execution = await self._restore_execution(resume_from_checkpoint)
        else:
            execution = pipeline.create_execution()
            execution.started_at = datetime.utcnow()
        
        self._executions[execution.execution_id] = execution
        
        # Update metrics
        self.metrics_collector.increment_counter("pipeline_executions_total")
        self.metrics_collector.set_gauge(
            "pipeline_active_executions",
            len([e for e in self._executions.values() if e.state.is_active])
        )
        
        try:
            # Execute state machine
            await self._execute_state_machine(execution, config, progress_callback)
            
            # Mark successful completion
            execution.completed_at = datetime.utcnow()
            execution.execution_metrics = ExecutionMetrics(
                started_at=execution.started_at,
                completed_at=execution.completed_at,
                duration_seconds=(execution.completed_at - execution.started_at).total_seconds(),
                records_processed=sum(
                    node.metrics.records_processed if node.metrics else 0
                    for node in execution.processor_graph.values()
                ),
                records_failed=sum(
                    node.metrics.records_failed if node.metrics else 0
                    for node in execution.processor_graph.values()
                ),
                error_count=len(execution.errors),
                warning_count=len(execution.warnings),
                retry_count=sum(
                    node.retry_count
                    for node in execution.processor_graph.values()
                )
            )
            
            self.logger.info(f"Pipeline execution {execution.execution_id} completed successfully")
            
        except Exception as e:
            execution.errors.append(str(e))
            execution.transition_to(PipelineState.FAILED)
            self.logger.error(f"Pipeline execution failed: {e}", exc_info=True)
            raise
            
        finally:
            # Update metrics
            self.metrics_collector.set_gauge(
                "pipeline_active_executions",
                len([e for e in self._executions.values() if e.state.is_active])
            )
            
            # Clean up if not keeping for history
            if execution.execution_id in self._executions:
                del self._executions[execution.execution_id]
        
        return execution
    
    async def _execute_state_machine(
        self,
        execution: PipelineExecution,
        config: PipelineConfig,
        progress_callback: Optional[Callable]
    ):
        """Execute the pipeline state machine."""
        # State machine transitions
        state_handlers = {
            PipelineState.PENDING: self._handle_pending_state,
            PipelineState.INITIALIZING: self._handle_initializing_state,
            PipelineState.COLLECTING: self._handle_collecting_state,
            PipelineState.PROCESSING: self._handle_processing_state,
            PipelineState.INTEGRATING: self._handle_integrating_state,
            PipelineState.VALIDATING: self._handle_validating_state,
            PipelineState.BUILDING: self._handle_building_state,
            PipelineState.RESUMING: self._handle_resuming_state
        }
        
        # Execute state machine
        while not execution.state.is_terminal:
            handler = state_handlers.get(execution.state)
            if not handler:
                raise DataPipelineException(f"No handler for state {execution.state}")
            
            try:
                # Execute state handler
                await handler(execution, config, progress_callback)
                
                # Create checkpoint after each major state
                if self.enable_checkpoints and execution.state in {
                    PipelineState.COLLECTING, PipelineState.PROCESSING,
                    PipelineState.INTEGRATING, PipelineState.BUILDING
                }:
                    await self._create_checkpoint(execution)
                    
            except Exception as e:
                self.logger.error(f"Error in state {execution.state}: {e}")
                execution.errors.append(str(e))
                
                # Check retry logic
                if execution.state != PipelineState.RESUMING:
                    # Attempt to transition to resuming
                    if execution.transition_to(PipelineState.RESUMING):
                        continue
                
                # Otherwise fail
                execution.transition_to(PipelineState.FAILED)
                raise
    
    async def _handle_pending_state(
        self,
        execution: PipelineExecution,
        config: PipelineConfig,
        progress_callback: Optional[Callable]
    ):
        """Handle pending state - initialize pipeline."""
        self.logger.info("Pipeline starting initialization")
        
        # Transition to initializing
        execution.transition_to(PipelineState.INITIALIZING)
    
    async def _handle_initializing_state(
        self,
        execution: PipelineExecution,
        config: PipelineConfig,
        progress_callback: Optional[Callable]
    ):
        """Handle initializing state - prepare resources."""
        self.logger.info("Initializing pipeline resources")
        
        # Check resource availability
        if self.resource_monitor:
            available = await self.resource_monitor.wait_for_resources(
                max_cpu_percent=80,
                max_memory_mb=6144,
                timeout_seconds=300
            )
            if not available:
                raise DataPipelineException("Insufficient resources for pipeline")
        
        # Initialize processor states
        for processor in execution.processor_graph.values():
            processor.status = ProcessorStatus.PENDING
        
        # Transition to collecting
        execution.transition_to(PipelineState.COLLECTING)
    
    async def _handle_collecting_state(
        self,
        execution: PipelineExecution,
        config: PipelineConfig,
        progress_callback: Optional[Callable]
    ):
        """Handle collecting state - run collection processors."""
        self.logger.info("Starting data collection phase")
        
        # Get collection processors
        collection_processors = [
            p for p_id, p in execution.processor_graph.items()
            if "collection" in p_id
        ]
        
        # Run collection processors
        await self._run_processor_group(
            execution, collection_processors, config, progress_callback
        )
        
        # Transition to processing
        execution.transition_to(PipelineState.PROCESSING)
    
    async def _handle_processing_state(
        self,
        execution: PipelineExecution,
        config: PipelineConfig,
        progress_callback: Optional[Callable]
    ):
        """Handle processing state - run processing processors."""
        self.logger.info("Starting data processing phase")
        
        # Get processing processors
        processing_processors = [
            p for p_id, p in execution.processor_graph.items()
            if "processing" in p_id and "collection" not in p_id
        ]
        
        # Run processing processors
        await self._run_processor_group(
            execution, processing_processors, config, progress_callback
        )
        
        # Transition to integrating
        execution.transition_to(PipelineState.INTEGRATING)
    
    async def _handle_integrating_state(
        self,
        execution: PipelineExecution,
        config: PipelineConfig,
        progress_callback: Optional[Callable]
    ):
        """Handle integrating state - run integration processors."""
        self.logger.info("Starting data integration phase")
        
        # Get integration processors
        integration_processors = [
            p for p_id, p in execution.processor_graph.items()
            if "integration" in p_id or "spatial" in p_id
        ]
        
        # Run integration processors
        await self._run_processor_group(
            execution, integration_processors, config, progress_callback
        )
        
        # Transition to validating
        execution.transition_to(PipelineState.VALIDATING)
    
    async def _handle_validating_state(
        self,
        execution: PipelineExecution,
        config: PipelineConfig,
        progress_callback: Optional[Callable]
    ):
        """Handle validating state - run validation."""
        self.logger.info("Starting data validation phase")
        
        # Get validation processors
        validation_processors = [
            p for p_id, p in execution.processor_graph.items()
            if "validation" in p_id
        ]
        
        # Run validation processors
        await self._run_processor_group(
            execution, validation_processors, config, progress_callback
        )
        
        # Transition to building
        execution.transition_to(PipelineState.BUILDING)
    
    async def _handle_building_state(
        self,
        execution: PipelineExecution,
        config: PipelineConfig,
        progress_callback: Optional[Callable]
    ):
        """Handle building state - build final panel."""
        self.logger.info("Starting panel building phase")
        
        # Get building processors
        building_processors = [
            p for p_id, p in execution.processor_graph.items()
            if "building" in p_id or "panel" in p_id
        ]
        
        # Run building processors
        await self._run_processor_group(
            execution, building_processors, config, progress_callback
        )
        
        # Transition to completed
        execution.transition_to(PipelineState.COMPLETED)
    
    async def _handle_resuming_state(
        self,
        execution: PipelineExecution,
        config: PipelineConfig,
        progress_callback: Optional[Callable]
    ):
        """Handle resuming state - resume from checkpoint."""
        self.logger.info("Resuming pipeline from checkpoint")
        
        if not execution.last_checkpoint:
            raise DataPipelineException("No checkpoint available for resume")
        
        # Restore state from checkpoint
        execution.restore_from_checkpoint(execution.last_checkpoint)
        
        # Determine next state based on completed processors
        completed_count = sum(
            1 for p in execution.processor_graph.values()
            if p.status == ProcessorStatus.COMPLETED
        )
        total_count = len(execution.processor_graph)
        
        if completed_count == 0:
            next_state = PipelineState.COLLECTING
        elif completed_count < total_count * 0.3:
            next_state = PipelineState.COLLECTING
        elif completed_count < total_count * 0.6:
            next_state = PipelineState.PROCESSING
        elif completed_count < total_count * 0.8:
            next_state = PipelineState.INTEGRATING
        elif completed_count < total_count * 0.9:
            next_state = PipelineState.VALIDATING
        else:
            next_state = PipelineState.BUILDING
        
        execution.transition_to(next_state)
    
    async def _run_processor_group(
        self,
        execution: PipelineExecution,
        processors: List[ProcessorNode],
        config: PipelineConfig,
        progress_callback: Optional[Callable]
    ):
        """Run a group of processors respecting dependencies."""
        # Get runnable processors
        runnable = [p for p in processors if p.can_run(
            {p_id for p_id, node in execution.processor_graph.items()
             if node.status == ProcessorStatus.COMPLETED}
        )]
        
        # Run in parallel up to max limit
        pipeline = self._pipelines.get(execution.pipeline_id)
        max_parallel = pipeline.max_parallel_processors if pipeline else 4
        
        while runnable:
            # Take batch up to parallel limit
            batch = runnable[:max_parallel]
            runnable = runnable[max_parallel:]
            
            # Run batch in parallel
            tasks = []
            for processor in batch:
                task = asyncio.create_task(
                    self._run_processor(processor, execution, config)
                )
                tasks.append((processor, task))
            
            # Wait for batch completion
            for processor, task in tasks:
                try:
                    await task
                    processor.status = ProcessorStatus.COMPLETED
                except Exception as e:
                    processor.status = ProcessorStatus.FAILED
                    execution.errors.append(f"{processor.processor_id}: {str(e)}")
                    
                    # Check retry
                    if processor.should_retry():
                        processor.retry_count += 1
                        processor.status = ProcessorStatus.RETRYING
                        runnable.append(processor)
            
            # Update progress
            if progress_callback:
                await progress_callback(self._create_status_from_execution(execution))
            
            # Update runnable list with newly runnable processors
            newly_runnable = [
                p for p in processors
                if p.status == ProcessorStatus.PENDING and p.can_run(
                    {p_id for p_id, node in execution.processor_graph.items()
                     if node.status == ProcessorStatus.COMPLETED}
                )
            ]
            runnable.extend(newly_runnable)
    
    async def _run_processor(
        self,
        processor: ProcessorNode,
        execution: PipelineExecution,
        config: PipelineConfig
    ):
        """Run a single processor."""
        processor.status = ProcessorStatus.RUNNING
        start_time = datetime.utcnow()
        
        # Get handler
        handler = self._processor_handlers.get(processor.processor_id)
        if not handler:
            raise DataPipelineException(f"No handler for processor {processor.processor_id}")
        
        # Update metrics
        self.metrics_collector.increment_counter(
            "pipeline_processor_runs_total",
            labels={"processor": processor.processor_id}
        )
        
        try:
            # Run with timing
            with self.metrics_collector.timer(
                "pipeline_processing_duration_seconds",
                labels={"processor": processor.processor_id}
            ):
                result = await handler(config)
            
            # Update processor metrics
            processor.metrics = ExecutionMetrics(
                started_at=start_time,
                completed_at=datetime.utcnow(),
                duration_seconds=(datetime.utcnow() - start_time).total_seconds(),
                records_processed=result.get("records_processed", 0),
                records_failed=0,
                error_count=0,
                warning_count=0,
                retry_count=processor.retry_count
            )
            
        except Exception as e:
            # Update failure metrics
            processor.metrics = ExecutionMetrics(
                started_at=start_time,
                completed_at=datetime.utcnow(),
                duration_seconds=(datetime.utcnow() - start_time).total_seconds(),
                records_processed=0,
                records_failed=0,
                error_count=1,
                warning_count=0,
                retry_count=processor.retry_count
            )
            
            self.metrics_collector.increment_counter(
                "pipeline_processor_failures_total",
                labels={"processor": processor.processor_id}
            )
            
            raise
    
    async def _create_checkpoint(self, execution: PipelineExecution):
        """Create a checkpoint for the execution."""
        checkpoint = execution.create_checkpoint()
        
        # Save checkpoint data
        checkpoint_file = self.checkpoint_dir / f"checkpoint_{checkpoint.checkpoint_id}.json"
        
        checkpoint_data = {
            "checkpoint_id": str(checkpoint.checkpoint_id),
            "pipeline_id": str(checkpoint.pipeline_id),
            "state": checkpoint.state.value,
            "timestamp": checkpoint.timestamp.isoformat(),
            "completed_processors": list(checkpoint.completed_processors),
            "processor_states": {
                k: v.value for k, v in checkpoint.processor_states.items()
            },
            "data_snapshots": checkpoint.data_snapshots,
            "metadata": checkpoint.metadata
        }
        
        with open(checkpoint_file, 'w') as f:
            json.dump(checkpoint_data, f, indent=2)
        
        self.logger.info(f"Created checkpoint {checkpoint.checkpoint_id}")
        
        # Update metrics
        self.metrics_collector.increment_counter(
            "pipeline_checkpoint_saves_total",
            labels={"pipeline_id": str(execution.pipeline_id), "status": "success"}
        )
    
    async def _restore_execution(self, checkpoint_id: UUID) -> PipelineExecution:
        """Restore execution from checkpoint."""
        checkpoint_file = self.checkpoint_dir / f"checkpoint_{checkpoint_id}.json"
        
        if not checkpoint_file.exists():
            raise DataPipelineException(f"Checkpoint {checkpoint_id} not found")
        
        with open(checkpoint_file, 'r') as f:
            checkpoint_data = json.load(f)
        
        # Recreate checkpoint object
        checkpoint = PipelineCheckpoint(
            checkpoint_id=UUID(checkpoint_data["checkpoint_id"]),
            pipeline_id=UUID(checkpoint_data["pipeline_id"]),
            state=PipelineState(checkpoint_data["state"]),
            timestamp=datetime.fromisoformat(checkpoint_data["timestamp"]),
            completed_processors=set(checkpoint_data["completed_processors"]),
            processor_states={
                k: ProcessorStatus(v)
                for k, v in checkpoint_data["processor_states"].items()
            },
            data_snapshots=checkpoint_data["data_snapshots"],
            metadata=checkpoint_data["metadata"]
        )
        
        # Get pipeline and create execution
        pipeline = self._pipelines.get(checkpoint.pipeline_id)
        if not pipeline:
            raise DataPipelineException(f"Pipeline {checkpoint.pipeline_id} not found")
        
        execution = pipeline.create_execution()
        execution.restore_from_checkpoint(checkpoint)
        
        self.logger.info(f"Restored execution from checkpoint {checkpoint_id}")
        return execution
    
    def _create_status_from_execution(self, execution: PipelineExecution) -> PipelineStatus:
        """Create legacy status object from execution."""
        status = PipelineStatus(
            pipeline_id=execution.execution_id,
            stage=self._map_state_to_stage(execution.state),
            started_at=execution.started_at
        )
        
        # Calculate progress
        completed_count = sum(
            1 for p in execution.processor_graph.values()
            if p.status == ProcessorStatus.COMPLETED
        )
        total_count = len(execution.processor_graph)
        status.current_stage_progress = completed_count / total_count if total_count > 0 else 0
        
        # Add errors and warnings
        status.errors = execution.errors
        status.warnings = execution.warnings
        
        return status
    
    def _map_state_to_stage(self, state: PipelineState):
        """Map new state to legacy stage."""
        from .data_pipeline_orchestrator import PipelineStage
        
        mapping = {
            PipelineState.PENDING: PipelineStage.COLLECTION,
            PipelineState.INITIALIZING: PipelineStage.COLLECTION,
            PipelineState.COLLECTING: PipelineStage.COLLECTION,
            PipelineState.PROCESSING: PipelineStage.PROCESSING,
            PipelineState.INTEGRATING: PipelineStage.INTEGRATION,
            PipelineState.VALIDATING: PipelineStage.VALIDATION,
            PipelineState.BUILDING: PipelineStage.PANEL_BUILDING,
            PipelineState.COMPLETED: PipelineStage.COMPLETED,
            PipelineState.FAILED: PipelineStage.FAILED
        }
        return mapping.get(state, PipelineStage.COLLECTION)
    
    def export_metrics(self, file_path: Optional[str] = None) -> str:
        """Export metrics in Prometheus format."""
        if file_path:
            self.prometheus_exporter.export_to_file(file_path)
        return self.prometheus_exporter.export_metrics()
    
    def get_health_status(self) -> Dict[str, any]:
        """Get overall system health status."""
        if not self.health_checker:
            return {"status": "monitoring_disabled"}
        
        return {
            "overall_status": self.health_checker.get_overall_status().value,
            "components": {
                name: component.last_result.to_dict() if component.last_result else None
                for name, component in self.health_checker.components.items()
            },
            "critical_components": [
                c.name for c in self.health_checker.get_critical_components()
            ]
        }
    
    # Processor implementations (stubs that delegate to actual processors)
    
    async def _process_wfp_collection(self, config: PipelineConfig) -> Dict[str, any]:
        """Process WFP data collection."""
        # This would delegate to actual WFP collection logic
        return {"records_processed": 1000}
    
    async def _process_acled_collection(self, config: PipelineConfig) -> Dict[str, any]:
        """Process ACLED data collection."""
        return {"records_processed": 500}
    
    async def _process_acaps_collection(self, config: PipelineConfig) -> Dict[str, any]:
        """Process ACAPS data collection."""
        return {"records_processed": 200}
    
    async def _process_wfp_data(self, config: PipelineConfig) -> Dict[str, any]:
        """Process WFP data."""
        return {"records_processed": 1000}
    
    async def _process_acled_data(self, config: PipelineConfig) -> Dict[str, any]:
        """Process ACLED data."""
        return {"records_processed": 500}
    
    async def _process_acaps_data(self, config: PipelineConfig) -> Dict[str, any]:
        """Process ACAPS data."""
        return {"records_processed": 200}
    
    async def _process_spatial_integration(self, config: PipelineConfig) -> Dict[str, any]:
        """Process spatial integration."""
        return {"records_processed": 1700}
    
    async def _process_panel_building(self, config: PipelineConfig) -> Dict[str, any]:
        """Process panel building."""
        return {"records_processed": 1500}
    
    async def _process_validation(self, config: PipelineConfig) -> Dict[str, any]:
        """Process validation."""
        return {"records_processed": 1500}