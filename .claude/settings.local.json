{"permissions": {"allow": ["mcp__sequential-thinking__sequentialthinking", "Bash(ls:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(find:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git push:*)", "<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(chmod:*)", "Bash(git fetch:*)", "Bash(git checkout:*)", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(uv run:*)", "Bash(git merge:*)", "Bash(git branch:*)"]}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["yemen-research"]}