# Yemen Market Integration: Critical Import Fixes - Priority 1

## 🚨 URGENT: Day 10 Critical Import Dependencies Resolution

**Context**: You are working on the Yemen Market Integration Data Pipeline V2 project on Day 10 of a 20-day roadmap. The system has comprehensive infrastructure but is blocked by critical import dependency issues that prevent end-to-end testing, data migration, and CLI functionality.

**Mission**: Fix the cascading import failures that are blocking all Day 10 objectives. This is Priority 1 and must be completed before any other work can proceed.

## Current Status
- ✅ Performance validation works (75.4/100 score, ACCEPTABLE)
- ✅ Extensive V2 infrastructure exists (Days 1-9 complete)
- ❌ Import errors block: CLI, end-to-end testing, migration, 39 test files
- ❌ Test coverage at 11% (target: >90%)

## Critical Import Errors to Fix

### 1. Missing ConflictEventId Value Object
**Error**: `ImportError: cannot import name 'ConflictEventId' from 'src.core.domain.conflict.value_objects'`

**Files Affected**:
- `src/infrastructure/persistence/repository_impls/postgres/conflict_repository.py`
- Multiple test files throughout the system

**Required Fix**: Add `ConflictEventId` class to `src/core/domain/conflict/value_objects.py`

### 2. Missing BATTLE Enum Value  
**Error**: `AttributeError: BATTLE` in `ConflictType.BATTLE`

**Files Affected**:
- `src/infrastructure/processors/acled_processor.py` (line 41)
- Multiple processor and test files

**Required Fix**: Add `BATTLE` enum value to `EventType` in `src/core/domain/conflict/value_objects.py`

### 3. Missing CacheStrategy Enum
**Error**: `ImportError: cannot import name 'CacheStrategy' from 'src.infrastructure.caching.cache_manager'`

**Files Affected**:
- `tests/infrastructure/test_cache_manager.py`
- Cache-related components

**Required Fix**: Add `CacheStrategy` enum to `src/infrastructure/caching/cache_manager.py`

### 4. Missing ExchangeRateCollector Export
**Error**: `ImportError: cannot import name 'ExchangeRateCollector' from 'src.infrastructure.external_services.exchange_rate_collector'`

**Files Affected**:
- `src/infrastructure/data_quality/dynamic_exchange_rate_validator.py`
- Exchange rate related components

**Required Fix**: Ensure `ExchangeRateCollector` class is properly defined and exported

## Implementation Instructions

### Step 1: Fix ConflictEventId (CRITICAL)
Add to `src/core/domain/conflict/value_objects.py`:

```python
@dataclass(frozen=True)
class ConflictEventId(ValueObject):
    """Unique identifier for conflict events."""
    
    value: str
    
    def __post_init__(self) -> None:
        """Validate conflict event ID."""
        if not self.value or len(self.value) < 3:
            raise ValidationException("ConflictEventId must be at least 3 characters")
    
    def __str__(self) -> str:
        return self.value
```

### Step 2: Fix BATTLE Enum Value (CRITICAL)
Modify `EventType` enum in `src/core/domain/conflict/value_objects.py`:

```python
class EventType(Enum):
    """Types of conflict events from ACLED."""
    BATTLES = "Battles"
    BATTLE = "Battles"  # Alias for backward compatibility
    EXPLOSIONS_REMOTE_VIOLENCE = "Explosions/Remote violence"
    VIOLENCE_AGAINST_CIVILIANS = "Violence against civilians"
    PROTESTS = "Protests"
    RIOTS = "Riots"
    STRATEGIC_DEVELOPMENTS = "Strategic developments"
```

### Step 3: Fix CacheStrategy (HIGH)
Add to `src/infrastructure/caching/cache_manager.py`:

```python
class CacheStrategy(Enum):
    """Cache strategy options."""
    LRU = "lru"
    FIFO = "fifo"
    TTL = "ttl"
    NONE = "none"
```

### Step 4: Verify ExchangeRateCollector (HIGH)
Check `src/infrastructure/external_services/exchange_rate_collector.py` has proper class definition and export.

## Validation Commands

After each fix, run these validation commands using UV:

```bash
# Test conflict imports
uv run python -c "from src.core.domain.conflict.value_objects import ConflictEventId, ConflictType; print('✅ Conflict imports work')"

# Test ACLED processor
uv run python -c "from src.infrastructure.processors.acled_processor import ACLEDProcessor; print('✅ ACLED processor works')"

# Test cache manager
uv run python -c "from src.infrastructure.caching.cache_manager import CacheStrategy; print('✅ Cache manager works')"

# Test CLI status (ultimate validation)
uv run python -m src.cli status
```

## Success Criteria

You have successfully completed Priority 1 when:
- ✅ All validation commands run without import errors
- ✅ CLI status command executes successfully
- ✅ ACLED processor can be imported without AttributeError
- ✅ ConflictEventId can be imported from value objects
- ✅ CacheStrategy enum is available

## Critical Requirements

1. **Use UV Package Manager**: All commands must use `uv run python` prefix
2. **Maintain Existing Code**: Only add missing components, don't modify existing working code
3. **Follow Existing Patterns**: Match the coding style and patterns already established
4. **Test Each Fix**: Validate each fix individually before proceeding to the next
5. **World Bank Standards**: Maintain the methodological rigor and documentation standards

## After Priority 1 Completion

Once these critical imports are fixed, the system should be ready for:
- End-to-end pipeline testing
- Data migration execution  
- Comprehensive test coverage measurement
- Full CLI functionality

## Context Files to Reference

- `DAY_10_COMPLETION_REPORT.md` - Current status assessment
- `CRITICAL_IMPORT_FIXES.md` - Detailed technical analysis
- `docs/DATA_PIPELINE_V2_PROGRESS.md` - Implementation history
- `CLAUDE.md` - Project overview and requirements

**Remember**: This is production-critical code affecting humanitarian aid distribution for millions of Yemenis. Every fix must maintain the three-tier econometric methodology and World Bank standards while resolving the immediate technical blockers.
