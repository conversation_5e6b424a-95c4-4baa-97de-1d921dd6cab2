# Google Jules Prompt: Data Pipeline V2 Implementation - Day 3 Tasks

## Project Context

You are working on the Yemen Market Integration project, implementing a production-ready data pipeline (V2) to integrate 10+ data sources for World Bank econometric analysis. The pipeline must handle currency fragmentation analysis with different exchange rates across regions (North: ~535 YER/USD, South: ~2000 YER/USD).

## Current State

**Repository**: yemen-market-integration  
**Day**: 3 of 20-day implementation plan  
**Completed**: Core infrastructure, BaseProcessor, ValidationFramework, CacheManager, HDXEnhancedClient, ConflictProcessor, GeoDataProcessor, AidDistributionProcessor

## Today's Tasks (Day 3 - Sequential Order)

### Task 1: Enhance HDX Client (2 hours)

**Objective**: Enhance the existing HDXEnhancedClient to support dataset search, resource filtering, and progress tracking.

**Requirements**:
1. Add dataset search capabilities to find Yemen-specific datasets
2. Implement resource filtering by format (CSV, Excel, GeoJSON, Shapefile)
3. Add progress callbacks for large downloads using the existing ProgressAdapter
4. Handle HDX authentication properly (API key from environment)
5. Create HDX dataset registry mapping dataset IDs to categories

**Files to modify**:
- `src/infrastructure/external_services/hdx_enhanced_client.py`

**Expected outputs**:
- Method `search_datasets(query: str, country: str = "YEM") -> List[Dict]`
- Method `filter_resources(dataset_id: str, formats: List[str]) -> List[Dict]`
- Updated `download_resource()` with progress callback support
- Dataset registry in `src/infrastructure/data_sources/hdx_dataset_registry.py`

**Tests to write**:
- `tests/infrastructure/test_hdx_enhanced_client.py` (update existing)

### Task 2: Climate Data Processor (2 hours)

**Objective**: Complete the climate data processor for CHIRPS rainfall and MODIS NDVI data.

**Requirements**:
1. Extend GeoDataProcessor for CHIRPS rainfall data (already started)
2. Add MODIS NDVI vegetation index processing
3. Implement temporal aggregation (monthly averages)
4. Create climate metrics calculator (drought index, anomalies)
5. Handle large raster files with chunking

**Files to create/modify**:
- `src/infrastructure/processors/climate_processor.py` (enhance existing)
- `src/core/domain/climate/metrics.py` (new)

**Expected outputs**:
- Process CHIRPS rainfall GeoTIFF files to market-level monthly rainfall
- Process MODIS NDVI for vegetation health monitoring
- Calculate standardized precipitation index (SPI)
- Generate climate anomaly indicators

**Tests to write**:
- `tests/infrastructure/processors/test_climate_processor.py`

### Task 3: Population Data Processor (1.5 hours)

**Objective**: Create processor for WorldPop and IOM displacement data.

**Requirements**:
1. Process WorldPop raster data for population density
2. Handle IOM DTM (Displacement Tracking Matrix) CSV data
3. Calculate population metrics at market catchment areas
4. Support temporal changes in population

**Files to create**:
- `src/infrastructure/processors/population_processor.py`
- `src/core/domain/population/entities.py`
- `src/core/domain/population/value_objects.py`

**Expected outputs**:
- Market-level population estimates
- Displacement flow metrics
- Population density per km²
- Temporal population changes

**Tests to write**:
- `tests/infrastructure/processors/test_population_processor.py`

### Task 4: Infrastructure Processor (1.5 hours)

**Objective**: Extract and process infrastructure data from OpenStreetMap and other sources.

**Requirements**:
1. Extract road networks from OSM using Overpass API
2. Identify ports, airports, and border crossings
3. Calculate market accessibility metrics (distance to infrastructure)
4. Process checkpoint and control point locations

**Files to create**:
- `src/infrastructure/processors/infrastructure_processor.py`
- `src/infrastructure/external_services/osm_client.py`
- `src/core/domain/infrastructure/entities.py`

**Expected outputs**:
- Road network graph for Yemen
- Distance matrix between markets and infrastructure
- Market accessibility scores
- Infrastructure quality indicators

**Tests to write**:
- `tests/infrastructure/processors/test_infrastructure_processor.py`

### Task 5: Integration Tests (1 hour)

**Objective**: Create comprehensive integration tests for all processors.

**Requirements**:
1. Test processor factory with all processor types
2. Validate error recovery mechanisms (retry, circuit breaker)
3. Test progress tracking integration across processors
4. End-to-end pipeline test with sample data

**Files to create**:
- `tests/integration/test_processor_integration.py`
- `tests/integration/test_pipeline_end_to_end.py`

**Test scenarios**:
- Process all data types in sequence
- Handle API failures and retries
- Validate progress reporting
- Check memory usage and performance

## Code Quality Standards

1. **Type Hints**: All functions must have complete type annotations
2. **Docstrings**: Comprehensive docstrings with Args, Returns, Raises
3. **Error Handling**: Use the established exception hierarchy
4. **Logging**: Structured logging with appropriate levels
5. **Async/Await**: All I/O operations must be async
6. **Validation**: Multi-level validation at each processing step
7. **Progress Tracking**: Integrate with PipelineProgressTracker

## Example Code Pattern

```python
class ClimateProcessor(BaseProcessor[ClimateData]):
    """Process climate data from CHIRPS and MODIS sources."""
    
    def __init__(
        self,
        cache_manager: CacheManager,
        spatial_service: SpatialIntegrationService,
        temporal_service: TemporalAlignmentService,
        progress_callback: Optional[Callable] = None
    ):
        super().__init__(
            "climate_processor",
            ClimateData,
            cache_manager,
            progress_callback
        )
        self.spatial_service = spatial_service
        self.temporal_service = temporal_service
    
    async def _process_internal(
        self,
        data: ClimateData,
        config: Dict[str, Any]
    ) -> ProcessingResult:
        """Process climate data with validation and error handling."""
        # Implementation following established patterns
```

## Success Criteria

1. All processors follow the established BaseProcessor pattern
2. Tests achieve >90% code coverage
3. Progress tracking works across all processors
4. Error recovery mechanisms are properly tested
5. Documentation is complete and clear
6. Integration tests pass without manual intervention

## Commands to Run

```bash
# Test your implementation
uv run pytest tests/infrastructure/test_hdx_enhanced_client.py -v
uv run pytest tests/infrastructure/processors/test_climate_processor.py -v
uv run pytest tests/infrastructure/processors/test_population_processor.py -v
uv run pytest tests/infrastructure/processors/test_infrastructure_processor.py -v
uv run pytest tests/integration/test_processor_integration.py -v

# Check code quality
uv run black src/infrastructure/
uv run ruff check src/infrastructure/
uv run mypy src/infrastructure/

# Run processors
uv run python -m src.infrastructure.processors.climate_processor
uv run python -m src.infrastructure.processors.population_processor
```

## Important Notes

1. **Domain Boundaries**: Each processor operates within its bounded context
2. **Reuse Components**: Use existing services (spatial, temporal, cache)
3. **Performance**: Chunk large files, use async I/O, implement progress tracking
4. **Validation**: Every processor must validate inputs and outputs
5. **Error Handling**: Use established patterns (retry, circuit breaker)

## References

- Implementation Plan: `docs/DATA_PIPELINE_V2_COMPREHENSIVE_PLAN.md`
- Technical Blueprint: `docs/DATA_PIPELINE_V2_IMPLEMENTATION_BLUEPRINT.md`
- Day-by-day Roadmap: `docs/DATA_PIPELINE_V2_ROADMAP.md`
- Progress Tracking: `docs/DATA_PIPELINE_V2_PROGRESS.md`

Focus on completing these tasks sequentially. Each builds on the previous work. The enhanced HDX client is critical for subsequent processors that need to download data.