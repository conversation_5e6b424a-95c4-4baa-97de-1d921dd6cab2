#!/usr/bin/env python3
"""
Performance Requirements Validation - Data Pipeline V2 Day 10

Validates that the data pipeline meets all performance requirements:
- Processing time < 30 minutes
- Memory usage < 8GB
- Throughput > 10 records/second
- 88.4% data coverage target
- Resource efficiency metrics
"""

import asyncio
import time
import psutil
import pandas as pd
from pathlib import Path
from typing import Dict, Any, List
from datetime import datetime, timedelta
import json
import logging
from dataclasses import dataclass
import tracemalloc

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetrics:
    """Performance metrics container"""
    processing_time_seconds: float
    peak_memory_mb: float
    average_memory_mb: float
    peak_cpu_percent: float
    average_cpu_percent: float
    total_records_processed: int
    records_per_second: float
    data_coverage_percentage: float
    cache_hit_rate: float
    error_rate: float
    
    @property
    def processing_time_minutes(self) -> float:
        return self.processing_time_seconds / 60
    
    @property
    def meets_time_requirement(self) -> bool:
        return self.processing_time_minutes < 30
    
    @property
    def meets_memory_requirement(self) -> bool:
        return self.peak_memory_mb < 8192  # 8GB
    
    @property
    def meets_throughput_requirement(self) -> bool:
        return self.records_per_second > 10
    
    @property
    def meets_coverage_requirement(self) -> bool:
        return self.data_coverage_percentage >= 88.4
    
    @property
    def overall_performance_score(self) -> float:
        """Calculate overall performance score (0-100)"""
        scores = []
        
        # Time score (30 min target)
        time_score = min(100, (30 / max(self.processing_time_minutes, 1)) * 100)
        scores.append(time_score)
        
        # Memory score (8GB target)
        memory_score = min(100, (8192 / max(self.peak_memory_mb, 1)) * 100)
        scores.append(memory_score)
        
        # Throughput score (10 records/sec target)
        throughput_score = min(100, (self.records_per_second / 10) * 100)
        scores.append(throughput_score)
        
        # Coverage score (88.4% target)
        coverage_score = (self.data_coverage_percentage / 88.4) * 100
        scores.append(coverage_score)
        
        # Error rate score (0% target)
        error_score = max(0, 100 - (self.error_rate * 100))
        scores.append(error_score)
        
        return sum(scores) / len(scores)

class PerformanceValidator:
    """Validates system performance against requirements"""
    
    def __init__(self):
        self.metrics_history = []
        self.current_metrics = None
        self.validation_results = {
            "validation_date": datetime.utcnow().isoformat(),
            "requirements": {
                "max_processing_time_minutes": 30,
                "max_memory_gb": 8,
                "min_throughput_records_per_sec": 10,
                "min_data_coverage_percent": 88.4,
                "max_error_rate_percent": 5.0
            },
            "test_scenarios": {},
            "overall_status": "PENDING"
        }
    
    async def validate_performance_requirements(self) -> Dict[str, Any]:
        """Run comprehensive performance validation"""
        logger.info("Starting performance requirements validation")
        
        try:
            # Test Scenario 1: Small dataset (development)
            small_metrics = await self._test_small_dataset()
            self.validation_results["test_scenarios"]["small_dataset"] = small_metrics
            
            # Test Scenario 2: Medium dataset (typical production)
            medium_metrics = await self._test_medium_dataset()
            self.validation_results["test_scenarios"]["medium_dataset"] = medium_metrics
            
            # Test Scenario 3: Large dataset (stress test)
            large_metrics = await self._test_large_dataset()
            self.validation_results["test_scenarios"]["large_dataset"] = large_metrics
            
            # Test Scenario 4: Memory pressure test
            memory_metrics = await self._test_memory_pressure()
            self.validation_results["test_scenarios"]["memory_pressure"] = memory_metrics
            
            # Test Scenario 5: Concurrent processing
            concurrent_metrics = await self._test_concurrent_processing()
            self.validation_results["test_scenarios"]["concurrent_processing"] = concurrent_metrics
            
            # Analyze results
            self._analyze_performance_results()
            
            # Generate recommendations
            self._generate_performance_recommendations()
            
            # Save results
            self._save_validation_results()
            
        except Exception as e:
            self.validation_results["overall_status"] = "ERROR"
            self.validation_results["error"] = str(e)
            logger.error(f"Performance validation failed: {e}")
        
        return self.validation_results
    
    async def _test_small_dataset(self) -> Dict[str, Any]:
        """Test with small dataset (3 months, limited markets)"""
        logger.info("Testing performance with small dataset")
        
        config = {
            "start_date": "2023-10-01",
            "end_date": "2023-12-31",  # 3 months
            "max_markets": 50,
            "enable_caching": True,
            "parallel_processors": 2
        }
        
        return await self._run_performance_test("small_dataset", config)
    
    async def _test_medium_dataset(self) -> Dict[str, Any]:
        """Test with medium dataset (6 months, typical markets)"""
        logger.info("Testing performance with medium dataset")
        
        config = {
            "start_date": "2023-07-01",
            "end_date": "2023-12-31",  # 6 months
            "max_markets": 200,
            "enable_caching": True,
            "parallel_processors": 4
        }
        
        return await self._run_performance_test("medium_dataset", config)
    
    async def _test_large_dataset(self) -> Dict[str, Any]:
        """Test with large dataset (full historical data)"""
        logger.info("Testing performance with large dataset")
        
        config = {
            "start_date": "2019-01-01",
            "end_date": "2024-12-31",  # Full historical
            "max_markets": None,  # All markets
            "enable_caching": True,
            "parallel_processors": 6
        }
        
        return await self._run_performance_test("large_dataset", config)
    
    async def _test_memory_pressure(self) -> Dict[str, Any]:
        """Test under memory pressure conditions"""
        logger.info("Testing performance under memory pressure")
        
        # Simulate memory pressure by reducing available memory
        config = {
            "start_date": "2023-01-01",
            "end_date": "2023-12-31",
            "max_markets": 300,
            "enable_caching": False,  # Disable caching to increase memory usage
            "parallel_processors": 8,  # Increase parallelism
            "memory_limit_mb": 4096  # Limit to 4GB
        }
        
        return await self._run_performance_test("memory_pressure", config)
    
    async def _test_concurrent_processing(self) -> Dict[str, Any]:
        """Test concurrent pipeline processing"""
        logger.info("Testing concurrent processing performance")
        
        # Run multiple smaller pipelines concurrently
        tasks = []
        for i in range(3):
            config = {
                "start_date": f"2023-{i*4+1:02d}-01",
                "end_date": f"2023-{i*4+4:02d}-30",
                "max_markets": 100,
                "enable_caching": True,
                "parallel_processors": 2,
                "instance_id": f"concurrent_{i}"
            }
            task = asyncio.create_task(
                self._run_performance_test(f"concurrent_{i}", config)
            )
            tasks.append(task)
        
        # Wait for all tasks to complete
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Aggregate results
        total_time = max(r.get("processing_time_seconds", 0) for r in results if isinstance(r, dict))
        total_records = sum(r.get("total_records_processed", 0) for r in results if isinstance(r, dict))
        peak_memory = max(r.get("peak_memory_mb", 0) for r in results if isinstance(r, dict))
        
        return {
            "processing_time_seconds": total_time,
            "processing_time_minutes": total_time / 60,
            "total_records_processed": total_records,
            "peak_memory_mb": peak_memory,
            "concurrent_instances": len(tasks),
            "successful_instances": len([r for r in results if isinstance(r, dict)]),
            "meets_requirements": total_time < 1800  # 30 minutes
        }
    
    async def _run_performance_test(self, test_name: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """Run a single performance test with given configuration"""
        logger.info(f"Running performance test: {test_name}")
        
        # Start monitoring
        process = psutil.Process()
        start_time = time.time()
        start_memory = process.memory_info().rss / 1024 / 1024  # MB
        tracemalloc.start()
        
        # Resource monitoring
        peak_memory = start_memory
        memory_samples = [start_memory]
        cpu_samples = []
        
        try:
            # Mock pipeline execution (replace with actual pipeline call)
            await self._simulate_pipeline_execution(config)
            
            # Sample resource usage during execution
            for _ in range(10):  # Sample 10 times during execution
                await asyncio.sleep(0.1)
                current_memory = process.memory_info().rss / 1024 / 1024
                current_cpu = process.cpu_percent()
                
                memory_samples.append(current_memory)
                cpu_samples.append(current_cpu)
                peak_memory = max(peak_memory, current_memory)
            
            # Calculate metrics
            end_time = time.time()
            processing_time = end_time - start_time
            
            # Get memory statistics
            current, peak_trace = tracemalloc.get_traced_memory()
            tracemalloc.stop()
            
            # Calculate derived metrics
            total_records = self._estimate_records_processed(config)
            throughput = total_records / processing_time if processing_time > 0 else 0
            data_coverage = self._estimate_data_coverage(config)
            
            # Create performance metrics
            metrics = PerformanceMetrics(
                processing_time_seconds=processing_time,
                peak_memory_mb=max(peak_memory, peak_trace / 1024 / 1024),
                average_memory_mb=sum(memory_samples) / len(memory_samples),
                peak_cpu_percent=max(cpu_samples) if cpu_samples else 0,
                average_cpu_percent=sum(cpu_samples) / len(cpu_samples) if cpu_samples else 0,
                total_records_processed=total_records,
                records_per_second=throughput,
                data_coverage_percentage=data_coverage,
                cache_hit_rate=85.0,  # Mock value
                error_rate=0.02  # Mock value (2%)
            )
            
            return {
                "test_name": test_name,
                "config": config,
                "metrics": {
                    "processing_time_seconds": metrics.processing_time_seconds,
                    "processing_time_minutes": metrics.processing_time_minutes,
                    "peak_memory_mb": metrics.peak_memory_mb,
                    "average_memory_mb": metrics.average_memory_mb,
                    "peak_cpu_percent": metrics.peak_cpu_percent,
                    "average_cpu_percent": metrics.average_cpu_percent,
                    "total_records_processed": metrics.total_records_processed,
                    "records_per_second": metrics.records_per_second,
                    "data_coverage_percentage": metrics.data_coverage_percentage,
                    "cache_hit_rate": metrics.cache_hit_rate,
                    "error_rate": metrics.error_rate
                },
                "requirements_met": {
                    "processing_time": metrics.meets_time_requirement,
                    "memory_usage": metrics.meets_memory_requirement,
                    "throughput": metrics.meets_throughput_requirement,
                    "data_coverage": metrics.meets_coverage_requirement
                },
                "performance_score": metrics.overall_performance_score,
                "status": "PASS" if all([
                    metrics.meets_time_requirement,
                    metrics.meets_memory_requirement,
                    metrics.meets_throughput_requirement,
                    metrics.meets_coverage_requirement
                ]) else "FAIL"
            }
            
        except Exception as e:
            tracemalloc.stop()
            end_time = time.time()
            
            return {
                "test_name": test_name,
                "config": config,
                "status": "ERROR",
                "error": str(e),
                "processing_time_seconds": end_time - start_time,
                "requirements_met": {
                    "processing_time": False,
                    "memory_usage": False,
                    "throughput": False,
                    "data_coverage": False
                },
                "performance_score": 0
            }
    
    async def _simulate_pipeline_execution(self, config: Dict[str, Any]):
        """Simulate pipeline execution for testing"""
        # Simulate data processing based on configuration
        start_date = datetime.strptime(config["start_date"], "%Y-%m-%d")
        end_date = datetime.strptime(config["end_date"], "%Y-%m-%d")
        
        months = (end_date - start_date).days / 30
        markets = config.get("max_markets", 300)
        parallel_processors = config.get("parallel_processors", 4)
        
        # Simulate processing time based on data volume
        base_time = months * markets / 1000  # Base processing time
        parallel_factor = max(1, 4 / parallel_processors)  # Parallelization benefit
        cache_factor = 0.7 if config.get("enable_caching", True) else 1.0
        
        simulated_time = base_time * parallel_factor * cache_factor
        
        # Add some random variation
        import random
        simulated_time *= random.uniform(0.8, 1.2)
        
        # Simulate work with actual delay
        await asyncio.sleep(min(simulated_time, 5.0))  # Cap at 5 seconds for testing
        
        # Simulate memory allocation
        if config.get("memory_limit_mb"):
            # Simulate memory-intensive operations
            data = []
            for _ in range(100):
                data.append([0] * 10000)  # Allocate some memory
                await asyncio.sleep(0.01)
            del data  # Clean up
    
    def _estimate_records_processed(self, config: Dict[str, Any]) -> int:
        """Estimate number of records processed based on configuration"""
        start_date = datetime.strptime(config["start_date"], "%Y-%m-%d")
        end_date = datetime.strptime(config["end_date"], "%Y-%m-%d")
        
        months = max(1, (end_date - start_date).days / 30)
        markets = config.get("max_markets", 300)
        commodities = 12  # Average commodities per market
        
        # Estimate total observations
        total_records = int(months * markets * commodities * 30)  # Daily records
        
        return total_records
    
    def _estimate_data_coverage(self, config: Dict[str, Any]) -> float:
        """Estimate data coverage percentage"""
        # Base coverage varies by time period
        start_date = datetime.strptime(config["start_date"], "%Y-%m-%d")
        
        if start_date.year >= 2023:
            base_coverage = 92.0  # Recent data has better coverage
        elif start_date.year >= 2020:
            base_coverage = 88.5  # Moderate coverage
        else:
            base_coverage = 85.0  # Historical data has gaps
        
        # Adjust for market limitations
        if config.get("max_markets") and config["max_markets"] < 200:
            base_coverage += 2.0  # Smaller datasets often have better coverage
        
        return min(95.0, base_coverage)
    
    def _analyze_performance_results(self):
        """Analyze performance test results"""
        scenarios = self.validation_results["test_scenarios"]
        
        # Calculate summary statistics
        all_passed = all(
            scenario.get("status") == "PASS" 
            for scenario in scenarios.values()
        )
        
        performance_scores = [
            scenario.get("performance_score", 0)
            for scenario in scenarios.values()
            if scenario.get("performance_score") is not None
        ]
        
        avg_performance_score = sum(performance_scores) / len(performance_scores) if performance_scores else 0
        
        # Check critical scenarios
        critical_scenarios = ["medium_dataset", "large_dataset"]
        critical_passed = all(
            scenarios.get(scenario, {}).get("status") == "PASS"
            for scenario in critical_scenarios
            if scenario in scenarios
        )
        
        # Determine overall status
        if all_passed and avg_performance_score >= 80:
            overall_status = "EXCELLENT"
        elif critical_passed and avg_performance_score >= 70:
            overall_status = "GOOD"
        elif any(scenarios.get(s, {}).get("status") == "PASS" for s in critical_scenarios):
            overall_status = "ACCEPTABLE"
        else:
            overall_status = "POOR"
        
        self.validation_results["analysis"] = {
            "all_scenarios_passed": all_passed,
            "critical_scenarios_passed": critical_passed,
            "average_performance_score": avg_performance_score,
            "total_scenarios": len(scenarios),
            "passed_scenarios": len([s for s in scenarios.values() if s.get("status") == "PASS"]),
            "overall_status": overall_status
        }
        
        self.validation_results["overall_status"] = overall_status
    
    def _generate_performance_recommendations(self):
        """Generate performance improvement recommendations"""
        recommendations = []
        scenarios = self.validation_results["test_scenarios"]
        
        for scenario_name, scenario_data in scenarios.items():
            if scenario_data.get("status") != "PASS":
                requirements_met = scenario_data.get("requirements_met", {})
                
                if not requirements_met.get("processing_time", True):
                    recommendations.append({
                        "category": "processing_time",
                        "priority": "HIGH",
                        "scenario": scenario_name,
                        "recommendation": "Optimize processing pipeline with increased parallelization or data partitioning"
                    })
                
                if not requirements_met.get("memory_usage", True):
                    recommendations.append({
                        "category": "memory_usage",
                        "priority": "HIGH", 
                        "scenario": scenario_name,
                        "recommendation": "Implement memory optimization strategies such as data streaming or garbage collection tuning"
                    })
                
                if not requirements_met.get("throughput", True):
                    recommendations.append({
                        "category": "throughput",
                        "priority": "MEDIUM",
                        "scenario": scenario_name,
                        "recommendation": "Improve throughput with async processing, caching, or database optimization"
                    })
                
                if not requirements_met.get("data_coverage", True):
                    recommendations.append({
                        "category": "data_coverage",
                        "priority": "MEDIUM",
                        "scenario": scenario_name,
                        "recommendation": "Enhance data collection and imputation strategies to reach 88.4% coverage target"
                    })
        
        # Add general recommendations based on overall performance
        avg_score = self.validation_results.get("analysis", {}).get("average_performance_score", 0)
        
        if avg_score < 70:
            recommendations.append({
                "category": "general",
                "priority": "CRITICAL",
                "scenario": "overall",
                "recommendation": "Comprehensive performance optimization required across all components"
            })
        elif avg_score < 85:
            recommendations.append({
                "category": "general",
                "priority": "MEDIUM",
                "scenario": "overall",
                "recommendation": "Fine-tune performance bottlenecks identified in testing"
            })
        
        self.validation_results["recommendations"] = recommendations
    
    def _save_validation_results(self):
        """Save performance validation results"""
        results_dir = Path("reports/performance_validation")
        results_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
        results_file = results_dir / f"performance_validation_{timestamp}.json"
        
        with open(results_file, 'w') as f:
            json.dump(self.validation_results, f, indent=2, default=str)
        
        # Create summary report
        self._create_performance_summary(results_dir, timestamp)
        
        logger.info(f"Performance validation results saved to {results_file}")
    
    def _create_performance_summary(self, results_dir: Path, timestamp: str):
        """Create human-readable performance summary"""
        summary_file = results_dir / f"performance_summary_{timestamp}.md"
        
        with open(summary_file, 'w') as f:
            f.write("# Performance Validation Report\n\n")
            f.write(f"**Validation Date:** {self.validation_results['validation_date']}\n")
            f.write(f"**Overall Status:** {self.validation_results['overall_status']}\n\n")
            
            # Requirements
            f.write("## Performance Requirements\n\n")
            reqs = self.validation_results["requirements"]
            f.write(f"- **Maximum Processing Time:** {reqs['max_processing_time_minutes']} minutes\n")
            f.write(f"- **Maximum Memory Usage:** {reqs['max_memory_gb']} GB\n")
            f.write(f"- **Minimum Throughput:** {reqs['min_throughput_records_per_sec']} records/second\n")
            f.write(f"- **Minimum Data Coverage:** {reqs['min_data_coverage_percent']}%\n")
            f.write(f"- **Maximum Error Rate:** {reqs['max_error_rate_percent']}%\n\n")
            
            # Test scenarios
            f.write("## Test Scenario Results\n\n")
            for scenario_name, scenario_data in self.validation_results["test_scenarios"].items():
                status = scenario_data.get("status", "UNKNOWN")
                status_icon = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
                
                f.write(f"### {scenario_name.replace('_', ' ').title()} {status_icon}\n\n")
                
                if "metrics" in scenario_data:
                    metrics = scenario_data["metrics"]
                    f.write(f"- **Processing Time:** {metrics.get('processing_time_minutes', 0):.1f} minutes\n")
                    f.write(f"- **Peak Memory:** {metrics.get('peak_memory_mb', 0):.1f} MB\n")
                    f.write(f"- **Throughput:** {metrics.get('records_per_second', 0):.1f} records/sec\n")
                    f.write(f"- **Data Coverage:** {metrics.get('data_coverage_percentage', 0):.1f}%\n")
                    f.write(f"- **Performance Score:** {scenario_data.get('performance_score', 0):.1f}/100\n\n")
            
            # Analysis summary
            if "analysis" in self.validation_results:
                analysis = self.validation_results["analysis"]
                f.write("## Analysis Summary\n\n")
                f.write(f"- **Scenarios Passed:** {analysis['passed_scenarios']}/{analysis['total_scenarios']}\n")
                f.write(f"- **Average Performance Score:** {analysis['average_performance_score']:.1f}/100\n")
                f.write(f"- **Critical Scenarios Passed:** {'Yes' if analysis['critical_scenarios_passed'] else 'No'}\n\n")
            
            # Recommendations
            if self.validation_results.get("recommendations"):
                f.write("## Recommendations\n\n")
                for rec in self.validation_results["recommendations"]:
                    priority_icon = "🔴" if rec["priority"] == "CRITICAL" else "🟡" if rec["priority"] == "HIGH" else "🟢"
                    f.write(f"- {priority_icon} **{rec['category'].replace('_', ' ').title()}** ({rec['scenario']}): {rec['recommendation']}\n")
            
            f.write("\n## Overall Assessment\n\n")
            status = self.validation_results["overall_status"]
            if status == "EXCELLENT":
                f.write("✅ **EXCELLENT** - System exceeds all performance requirements\n")
            elif status == "GOOD":
                f.write("✅ **GOOD** - System meets critical performance requirements\n")
            elif status == "ACCEPTABLE":
                f.write("⚠️ **ACCEPTABLE** - System meets minimum requirements with some optimizations needed\n")
            else:
                f.write("❌ **POOR** - System does not meet performance requirements and needs significant optimization\n")


async def main():
    """Run performance validation"""
    validator = PerformanceValidator()
    results = await validator.validate_performance_requirements()
    
    # Print summary
    print("\n" + "="*60)
    print("PERFORMANCE VALIDATION RESULTS")
    print("="*60)
    
    print(f"Overall Status: {results['overall_status']}")
    
    if "analysis" in results:
        analysis = results["analysis"]
        print(f"Scenarios Passed: {analysis['passed_scenarios']}/{analysis['total_scenarios']}")
        print(f"Average Score: {analysis['average_performance_score']:.1f}/100")
    
    print("\nTest Scenarios:")
    for scenario_name, scenario_data in results["test_scenarios"].items():
        status = scenario_data.get("status", "UNKNOWN")
        score = scenario_data.get("performance_score", 0)
        status_icon = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
        print(f"  {scenario_name.replace('_', ' ').title()}: {status} ({score:.1f}/100) {status_icon}")
    
    critical_recommendations = [
        rec for rec in results.get("recommendations", [])
        if rec["priority"] == "CRITICAL"
    ]
    
    if critical_recommendations:
        print(f"\nCritical Issues ({len(critical_recommendations)}):")
        for rec in critical_recommendations:
            print(f"  🔴 {rec['category'].replace('_', ' ').title()}: {rec['recommendation']}")
    
    print("\n" + "="*60)
    
    return results['overall_status'] in ['EXCELLENT', 'GOOD', 'ACCEPTABLE']


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)