"""Generate comprehensive data quality reports for the pipeline."""

import asyncio
from datetime import datetime
from pathlib import Path
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.layout import Layout
from rich.text import Text
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots

from src.infrastructure.data_quality.validation_framework import (
    ValidationFramework, ValidationLevel, ValidationReport
)
from src.infrastructure.processors import ProcessorFactory, ProcessorType
from src.infrastructure.processors.base_processor import SourceConfig
from src.infrastructure.caching.cache_manager import CacheManager


class DataQualityReportGenerator:
    """Generate comprehensive data quality reports."""
    
    def __init__(self, output_dir: Path = None):
        self.console = Console()
        self.output_dir = output_dir or Path("reports/data_quality")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.validator = ValidationFramework()
        
    async def analyze_data_source(
        self, 
        processor_type: ProcessorType,
        data: pd.DataFrame = None
    ) -> Dict:
        """Analyze data quality for a specific source."""
        if data is None:
            # Load cached data if available
            cache_manager = CacheManager()
            factory = ProcessorFactory(cache_manager=cache_manager, validator=self.validator)
            
            config = ProcessorConfig(
                processor_type=processor_type,
                source_config=SourceConfig(
                    source_id=f"{processor_type.value}_quality",
                    name=f"{processor_type.value} Quality Check",
                    description="Quality analysis"
                )
            )
            
            processor = factory.create_processor(config)
            result = await processor.process()
            
            if not result.success or result.data is None:
                return {
                    'source': processor_type.value,
                    'status': 'failed',
                    'error': result.errors[0] if result.errors else "No data available"
                }
            
            data = result.data
        
        # Perform quality analysis
        analysis = {
            'source': processor_type.value,
            'status': 'success',
            'record_count': len(data),
            'columns': list(data.columns),
            'missing_values': self._analyze_missing_values(data),
            'duplicates': self._analyze_duplicates(data),
            'outliers': self._analyze_outliers(data),
            'data_types': self._analyze_data_types(data),
            'temporal_coverage': self._analyze_temporal_coverage(data),
            'spatial_coverage': self._analyze_spatial_coverage(data),
            'quality_score': 0.0  # Will be calculated
        }
        
        # Calculate overall quality score
        analysis['quality_score'] = self._calculate_quality_score(analysis)
        
        return analysis
    
    def _analyze_missing_values(self, data: pd.DataFrame) -> Dict:
        """Analyze missing values in the dataset."""
        missing_counts = data.isnull().sum()
        missing_pct = (missing_counts / len(data)) * 100
        
        return {
            'total_missing': int(missing_counts.sum()),
            'by_column': {
                col: {
                    'count': int(missing_counts[col]),
                    'percentage': float(missing_pct[col])
                }
                for col in data.columns
                if missing_counts[col] > 0
            },
            'completeness': float(100 - missing_pct.mean())
        }
    
    def _analyze_duplicates(self, data: pd.DataFrame) -> Dict:
        """Analyze duplicate records."""
        # Check for exact duplicates
        exact_dupes = data.duplicated().sum()
        
        # Check for key duplicates (if identifiable columns exist)
        key_columns = []
        if 'market' in data.columns and 'date' in data.columns:
            key_columns = ['market', 'date']
        elif 'location' in data.columns and 'date' in data.columns:
            key_columns = ['location', 'date']
        
        key_dupes = 0
        if key_columns:
            key_dupes = data.duplicated(subset=key_columns).sum()
        
        return {
            'exact_duplicates': int(exact_dupes),
            'key_duplicates': int(key_dupes),
            'key_columns': key_columns,
            'uniqueness_score': float(100 * (1 - exact_dupes / len(data))) if len(data) > 0 else 100.0
        }
    
    def _analyze_outliers(self, data: pd.DataFrame) -> Dict:
        """Analyze outliers in numerical columns."""
        outliers = {}
        
        numeric_cols = data.select_dtypes(include=[np.number]).columns
        
        for col in numeric_cols:
            if col in data.columns:
                col_data = data[col].dropna()
                if len(col_data) > 0:
                    q1 = col_data.quantile(0.25)
                    q3 = col_data.quantile(0.75)
                    iqr = q3 - q1
                    
                    lower_bound = q1 - 1.5 * iqr
                    upper_bound = q3 + 1.5 * iqr
                    
                    outlier_count = ((col_data < lower_bound) | (col_data > upper_bound)).sum()
                    
                    outliers[col] = {
                        'count': int(outlier_count),
                        'percentage': float(100 * outlier_count / len(col_data)),
                        'lower_bound': float(lower_bound),
                        'upper_bound': float(upper_bound)
                    }
        
        return outliers
    
    def _analyze_data_types(self, data: pd.DataFrame) -> Dict:
        """Analyze data types and consistency."""
        type_info = {}
        
        for col in data.columns:
            dtype = str(data[col].dtype)
            
            # Check for mixed types
            unique_types = data[col].apply(type).value_counts()
            mixed = len(unique_types) > 1
            
            type_info[col] = {
                'dtype': dtype,
                'mixed_types': mixed,
                'unique_values': int(data[col].nunique()),
                'sample_values': list(data[col].dropna().head(3).astype(str))
            }
        
        return type_info
    
    def _analyze_temporal_coverage(self, data: pd.DataFrame) -> Dict:
        """Analyze temporal coverage of the data."""
        date_columns = []
        
        # Find date columns
        for col in data.columns:
            if 'date' in col.lower() or 'time' in col.lower():
                try:
                    pd.to_datetime(data[col])
                    date_columns.append(col)
                except:
                    pass
        
        if not date_columns:
            return {'status': 'no_temporal_data'}
        
        # Use first date column found
        date_col = date_columns[0]
        dates = pd.to_datetime(data[date_col]).dropna()
        
        if len(dates) == 0:
            return {'status': 'no_valid_dates'}
        
        return {
            'status': 'ok',
            'date_column': date_col,
            'start_date': str(dates.min()),
            'end_date': str(dates.max()),
            'date_range_days': int((dates.max() - dates.min()).days),
            'unique_dates': int(dates.nunique()),
            'frequency': self._infer_frequency(dates),
            'gaps': self._find_temporal_gaps(dates)
        }
    
    def _analyze_spatial_coverage(self, data: pd.DataFrame) -> Dict:
        """Analyze spatial coverage of the data."""
        spatial_columns = []
        
        # Find spatial columns
        for col in data.columns:
            if any(term in col.lower() for term in ['market', 'location', 'governorate', 'district', 'lat', 'lon']):
                spatial_columns.append(col)
        
        if not spatial_columns:
            return {'status': 'no_spatial_data'}
        
        coverage = {
            'status': 'ok',
            'spatial_columns': spatial_columns,
            'coverage_by_column': {}
        }
        
        for col in spatial_columns:
            if col in data.columns:
                unique_locations = data[col].dropna().unique()
                coverage['coverage_by_column'][col] = {
                    'unique_count': len(unique_locations),
                    'sample_values': list(unique_locations[:5])
                }
        
        return coverage
    
    def _infer_frequency(self, dates: pd.Series) -> str:
        """Infer the frequency of temporal data."""
        if len(dates) < 2:
            return "unknown"
        
        # Calculate mode of differences
        diffs = dates.sort_values().diff().dropna()
        if len(diffs) == 0:
            return "unknown"
        
        mode_diff = diffs.mode()[0] if len(diffs.mode()) > 0 else diffs.median()
        
        if mode_diff.days <= 1:
            return "daily"
        elif mode_diff.days <= 7:
            return "weekly"
        elif mode_diff.days <= 31:
            return "monthly"
        elif mode_diff.days <= 92:
            return "quarterly"
        else:
            return "irregular"
    
    def _find_temporal_gaps(self, dates: pd.Series, threshold_days: int = 7) -> List[Dict]:
        """Find gaps in temporal coverage."""
        sorted_dates = dates.sort_values().unique()
        gaps = []
        
        for i in range(1, len(sorted_dates)):
            diff = (sorted_dates[i] - sorted_dates[i-1]).days
            if diff > threshold_days:
                gaps.append({
                    'start': str(sorted_dates[i-1]),
                    'end': str(sorted_dates[i]),
                    'gap_days': diff
                })
        
        return gaps[:5]  # Return top 5 gaps
    
    def _calculate_quality_score(self, analysis: Dict) -> float:
        """Calculate overall data quality score."""
        scores = []
        
        # Completeness score
        if 'missing_values' in analysis:
            scores.append(analysis['missing_values']['completeness'] / 100)
        
        # Uniqueness score
        if 'duplicates' in analysis:
            scores.append(analysis['duplicates']['uniqueness_score'] / 100)
        
        # Consistency score (based on outliers)
        if 'outliers' in analysis and analysis['outliers']:
            outlier_pcts = [v['percentage'] for v in analysis['outliers'].values()]
            avg_outlier_pct = sum(outlier_pcts) / len(outlier_pcts) if outlier_pcts else 0
            scores.append(1 - (avg_outlier_pct / 100))
        
        # Temporal coverage score
        if 'temporal_coverage' in analysis and analysis['temporal_coverage'].get('status') == 'ok':
            # Simple scoring based on gap presence
            gap_count = len(analysis['temporal_coverage'].get('gaps', []))
            scores.append(max(0, 1 - (gap_count * 0.1)))
        
        # Calculate weighted average
        return float(sum(scores) / len(scores) * 100) if scores else 0.0
    
    def generate_quality_dashboard(self, analyses: List[Dict]):
        """Generate interactive quality dashboard."""
        # Create subplots
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('Quality Scores by Source', 'Missing Data Analysis',
                          'Temporal Coverage', 'Record Counts'),
            specs=[[{'type': 'bar'}, {'type': 'bar'}],
                   [{'type': 'scatter'}, {'type': 'bar'}]]
        )
        
        # Quality scores
        sources = [a['source'] for a in analyses if a['status'] == 'success']
        scores = [a['quality_score'] for a in analyses if a['status'] == 'success']
        
        fig.add_trace(
            go.Bar(x=sources, y=scores, name='Quality Score'),
            row=1, col=1
        )
        
        # Missing data
        missing_pcts = []
        for a in analyses:
            if a['status'] == 'success' and 'missing_values' in a:
                missing_pcts.append(100 - a['missing_values']['completeness'])
            else:
                missing_pcts.append(0)
        
        fig.add_trace(
            go.Bar(x=sources, y=missing_pcts, name='Missing %'),
            row=1, col=2
        )
        
        # Temporal coverage (if available)
        temporal_data = []
        for a in analyses:
            if (a['status'] == 'success' and 
                'temporal_coverage' in a and 
                a['temporal_coverage'].get('status') == 'ok'):
                temporal_data.append({
                    'source': a['source'],
                    'days': a['temporal_coverage']['date_range_days'],
                    'dates': a['temporal_coverage']['unique_dates']
                })
        
        if temporal_data:
            fig.add_trace(
                go.Scatter(
                    x=[d['days'] for d in temporal_data],
                    y=[d['dates'] for d in temporal_data],
                    mode='markers+text',
                    text=[d['source'] for d in temporal_data],
                    textposition='top center',
                    name='Coverage'
                ),
                row=2, col=1
            )
        
        # Record counts
        record_counts = [a.get('record_count', 0) for a in analyses if a['status'] == 'success']
        
        fig.add_trace(
            go.Bar(x=sources, y=record_counts, name='Records'),
            row=2, col=2
        )
        
        # Update layout
        fig.update_layout(
            title='Data Quality Dashboard',
            showlegend=False,
            height=800
        )
        
        # Save dashboard
        dashboard_path = self.output_dir / f"quality_dashboard_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
        fig.write_html(str(dashboard_path))
        
        return dashboard_path
    
    def generate_text_report(self, analyses: List[Dict]) -> Path:
        """Generate detailed text report."""
        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append("DATA QUALITY ASSESSMENT REPORT")
        report_lines.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("=" * 80)
        report_lines.append("")
        
        # Summary
        successful = [a for a in analyses if a['status'] == 'success']
        avg_quality = sum(a['quality_score'] for a in successful) / len(successful) if successful else 0
        
        report_lines.append("EXECUTIVE SUMMARY")
        report_lines.append("-" * 40)
        report_lines.append(f"Data Sources Analyzed: {len(analyses)}")
        report_lines.append(f"Successful Analyses: {len(successful)}")
        report_lines.append(f"Average Quality Score: {avg_quality:.1f}%")
        report_lines.append("")
        
        # Detailed analysis per source
        for analysis in analyses:
            report_lines.append(f"\n{analysis['source'].upper()} DATA ANALYSIS")
            report_lines.append("-" * 40)
            
            if analysis['status'] != 'success':
                report_lines.append(f"Status: FAILED - {analysis.get('error', 'Unknown error')}")
                continue
            
            report_lines.append(f"Quality Score: {analysis['quality_score']:.1f}%")
            report_lines.append(f"Total Records: {analysis['record_count']:,}")
            report_lines.append(f"Columns: {len(analysis['columns'])}")
            
            # Missing values
            if 'missing_values' in analysis:
                report_lines.append(f"\nMissing Data:")
                report_lines.append(f"  - Completeness: {analysis['missing_values']['completeness']:.1f}%")
                report_lines.append(f"  - Total Missing: {analysis['missing_values']['total_missing']:,}")
                
                if analysis['missing_values']['by_column']:
                    report_lines.append("  - By Column:")
                    for col, info in list(analysis['missing_values']['by_column'].items())[:5]:
                        report_lines.append(f"    * {col}: {info['percentage']:.1f}%")
            
            # Duplicates
            if 'duplicates' in analysis:
                report_lines.append(f"\nDuplicate Analysis:")
                report_lines.append(f"  - Exact Duplicates: {analysis['duplicates']['exact_duplicates']:,}")
                report_lines.append(f"  - Uniqueness Score: {analysis['duplicates']['uniqueness_score']:.1f}%")
            
            # Temporal coverage
            if 'temporal_coverage' in analysis and analysis['temporal_coverage'].get('status') == 'ok':
                tc = analysis['temporal_coverage']
                report_lines.append(f"\nTemporal Coverage:")
                report_lines.append(f"  - Date Range: {tc['start_date']} to {tc['end_date']}")
                report_lines.append(f"  - Duration: {tc['date_range_days']} days")
                report_lines.append(f"  - Frequency: {tc['frequency']}")
                
                if tc['gaps']:
                    report_lines.append(f"  - Gaps Found: {len(tc['gaps'])}")
            
            report_lines.append("")
        
        # Recommendations
        report_lines.append("\nRECOMMENDATIONS")
        report_lines.append("-" * 40)
        
        # Generate recommendations based on findings
        for analysis in successful:
            if analysis['quality_score'] < 80:
                report_lines.append(f"\n{analysis['source']}:")
                
                if analysis['missing_values']['completeness'] < 90:
                    report_lines.append("  - Address missing data issues")
                
                if analysis['duplicates']['exact_duplicates'] > 0:
                    report_lines.append("  - Remove duplicate records")
                
                if 'outliers' in analysis and any(v['percentage'] > 5 for v in analysis['outliers'].values()):
                    report_lines.append("  - Investigate and handle outliers")
        
        # Save report
        report_path = self.output_dir / f"quality_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(report_path, 'w') as f:
            f.write('\n'.join(report_lines))
        
        return report_path
    
    def display_summary(self, analyses: List[Dict]):
        """Display summary in console."""
        # Create summary table
        table = Table(title="Data Quality Summary")
        table.add_column("Data Source", style="cyan")
        table.add_column("Status", style="green")
        table.add_column("Quality Score", justify="right")
        table.add_column("Records", justify="right")
        table.add_column("Completeness", justify="right")
        
        for analysis in analyses:
            status = "✓" if analysis['status'] == 'success' else "✗"
            quality = f"{analysis.get('quality_score', 0):.1f}%" if analysis['status'] == 'success' else "N/A"
            records = f"{analysis.get('record_count', 0):,}" if analysis['status'] == 'success' else "N/A"
            completeness = f"{analysis.get('missing_values', {}).get('completeness', 0):.1f}%" if analysis['status'] == 'success' else "N/A"
            
            table.add_row(
                analysis['source'],
                status,
                quality,
                records,
                completeness
            )
        
        self.console.print(table)


async def main():
    """Run data quality analysis."""
    console = Console()
    generator = DataQualityReportGenerator()
    
    console.print("[bold cyan]Yemen Market Integration - Data Quality Assessment[/bold cyan]\n")
    
    # Analyze all data sources
    processor_types = [
        ProcessorType.PRICE,
        ProcessorType.CONFLICT,
        ProcessorType.CLIMATE,
        ProcessorType.POPULATION,
        ProcessorType.INFRASTRUCTURE,
        ProcessorType.AID,
        ProcessorType.CONTROL_ZONES,
        ProcessorType.GLOBAL_PRICES,
        ProcessorType.FOOD_SECURITY
    ]
    
    analyses = []
    
    with console.status("Analyzing data quality...") as status:
        for proc_type in processor_types:
            status.update(f"Analyzing {proc_type.value}...")
            analysis = await generator.analyze_data_source(proc_type)
            analyses.append(analysis)
    
    # Display summary
    generator.display_summary(analyses)
    
    # Generate reports
    console.print("\n[yellow]Generating reports...[/yellow]")
    
    text_report = generator.generate_text_report(analyses)
    console.print(f"✓ Text report: {text_report}")
    
    dashboard = generator.generate_quality_dashboard(analyses)
    console.print(f"✓ Interactive dashboard: {dashboard}")
    
    # Overall assessment
    successful = [a for a in analyses if a['status'] == 'success']
    avg_quality = sum(a['quality_score'] for a in successful) / len(successful) if successful else 0
    
    console.print(f"\n[bold]Overall Data Quality Score: {avg_quality:.1f}%[/bold]")
    
    if avg_quality >= 90:
        console.print("[green]✓ Excellent data quality - ready for analysis[/green]")
    elif avg_quality >= 80:
        console.print("[yellow]⚠ Good data quality - minor improvements recommended[/yellow]")
    elif avg_quality >= 70:
        console.print("[orange1]⚠ Fair data quality - significant improvements needed[/orange1]")
    else:
        console.print("[red]✗ Poor data quality - major issues must be addressed[/red]")


if __name__ == "__main__":
    asyncio.run(main())