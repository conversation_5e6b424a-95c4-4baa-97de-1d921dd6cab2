"""Benchmark pipeline performance to identify bottlenecks."""

import asyncio
import time
import psutil
import tracemalloc
from datetime import datetime
from pathlib import Path
import pandas as pd
from typing import Dict, List, Tuple
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn

from src.infrastructure.processors import ProcessorFactory, ProcessorConfig, ProcessorType
from src.infrastructure.processors.base_processor import SourceConfig
from src.infrastructure.data_quality.validation_framework import ValidationFramework, ValidationLevel
from src.infrastructure.caching.cache_manager import CacheManager
from src.application.services.data_pipeline_orchestrator import DataPipelineOrchestrator, PipelineConfig


class PipelineBenchmark:
    """Benchmark pipeline performance."""
    
    def __init__(self, cache_dir: Path = None):
        self.console = Console()
        self.cache_dir = cache_dir or Path.home() / '.cache' / 'yemen_market_pipeline'
        self.results: Dict[str, Dict] = {}
        
    async def benchmark_processor(
        self, 
        processor_type: ProcessorType,
        cache_manager: CacheManager,
        validator: ValidationFramework
    ) -> Dict:
        """Benchmark a single processor."""
        # Start memory tracking
        tracemalloc.start()
        start_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        
        # Create processor
        factory = ProcessorFactory(cache_manager=cache_manager, validator=validator)
        config = ProcessorConfig(
            processor_type=processor_type,
            source_config=SourceConfig(
                source_id=f"{processor_type.value}_bench",
                name=f"{processor_type.value} Benchmark",
                description="Benchmark processor",
                validation_level=ValidationLevel.STANDARD
            )
        )
        
        processor = factory.create_processor(config)
        
        # Measure processing time
        start_time = time.time()
        
        try:
            result = await processor.process()
            success = result.success if result else False
            error = None
            record_count = len(result.data) if result and hasattr(result.data, '__len__') else 0
        except Exception as e:
            success = False
            error = str(e)
            record_count = 0
        
        end_time = time.time()
        
        # Get memory stats
        current_memory = psutil.Process().memory_info().rss / 1024 / 1024
        peak_memory = tracemalloc.get_traced_memory()[1] / 1024 / 1024  # Peak memory in MB
        tracemalloc.stop()
        
        return {
            'processor': processor_type.value,
            'success': success,
            'duration': end_time - start_time,
            'memory_start': start_memory,
            'memory_end': current_memory,
            'memory_peak': peak_memory,
            'memory_used': current_memory - start_memory,
            'record_count': record_count,
            'error': error
        }
    
    async def benchmark_all_processors(self) -> List[Dict]:
        """Benchmark all processors."""
        self.console.print("\n[bold cyan]Benchmarking All Processors[/bold cyan]\n")
        
        # Setup
        cache_manager = CacheManager(cache_dir=self.cache_dir)
        validator = ValidationFramework()
        
        # Processors to benchmark
        processor_types = [
            ProcessorType.PRICE,
            ProcessorType.CONFLICT,
            ProcessorType.CLIMATE,
            ProcessorType.POPULATION,
            ProcessorType.INFRASTRUCTURE,
            ProcessorType.AID,
            ProcessorType.CONTROL_ZONES,
            ProcessorType.GLOBAL_PRICES,
            ProcessorType.FOOD_SECURITY
        ]
        
        results = []
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TimeElapsedColumn(),
            console=self.console
        ) as progress:
            
            task = progress.add_task(
                "Benchmarking processors...", 
                total=len(processor_types)
            )
            
            for proc_type in processor_types:
                progress.update(
                    task, 
                    description=f"Benchmarking {proc_type.value}..."
                )
                
                result = await self.benchmark_processor(
                    proc_type, 
                    cache_manager, 
                    validator
                )
                results.append(result)
                
                progress.advance(task)
        
        return results
    
    async def benchmark_parallel_execution(self) -> Dict:
        """Benchmark parallel vs sequential execution."""
        self.console.print("\n[bold cyan]Benchmarking Parallel Execution[/bold cyan]\n")
        
        cache_manager = CacheManager(cache_dir=self.cache_dir)
        validator = ValidationFramework()
        
        # Select fast processors for testing
        processor_types = [
            ProcessorType.CLIMATE,
            ProcessorType.POPULATION,
            ProcessorType.INFRASTRUCTURE
        ]
        
        # Sequential execution
        self.console.print("Running sequential execution...")
        seq_start = time.time()
        seq_results = []
        
        for proc_type in processor_types:
            result = await self.benchmark_processor(proc_type, cache_manager, validator)
            seq_results.append(result)
        
        seq_duration = time.time() - seq_start
        
        # Parallel execution
        self.console.print("Running parallel execution...")
        par_start = time.time()
        
        tasks = [
            self.benchmark_processor(proc_type, cache_manager, validator)
            for proc_type in processor_types
        ]
        par_results = await asyncio.gather(*tasks)
        
        par_duration = time.time() - par_start
        
        return {
            'sequential': {
                'duration': seq_duration,
                'results': seq_results
            },
            'parallel': {
                'duration': par_duration,
                'results': par_results
            },
            'speedup': seq_duration / par_duration if par_duration > 0 else 0
        }
    
    async def benchmark_full_pipeline(self) -> Dict:
        """Benchmark full pipeline execution."""
        self.console.print("\n[bold cyan]Benchmarking Full Pipeline[/bold cyan]\n")
        
        # Setup
        config = PipelineConfig(
            start_date=datetime(2023, 1, 1),
            end_date=datetime(2023, 3, 31),
            data_sources={
                'wfp': {'enabled': True, 'priority': 1},
                'climate': {'enabled': True, 'priority': 2},
                'conflict': {'enabled': True, 'priority': 3}
            },
            output_format='parquet',
            validation_level=ValidationLevel.STANDARD,
            enable_caching=True,
            parallel_downloads=3
        )
        
        cache_manager = CacheManager(cache_dir=self.cache_dir)
        
        # Create orchestrator
        orchestrator = DataPipelineOrchestrator(
            config=config,
            cache_manager=cache_manager
        )
        
        # Measure pipeline execution
        tracemalloc.start()
        start_memory = psutil.Process().memory_info().rss / 1024 / 1024
        start_time = time.time()
        
        try:
            result = await orchestrator.run_pipeline()
            success = result.get('success', False)
            error = None
        except Exception as e:
            success = False
            error = str(e)
            result = None
        
        end_time = time.time()
        end_memory = psutil.Process().memory_info().rss / 1024 / 1024
        peak_memory = tracemalloc.get_traced_memory()[1] / 1024 / 1024
        tracemalloc.stop()
        
        return {
            'success': success,
            'duration': end_time - start_time,
            'memory_used': end_memory - start_memory,
            'memory_peak': peak_memory,
            'error': error,
            'stages_completed': len(result.get('completed_stages', [])) if result else 0
        }
    
    def display_results(self, processor_results: List[Dict], parallel_results: Dict, pipeline_results: Dict):
        """Display benchmark results in a nice table."""
        # Processor results table
        self.console.print("\n[bold green]Processor Benchmark Results[/bold green]")
        
        table = Table(title="Individual Processor Performance")
        table.add_column("Processor", style="cyan")
        table.add_column("Success", style="green")
        table.add_column("Duration (s)", justify="right")
        table.add_column("Memory (MB)", justify="right")
        table.add_column("Records", justify="right")
        
        for result in processor_results:
            table.add_row(
                result['processor'],
                "✓" if result['success'] else "✗",
                f"{result['duration']:.2f}",
                f"{result['memory_used']:.1f}",
                str(result['record_count'])
            )
        
        self.console.print(table)
        
        # Parallel execution results
        self.console.print("\n[bold green]Parallel Execution Results[/bold green]")
        
        par_table = Table(title="Sequential vs Parallel Performance")
        par_table.add_column("Execution Mode", style="cyan")
        par_table.add_column("Duration (s)", justify="right")
        par_table.add_column("Speedup", justify="right")
        
        par_table.add_row(
            "Sequential",
            f"{parallel_results['sequential']['duration']:.2f}",
            "1.0x"
        )
        par_table.add_row(
            "Parallel",
            f"{parallel_results['parallel']['duration']:.2f}",
            f"{parallel_results['speedup']:.2f}x"
        )
        
        self.console.print(par_table)
        
        # Pipeline results
        self.console.print("\n[bold green]Full Pipeline Results[/bold green]")
        
        pipeline_table = Table(title="Complete Pipeline Performance")
        pipeline_table.add_column("Metric", style="cyan")
        pipeline_table.add_column("Value", justify="right")
        
        pipeline_table.add_row("Success", "✓" if pipeline_results['success'] else "✗")
        pipeline_table.add_row("Duration", f"{pipeline_results['duration']:.2f}s")
        pipeline_table.add_row("Memory Used", f"{pipeline_results['memory_used']:.1f} MB")
        pipeline_table.add_row("Peak Memory", f"{pipeline_results['memory_peak']:.1f} MB")
        pipeline_table.add_row("Stages Completed", str(pipeline_results['stages_completed']))
        
        self.console.print(pipeline_table)
        
        # Save detailed results
        self.save_results(processor_results, parallel_results, pipeline_results)
    
    def save_results(self, processor_results: List[Dict], parallel_results: Dict, pipeline_results: Dict):
        """Save benchmark results to file."""
        results_dir = Path("results/benchmarks")
        results_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Create summary
        summary = {
            'timestamp': timestamp,
            'processor_benchmarks': processor_results,
            'parallel_execution': parallel_results,
            'full_pipeline': pipeline_results,
            'summary': {
                'total_processors': len(processor_results),
                'successful_processors': sum(1 for r in processor_results if r['success']),
                'average_processor_time': sum(r['duration'] for r in processor_results) / len(processor_results),
                'total_memory_used': sum(r['memory_used'] for r in processor_results),
                'parallel_speedup': parallel_results['speedup'],
                'pipeline_duration': pipeline_results['duration']
            }
        }
        
        # Save as JSON
        import json
        output_path = results_dir / f"benchmark_{timestamp}.json"
        with open(output_path, 'w') as f:
            json.dump(summary, f, indent=2, default=str)
        
        self.console.print(f"\n[green]Results saved to: {output_path}[/green]")


async def main():
    """Run pipeline benchmarks."""
    benchmark = PipelineBenchmark()
    
    # Run benchmarks
    processor_results = await benchmark.benchmark_all_processors()
    parallel_results = await benchmark.benchmark_parallel_execution()
    pipeline_results = await benchmark.benchmark_full_pipeline()
    
    # Display results
    benchmark.display_results(processor_results, parallel_results, pipeline_results)


if __name__ == "__main__":
    asyncio.run(main())