#!/usr/bin/env python3
"""Script to migrate data from old pipeline to new Data Pipeline V2."""

import asyncio
import sys
from pathlib import Path
from datetime import datetime
import typer
from rich.console import Console
from rich.table import Table
from rich.progress import track
from rich import print as rprint
from typing import Optional
import json

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from src.infrastructure.migration.pipeline_migrator import (
    PipelineMigrator,
    MigrationConfig,
    MigrationStrategy,
    MigrationState
)
from src.infrastructure.observability.structured_logging import setup_logging


app = typer.Typer()
console = Console()
setup_logging()


@app.command()
def migrate(
    strategy: str = typer.Option(
        "full",
        "--strategy", "-s",
        help="Migration strategy: full, incremental, parallel, rollback"
    ),
    backup: bool = typer.Option(
        True,
        "--backup/--no-backup", "-b",
        help="Create backup before migration"
    ),
    validate: bool = typer.Option(
        True,
        "--validate/--no-validate", "-v",
        help="Validate data after migration"
    ),
    parallel_validation: bool = typer.Option(
        False,
        "--parallel-validation", "-p",
        help="Run parallel validation with old pipeline"
    ),
    old_data_path: Optional[str] = typer.Option(
        None,
        "--old-data", "-o",
        help="Path to old data directory"
    ),
    new_data_path: Optional[str] = typer.Option(
        None,
        "--new-data", "-n",
        help="Path to new data directory"
    ),
    batch_size: int = typer.Option(
        10000,
        "--batch-size",
        help="Batch size for processing"
    ),
    workers: int = typer.Option(
        4,
        "--workers", "-w",
        help="Number of parallel workers"
    ),
    dry_run: bool = typer.Option(
        False,
        "--dry-run", "-d",
        help="Perform dry run without actual migration"
    )
):
    """Migrate data from old pipeline to new Data Pipeline V2."""
    
    console.print("[bold blue]Yemen Market Integration - Data Pipeline Migration[/bold blue]")
    console.print(f"Starting migration at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # Convert strategy string to enum
        migration_strategy = MigrationStrategy[strategy.upper()]
    except KeyError:
        console.print(f"[red]Invalid strategy: {strategy}[/red]")
        console.print("Valid strategies: full, incremental, parallel, rollback")
        raise typer.Exit(1)
        
    # Create migration config
    config = MigrationConfig(
        strategy=migration_strategy,
        backup_enabled=backup,
        validation_enabled=validate,
        parallel_validation=parallel_validation,
        batch_size=batch_size,
        max_workers=workers
    )
    
    # Override paths if provided
    if old_data_path:
        config.old_data_path = Path(old_data_path)
    if new_data_path:
        config.new_data_path = Path(new_data_path)
        
    # Display configuration
    display_config(config)
    
    if dry_run:
        console.print("\n[yellow]DRY RUN MODE - No actual changes will be made[/yellow]")
        analyze_migration_scope(config)
        raise typer.Exit(0)
        
    # Confirm migration
    if not typer.confirm("\nProceed with migration?"):
        console.print("[red]Migration cancelled[/red]")
        raise typer.Exit(0)
        
    # Run migration
    asyncio.run(run_migration(config))


async def run_migration(config: MigrationConfig) -> None:
    """Execute the migration process."""
    migrator = PipelineMigrator(config)
    
    # Add progress callback
    def progress_callback(state: MigrationState):
        display_progress(state)
    
    try:
        # Run migration
        with console.status("[bold green]Running migration...") as status:
            state = await migrator.migrate()
            
        # Display results
        display_results(state)
        
        # Save state to file
        state_file = Path(f"migration_state_{state.migration_id}.json")
        with open(state_file, "w") as f:
            json.dump({
                "migration_id": state.migration_id,
                "status": state.status,
                "start_time": state.start_time.isoformat(),
                "end_time": state.end_time.isoformat() if state.end_time else None,
                "records_processed": state.records_processed,
                "records_migrated": state.records_migrated,
                "records_failed": state.records_failed,
                "validation_passed": state.validation_passed,
                "errors": state.errors,
                "warnings": state.warnings
            }, f, indent=2)
            
        console.print(f"\n[green]Migration state saved to: {state_file}[/green]")
        
        # Exit with appropriate code
        if state.status == "completed":
            raise typer.Exit(0)
        else:
            raise typer.Exit(1)
            
    except Exception as e:
        console.print(f"\n[red]Migration failed: {str(e)}[/red]")
        raise typer.Exit(1)


def display_config(config: MigrationConfig) -> None:
    """Display migration configuration."""
    table = Table(title="Migration Configuration")
    table.add_column("Setting", style="cyan")
    table.add_column("Value", style="yellow")
    
    table.add_row("Strategy", config.strategy.value)
    table.add_row("Backup Enabled", str(config.backup_enabled))
    table.add_row("Validation Enabled", str(config.validation_enabled))
    table.add_row("Parallel Validation", str(config.parallel_validation))
    table.add_row("Old Data Path", str(config.old_data_path))
    table.add_row("New Data Path", str(config.new_data_path))
    table.add_row("Batch Size", f"{config.batch_size:,}")
    table.add_row("Workers", str(config.max_workers))
    table.add_row("Memory Limit", f"{config.memory_limit_gb} GB")
    
    console.print(table)


def analyze_migration_scope(config: MigrationConfig) -> None:
    """Analyze and display migration scope."""
    console.print("\n[bold]Analyzing migration scope...[/bold]")
    
    # Check old data
    if config.old_data_path.exists():
        old_files = list(config.old_data_path.rglob("*.parquet"))
        old_size = sum(f.stat().st_size for f in old_files) / (1024**3)  # GB
        
        console.print(f"\nOld data directory: {config.old_data_path}")
        console.print(f"  Files found: {len(old_files)}")
        console.print(f"  Total size: {old_size:.2f} GB")
        
        # List key files
        console.print("\n  Key files:")
        for pattern in ["**/integrated_panel/*.parquet", "**/price*.parquet", "**/exchange*.parquet"]:
            matches = list(config.old_data_path.glob(pattern))
            if matches:
                for match in matches[:3]:  # Show first 3
                    console.print(f"    - {match.relative_to(config.old_data_path)}")
    else:
        console.print(f"\n[red]Old data directory not found: {config.old_data_path}[/red]")
        
    # Check new data directory
    if config.new_data_path.exists():
        console.print(f"\n[yellow]Warning: New data directory already exists: {config.new_data_path}[/yellow]")
        new_files = list(config.new_data_path.rglob("*.parquet"))
        if new_files:
            console.print(f"  Existing files: {len(new_files)}")


def display_progress(state: MigrationState) -> None:
    """Display migration progress."""
    # This would be called by progress callback
    # For now, just update the console
    if state.current_phase:
        console.print(f"Phase: {state.current_phase} - Progress: {state.progress_pct:.1f}%")


def display_results(state: MigrationState) -> None:
    """Display migration results."""
    console.print("\n[bold]Migration Results[/bold]")
    
    # Status
    status_color = "green" if state.status == "completed" else "red"
    console.print(f"Status: [{status_color}]{state.status}[/{status_color}]")
    
    # Duration
    if state.end_time:
        duration = state.end_time - state.start_time
        console.print(f"Duration: {duration}")
        
    # Statistics table
    table = Table(title="Migration Statistics")
    table.add_column("Metric", style="cyan")
    table.add_column("Value", style="yellow")
    
    table.add_row("Records Processed", f"{state.records_processed:,}")
    table.add_row("Records Migrated", f"{state.records_migrated:,}")
    table.add_row("Records Failed", f"{state.records_failed:,}")
    
    if state.records_processed > 0:
        success_rate = (state.records_migrated / state.records_processed) * 100
        table.add_row("Success Rate", f"{success_rate:.1f}%")
        
    table.add_row("Validation", "PASSED" if state.validation_passed else "FAILED")
    
    console.print(table)
    
    # Errors and warnings
    if state.errors:
        console.print("\n[red]Errors:[/red]")
        for error in state.errors[:5]:  # Show first 5
            console.print(f"  • {error}")
        if len(state.errors) > 5:
            console.print(f"  ... and {len(state.errors) - 5} more")
            
    if state.warnings:
        console.print("\n[yellow]Warnings:[/yellow]")
        for warning in state.warnings[:5]:  # Show first 5
            console.print(f"  • {warning}")
        if len(state.warnings) > 5:
            console.print(f"  ... and {len(state.warnings) - 5} more")
            
    # Validation details
    if state.validation_details:
        console.print("\n[bold]Validation Details:[/bold]")
        for dataset, details in state.validation_details.items():
            status = details.get('status', 'unknown')
            color = "green" if status == "passed" else "red"
            console.print(f"  {dataset}: [{color}]{status}[/{color}]")


@app.command()
def status(
    migration_id: Optional[str] = typer.Argument(
        None,
        help="Migration ID to check status for"
    )
):
    """Check status of a migration."""
    if migration_id:
        state_file = Path(f"migration_state_{migration_id}.json")
    else:
        # Find most recent migration
        state_files = list(Path.cwd().glob("migration_state_*.json"))
        if not state_files:
            console.print("[red]No migration state files found[/red]")
            raise typer.Exit(1)
        state_file = max(state_files, key=lambda p: p.stat().st_mtime)
        
    if not state_file.exists():
        console.print(f"[red]Migration state file not found: {state_file}[/red]")
        raise typer.Exit(1)
        
    # Load and display state
    with open(state_file) as f:
        state_data = json.load(f)
        
    console.print(f"\n[bold]Migration Status: {state_data['migration_id']}[/bold]")
    console.print(f"Status: {state_data['status']}")
    console.print(f"Started: {state_data['start_time']}")
    console.print(f"Ended: {state_data.get('end_time', 'In progress')}")
    console.print(f"Records Migrated: {state_data['records_migrated']:,}")
    console.print(f"Validation: {'PASSED' if state_data['validation_passed'] else 'FAILED'}")


@app.command()
def rollback(
    migration_id: str = typer.Argument(
        ...,
        help="Migration ID to rollback"
    ),
    force: bool = typer.Option(
        False,
        "--force", "-f",
        help="Force rollback without confirmation"
    )
):
    """Rollback a migration."""
    console.print(f"[yellow]Preparing to rollback migration: {migration_id}[/yellow]")
    
    # Create config for rollback
    config = MigrationConfig(strategy=MigrationStrategy.ROLLBACK)
    
    if not force and not typer.confirm("\nAre you sure you want to rollback this migration?"):
        console.print("[red]Rollback cancelled[/red]")
        raise typer.Exit(0)
        
    # This would trigger the rollback process
    console.print("[green]Rollback functionality will be implemented by the migrator[/green]")


if __name__ == "__main__":
    app()