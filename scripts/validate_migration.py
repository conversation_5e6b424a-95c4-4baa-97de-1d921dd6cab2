#!/usr/bin/env python3
"""Script to validate migration by comparing old and new pipeline outputs."""

import asyncio
import sys
from pathlib import Path
from datetime import datetime
import typer
from rich.console import Console
from rich.table import Table
from rich import print as rprint
from typing import Optional, List
import json

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from src.infrastructure.migration.parallel_validator import (
    ParallelValidator,
    ParallelValidationConfig,
    DatasetComparison
)
from src.application.services.data_pipeline_orchestrator import PipelineConfig
from src.infrastructure.observability.structured_logging import setup_logging


app = typer.Typer()
console = Console()
setup_logging()


@app.command()
def validate(
    datasets: Optional[List[str]] = typer.Option(
        None,
        "--dataset", "-d",
        help="Specific datasets to validate (can be used multiple times)"
    ),
    old_path: str = typer.Option(
        "data/processed",
        "--old-path", "-o",
        help="Path to old pipeline data"
    ),
    new_path: str = typer.Option(
        "data/processed_v2",
        "--new-path", "-n",
        help="Path to new pipeline data"
    ),
    tolerance: float = typer.Option(
        0.01,
        "--tolerance", "-t",
        help="Row count tolerance (default: 1%)"
    ),
    correlation_threshold: float = typer.Option(
        0.99,
        "--correlation", "-c",
        help="Value correlation threshold (default: 0.99)"
    ),
    workers: int = typer.Option(
        4,
        "--workers", "-w",
        help="Number of parallel workers"
    ),
    report_format: str = typer.Option(
        "html",
        "--format", "-f",
        help="Report format: html, json, markdown"
    ),
    output_dir: Optional[str] = typer.Option(
        None,
        "--output", "-o",
        help="Output directory for reports"
    ),
    run_pipelines: bool = typer.Option(
        False,
        "--run-pipelines", "-r",
        help="Run both pipelines before comparison"
    )
):
    """Validate migration by comparing old and new pipeline outputs."""
    
    console.print("[bold blue]Yemen Market Integration - Pipeline Validation[/bold blue]")
    console.print(f"Starting validation at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Create validation config
    config = ParallelValidationConfig(
        row_count_tolerance=tolerance,
        value_correlation_threshold=correlation_threshold,
        parallel_workers=workers,
        report_format=report_format,
        generate_reports=True
    )
    
    # Override datasets if specified
    if datasets:
        config.datasets_to_compare = datasets
        
    # Override output directory if specified
    if output_dir:
        config.report_output_dir = Path(output_dir)
        
    # Display configuration
    display_config(config, old_path, new_path)
    
    # Run validation
    if run_pipelines:
        asyncio.run(validate_with_pipeline_run(config, old_path, new_path))
    else:
        asyncio.run(validate_existing_data(config, old_path, new_path))


async def validate_with_pipeline_run(
    config: ParallelValidationConfig,
    old_base_path: str,
    new_base_path: str
) -> None:
    """Run both pipelines and validate outputs."""
    console.print("\n[yellow]Running both pipelines for comparison...[/yellow]")
    
    # Create pipeline configurations
    old_pipeline_config = PipelineConfig(
        name="old_pipeline",
        data_path=Path(old_base_path),
        # Add other config as needed
    )
    
    new_pipeline_config = PipelineConfig(
        name="new_pipeline", 
        data_path=Path(new_base_path),
        # Add other config as needed
    )
    
    # Create validator
    validator = ParallelValidator(config)
    
    try:
        # Run validation
        results = await validator.validate_pipelines(
            old_pipeline_config,
            new_pipeline_config
        )
        
        # Save results
        save_validation_results(results, config.report_output_dir)
        
    except Exception as e:
        console.print(f"\n[red]Validation failed: {str(e)}[/red]")
        raise typer.Exit(1)


async def validate_existing_data(
    config: ParallelValidationConfig,
    old_base_path: str,
    new_base_path: str
) -> None:
    """Validate existing data without running pipelines."""
    console.print("\n[yellow]Validating existing data...[/yellow]")
    
    old_base = Path(old_base_path)
    new_base = Path(new_base_path)
    
    # Check paths exist
    if not old_base.exists():
        console.print(f"[red]Old data path not found: {old_base}[/red]")
        raise typer.Exit(1)
        
    if not new_base.exists():
        console.print(f"[red]New data path not found: {new_base}[/red]")
        raise typer.Exit(1)
        
    # Create validator
    validator = ParallelValidator(config)
    
    # Validate each dataset
    all_results = {}
    
    for dataset_name in config.datasets_to_compare:
        console.print(f"\n[cyan]Validating {dataset_name}...[/cyan]")
        
        # Find data files
        old_file = find_dataset_file(old_base, dataset_name)
        new_file = find_dataset_file(new_base, dataset_name)
        
        if not old_file:
            console.print(f"[yellow]Skipping {dataset_name} - not found in old data[/yellow]")
            continue
            
        if not new_file:
            console.print(f"[yellow]Skipping {dataset_name} - not found in new data[/yellow]")
            continue
            
        try:
            # Compare specific datasets
            result = await validator.compare_specific_datasets(
                old_file,
                new_file,
                dataset_name
            )
            all_results[dataset_name] = result
            
        except Exception as e:
            console.print(f"[red]Failed to validate {dataset_name}: {str(e)}[/red]")
            
    # Display overall summary
    display_overall_summary(all_results)
    
    # Save results
    save_validation_results(all_results, config.report_output_dir)


def find_dataset_file(base_path: Path, dataset_name: str) -> Optional[Path]:
    """Find dataset file in directory structure."""
    # Common patterns for dataset files
    patterns = {
        "integrated_panel": ["**/integrated_panel/*.parquet", "**/yemen_integrated*.parquet"],
        "price_observations": ["**/price*.parquet", "**/wfp*.parquet"],
        "exchange_rates": ["**/exchange*.parquet"],
        "control_zones": ["**/control*.parquet", "**/zones*.parquet"],
        "conflict_events": ["**/conflict*.parquet", "**/acled*.parquet"]
    }
    
    search_patterns = patterns.get(dataset_name, [f"**/{dataset_name}*.parquet"])
    
    for pattern in search_patterns:
        matches = list(base_path.glob(pattern))
        if matches:
            # Return the most recent file
            return max(matches, key=lambda p: p.stat().st_mtime)
            
    return None


def display_config(
    config: ParallelValidationConfig,
    old_path: str,
    new_path: str
) -> None:
    """Display validation configuration."""
    table = Table(title="Validation Configuration")
    table.add_column("Setting", style="cyan")
    table.add_column("Value", style="yellow")
    
    table.add_row("Old Data Path", old_path)
    table.add_row("New Data Path", new_path)
    table.add_row("Datasets", ", ".join(config.datasets_to_compare))
    table.add_row("Row Count Tolerance", f"{config.row_count_tolerance * 100:.1f}%")
    table.add_row("Correlation Threshold", f"{config.value_correlation_threshold:.3f}")
    table.add_row("Workers", str(config.parallel_workers))
    table.add_row("Report Format", config.report_format)
    table.add_row("Output Directory", str(config.report_output_dir))
    
    console.print(table)


def display_overall_summary(results: Dict[str, DatasetComparison]) -> None:
    """Display overall validation summary."""
    console.print("\n[bold]Overall Validation Summary[/bold]")
    
    total_datasets = len(results)
    passed_datasets = sum(1 for r in results.values() if r.overall_match)
    
    # Calculate aggregate metrics
    total_checks = sum(len(r.comparison_results) for r in results.values())
    passed_checks = sum(
        sum(1 for c in r.comparison_results if c.passed)
        for r in results.values()
    )
    
    # Summary table
    table = Table()
    table.add_column("Metric", style="cyan")
    table.add_column("Value", style="yellow")
    
    table.add_row("Total Datasets", str(total_datasets))
    table.add_row("Passed Datasets", str(passed_datasets))
    table.add_row("Dataset Success Rate", f"{(passed_datasets/total_datasets*100):.1f}%")
    table.add_row("Total Checks", str(total_checks))
    table.add_row("Passed Checks", str(passed_checks))
    table.add_row("Check Success Rate", f"{(passed_checks/total_checks*100):.1f}%")
    
    console.print(table)
    
    # Overall result
    if passed_datasets == total_datasets:
        console.print("\n[bold green]✓ All datasets passed validation![/bold green]")
    else:
        console.print("\n[bold red]✗ Some datasets failed validation[/bold red]")
        
        # Show failed datasets
        failed = [name for name, result in results.items() if not result.overall_match]
        console.print(f"Failed datasets: {', '.join(failed)}")


def save_validation_results(
    results: Dict[str, DatasetComparison],
    output_dir: Path
) -> None:
    """Save validation results to file."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Convert results to serializable format
    results_data = {
        "validation_timestamp": timestamp,
        "summary": {
            "total_datasets": len(results),
            "passed_datasets": sum(1 for r in results.values() if r.overall_match),
            "datasets": {}
        }
    }
    
    for dataset_name, comparison in results.items():
        results_data["summary"]["datasets"][dataset_name] = {
            "passed": comparison.overall_match,
            "match_percentage": comparison.match_percentage,
            "recommendations": comparison.recommendations,
            "failed_checks": [
                r.metric.value for r in comparison.comparison_results
                if not r.passed
            ]
        }
        
    # Save summary
    summary_file = output_dir / f"validation_summary_{timestamp}.json"
    with open(summary_file, "w") as f:
        json.dump(results_data, f, indent=2)
        
    console.print(f"\n[green]Validation summary saved to: {summary_file}[/green]")


@app.command()
def compare(
    dataset: str = typer.Argument(
        ...,
        help="Dataset name to compare"
    ),
    old_file: str = typer.Argument(
        ...,
        help="Path to old dataset file"
    ),
    new_file: str = typer.Argument(
        ...,
        help="Path to new dataset file"
    ),
    detailed: bool = typer.Option(
        False,
        "--detailed", "-d",
        help="Show detailed comparison results"
    )
):
    """Compare specific dataset files directly."""
    console.print(f"\n[bold]Comparing {dataset} datasets[/bold]")
    console.print(f"Old: {old_file}")
    console.print(f"New: {new_file}")
    
    # Create default config
    config = ParallelValidationConfig(generate_reports=False)
    validator = ParallelValidator(config)
    
    # Run comparison
    result = asyncio.run(
        validator.compare_specific_datasets(
            Path(old_file),
            Path(new_file),
            dataset
        )
    )
    
    if detailed:
        display_detailed_results(result)


def display_detailed_results(comparison: DatasetComparison) -> None:
    """Display detailed comparison results."""
    console.print("\n[bold]Detailed Comparison Results[/bold]")
    
    for result in comparison.comparison_results:
        color = "green" if result.passed else "red"
        status = "✓" if result.passed else "✗"
        
        console.print(f"\n[{color}]{status} {result.metric.value}[/{color}]")
        
        if result.old_value is not None and result.new_value is not None:
            console.print(f"  Old: {result.old_value}")
            console.print(f"  New: {result.new_value}")
            
        if result.difference is not None:
            console.print(f"  Difference: {result.difference:.4f}")
            
        if result.threshold is not None:
            console.print(f"  Threshold: {result.threshold:.4f}")
            
        if result.details:
            console.print("  Details:")
            for key, value in result.details.items():
                if isinstance(value, (list, dict)) and len(str(value)) > 100:
                    console.print(f"    {key}: [truncated]")
                else:
                    console.print(f"    {key}: {value}")


@app.command()
def report(
    validation_id: Optional[str] = typer.Argument(
        None,
        help="Validation ID to generate report for"
    ),
    format: str = typer.Option(
        "html",
        "--format", "-f",
        help="Report format: html, json, markdown"
    )
):
    """Generate validation report from saved results."""
    # Find validation results
    if validation_id:
        pattern = f"*{validation_id}*.json"
    else:
        pattern = "validation_summary_*.json"
        
    result_files = list(Path("reports/parallel_validation").glob(pattern))
    
    if not result_files:
        console.print("[red]No validation results found[/red]")
        raise typer.Exit(1)
        
    # Use most recent if multiple found
    result_file = max(result_files, key=lambda p: p.stat().st_mtime)
    
    console.print(f"Generating {format} report from: {result_file}")
    
    # Load results
    with open(result_file) as f:
        results = json.load(f)
        
    # Generate report based on format
    if format == "markdown":
        generate_markdown_report(results, result_file.parent)
    else:
        console.print(f"[yellow]Report format {format} not yet implemented[/yellow]")


def generate_markdown_report(results: dict, output_dir: Path) -> None:
    """Generate markdown report from results."""
    timestamp = results.get("validation_timestamp", "unknown")
    report_file = output_dir / f"validation_report_{timestamp}.md"
    
    with open(report_file, "w") as f:
        f.write("# Pipeline Validation Report\n\n")
        f.write(f"Generated: {timestamp}\n\n")
        
        # Summary
        summary = results["summary"]
        f.write("## Summary\n\n")
        f.write(f"- Total Datasets: {summary['total_datasets']}\n")
        f.write(f"- Passed Datasets: {summary['passed_datasets']}\n")
        f.write(f"- Success Rate: {(summary['passed_datasets']/summary['total_datasets']*100):.1f}%\n\n")
        
        # Dataset details
        f.write("## Dataset Results\n\n")
        for dataset, details in summary["datasets"].items():
            status = "✓" if details["passed"] else "✗"
            f.write(f"### {status} {dataset}\n\n")
            f.write(f"- Match Percentage: {details['match_percentage']:.1f}%\n")
            
            if details["failed_checks"]:
                f.write("- Failed Checks:\n")
                for check in details["failed_checks"]:
                    f.write(f"  - {check}\n")
                    
            if details["recommendations"]:
                f.write("- Recommendations:\n")
                for rec in details["recommendations"]:
                    f.write(f"  - {rec}\n")
                    
            f.write("\n")
            
    console.print(f"[green]Markdown report saved to: {report_file}[/green]")


if __name__ == "__main__":
    app()