"""Optimize pipeline performance using profiling and optimization strategies."""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

import asyncio
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional
import pandas as pd
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn
import matplotlib.pyplot as plt
import seaborn as sns

from src.infrastructure.processors import ProcessorFactory, ProcessorConfig, ProcessorType
from src.infrastructure.processors.base_processor import SourceConfig
from src.infrastructure.data_quality.validation_framework import ValidationFramework, ValidationLevel
from src.infrastructure.caching.cache_manager import CacheManager
from src.infrastructure.performance.profiler import PipelineProfiler, ProfileResult
from src.infrastructure.performance.optimizer import (
    PipelineOptimizer,
    OptimizationStrategy,
    PartitionStrategy,
    ParallelizationConfig,
    OptimizationConfig
)
from src.application.services.data_pipeline_orchestrator import DataPipelineOrchestrator, PipelineConfig
from src.core.utils.logging import get_logger


logger = get_logger(__name__)
console = Console()


class PipelineOptimizationRunner:
    """Run comprehensive pipeline optimization."""
    
    def __init__(self, output_dir: Path = None):
        """Initialize optimization runner.
        
        Args:
            output_dir: Directory for optimization results
        """
        self.output_dir = output_dir or Path("results/optimization")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.cache_manager = CacheManager(cache_dir=Path.home() / '.cache' / 'yemen_market_pipeline')
        self.validator = ValidationFramework()
        
    async def profile_current_pipeline(self) -> ProfileResult:
        """Profile the current pipeline implementation."""
        console.print("\n[bold cyan]Step 1: Profiling Current Pipeline[/bold cyan]\n")
        
        profiler = PipelineProfiler(output_dir=self.output_dir / "profiles")
        
        # Create processors for profiling
        processors = []
        processor_types = [
            ProcessorType.PRICE,
            ProcessorType.CONFLICT,
            ProcessorType.CLIMATE,
            ProcessorType.POPULATION,
            ProcessorType.INFRASTRUCTURE
        ]
        
        factory = ProcessorFactory(
            cache_manager=self.cache_manager,
            validator=self.validator
        )
        
        for proc_type in processor_types:
            config = ProcessorConfig(
                processor_type=proc_type,
                source_config=SourceConfig(
                    source_id=f"{proc_type.value}_optimize",
                    name=f"{proc_type.value} Optimization Test",
                    description="Optimization profiling",
                    validation_level=ValidationLevel.STANDARD
                )
            )
            processor = factory.create_processor(config)
            processors.append(processor)
        
        # Profile pipeline
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            console=console
        ) as progress:
            task = progress.add_task("Profiling pipeline...", total=len(processors))
            
            profile_result = await profiler.profile_pipeline(processors, parallel=True)
            
            progress.update(task, completed=len(processors))
        
        # Display and save results
        profiler.display_results(profile_result)
        profiler.save_profile_report(profile_result, "baseline_profile")
        
        return profile_result
    
    async def optimize_pipeline(self, profile_result: ProfileResult) -> OptimizationConfig:
        """Generate and apply optimizations based on profiling."""
        console.print("\n[bold cyan]Step 2: Generating Optimization Plan[/bold cyan]\n")
        
        optimizer = PipelineOptimizer(
            cache_manager=self.cache_manager,
            profile_result=profile_result
        )
        
        # Generate optimization plan
        optimization_config = optimizer.generate_optimization_plan(profile_result)
        
        # Display optimization plan
        table = Table(title="Optimization Plan")
        table.add_column("Strategy", style="cyan")
        table.add_column("Description", style="white")
        
        strategy_descriptions = {
            OptimizationStrategy.PARALLEL_PROCESSING: "Enable parallel processing for independent operations",
            OptimizationStrategy.DATA_PARTITIONING: "Partition large datasets for distributed processing",
            OptimizationStrategy.MEMORY_OPTIMIZATION: "Reduce memory usage through efficient data types",
            OptimizationStrategy.CACHING_ENHANCEMENT: "Improve cache hit rates and TTL configuration",
            OptimizationStrategy.ASYNC_IO: "Use asynchronous I/O for file and network operations",
            OptimizationStrategy.BATCH_PROCESSING: "Process data in optimized batch sizes",
            OptimizationStrategy.INCREMENTAL_PROCESSING: "Process only changed data"
        }
        
        for strategy in optimization_config.strategies:
            table.add_row(
                strategy.value,
                strategy_descriptions.get(strategy, "")
            )
        
        console.print(table)
        
        # Display parallelization config
        console.print("\n[bold]Parallelization Configuration:[/bold]")
        console.print(f"  Max Workers: {optimization_config.parallelization.max_workers}")
        console.print(f"  Chunk Size: {optimization_config.parallelization.chunk_size:,}")
        console.print(f"  Use Multiprocessing: {optimization_config.parallelization.use_multiprocessing}")
        console.print(f"  Memory Limit: {optimization_config.parallelization.memory_limit_mb:,}MB")
        console.print(f"  Partition Strategy: {optimization_config.partition_strategy.value}")
        
        return optimization_config
    
    async def test_optimizations(
        self,
        optimization_config: OptimizationConfig
    ) -> Dict[str, any]:
        """Test the impact of optimizations."""
        console.print("\n[bold cyan]Step 3: Testing Optimizations[/bold cyan]\n")
        
        # Create test data
        test_data = self._create_test_data()
        
        optimizer = PipelineOptimizer(self.cache_manager)
        
        results = {}
        
        # Test data partitioning
        if OptimizationStrategy.DATA_PARTITIONING in optimization_config.strategies:
            console.print("Testing data partitioning...")
            
            partitions = await optimizer.partition_data(
                test_data,
                optimization_config.partition_strategy,
                n_partitions=4
            )
            
            console.print(f"  Created {len(partitions)} partitions")
            console.print(f"  Partition sizes: {[len(p) for p in partitions]}")
            
            results['partitioning'] = {
                'n_partitions': len(partitions),
                'partition_sizes': [len(p) for p in partitions],
                'strategy': optimization_config.partition_strategy.value
            }
        
        # Test memory optimization
        if OptimizationStrategy.MEMORY_OPTIMIZATION in optimization_config.strategies:
            console.print("\nTesting memory optimization...")
            
            original_memory = test_data.memory_usage(deep=True).sum() / 1024 / 1024
            optimized_data = optimizer.optimize_dataframe_memory(test_data.copy())
            optimized_memory = optimized_data.memory_usage(deep=True).sum() / 1024 / 1024
            
            reduction_pct = (original_memory - optimized_memory) / original_memory * 100
            
            console.print(f"  Original memory: {original_memory:.1f}MB")
            console.print(f"  Optimized memory: {optimized_memory:.1f}MB")
            console.print(f"  Reduction: {reduction_pct:.1f}%")
            
            results['memory_optimization'] = {
                'original_mb': original_memory,
                'optimized_mb': optimized_memory,
                'reduction_pct': reduction_pct
            }
        
        # Test parallel processing
        if OptimizationStrategy.PARALLEL_PROCESSING in optimization_config.strategies:
            console.print("\nTesting parallel processing...")
            
            # Simple processing function
            def process_partition(df):
                # Simulate some processing
                return df.groupby('commodity').agg({
                    'price': ['mean', 'std'],
                    'quantity': 'sum'
                })
            
            partitions = await optimizer.partition_data(test_data, PartitionStrategy.BY_SIZE, n_partitions=4)
            
            # Sequential timing
            seq_start = time.time()
            seq_results = [process_partition(p) for p in partitions]
            seq_duration = time.time() - seq_start
            
            # Parallel timing
            par_start = time.time()
            par_results = await optimizer.process_partitions_parallel(
                partitions,
                process_partition,
                optimization_config.parallelization
            )
            par_duration = time.time() - par_start
            
            speedup = seq_duration / par_duration if par_duration > 0 else 1.0
            
            console.print(f"  Sequential: {seq_duration:.2f}s")
            console.print(f"  Parallel: {par_duration:.2f}s")
            console.print(f"  Speedup: {speedup:.2f}x")
            
            results['parallel_processing'] = {
                'sequential_duration': seq_duration,
                'parallel_duration': par_duration,
                'speedup': speedup
            }
        
        return results
    
    async def run_optimized_pipeline(
        self,
        optimization_config: OptimizationConfig
    ) -> Dict[str, any]:
        """Run the pipeline with optimizations applied."""
        console.print("\n[bold cyan]Step 4: Running Optimized Pipeline[/bold cyan]\n")
        
        # Configure optimized pipeline
        pipeline_config = PipelineConfig(
            start_date=datetime(2024, 1, 1),
            end_date=datetime(2024, 3, 31),
            data_sources={
                'wfp': {'enabled': True, 'priority': 1},
                'climate': {'enabled': True, 'priority': 2},
                'conflict': {'enabled': True, 'priority': 3}
            },
            output_format='parquet',
            validation_level=ValidationLevel.STANDARD,
            enable_caching=True,
            parallel_downloads=optimization_config.parallelization.max_workers or 3,
            max_retries=3
        )
        
        # Apply optimizations to cache
        if OptimizationStrategy.CACHING_ENHANCEMENT in optimization_config.strategies:
            self.cache_manager.default_ttl = optimization_config.cache_ttl_hours * 3600
        
        orchestrator = DataPipelineOrchestrator(
            config=pipeline_config,
            cache_manager=self.cache_manager
        )
        
        # Run with timing
        start_time = time.time()
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("Running optimized pipeline...", total=None)
            
            result = await orchestrator.run_pipeline()
            
            progress.update(task, completed=True)
        
        duration = time.time() - start_time
        
        if result.get('success'):
            console.print(f"\n[green]✓ Optimized pipeline completed in {duration:.1f}s[/green]")
            console.print(f"  Records processed: {result.get('total_records', 0):,}")
            console.print(f"  Throughput: {result.get('total_records', 0) / duration:.0f} records/sec")
        else:
            console.print(f"\n[red]✗ Pipeline failed: {result.get('error')}[/red]")
        
        return {
            'duration': duration,
            'success': result.get('success', False),
            'total_records': result.get('total_records', 0),
            'throughput': result.get('total_records', 0) / duration if duration > 0 else 0
        }
    
    async def compare_performance(
        self,
        baseline_profile: ProfileResult,
        optimized_results: Dict[str, any]
    ):
        """Compare baseline vs optimized performance."""
        console.print("\n[bold cyan]Step 5: Performance Comparison[/bold cyan]\n")
        
        # Calculate baseline metrics
        baseline_duration = baseline_profile.total_duration
        baseline_memory = baseline_profile.memory_usage_summary['total_peak_mb']
        
        # Create comparison table
        table = Table(title="Performance Comparison")
        table.add_column("Metric", style="cyan")
        table.add_column("Baseline", justify="right")
        table.add_column("Optimized", justify="right")
        table.add_column("Improvement", justify="right", style="green")
        
        # Duration comparison
        if 'duration' in optimized_results:
            opt_duration = optimized_results['duration']
            duration_improvement = (baseline_duration - opt_duration) / baseline_duration * 100
            
            table.add_row(
                "Duration",
                f"{baseline_duration:.1f}s",
                f"{opt_duration:.1f}s",
                f"{duration_improvement:.1f}%"
            )
        
        # Memory comparison
        if 'memory_optimization' in optimized_results:
            opt_memory = optimized_results['memory_optimization']['optimized_mb']
            memory_improvement = optimized_results['memory_optimization']['reduction_pct']
            
            table.add_row(
                "Memory Usage",
                f"{baseline_memory:.0f}MB",
                f"{opt_memory:.0f}MB",
                f"{memory_improvement:.1f}%"
            )
        
        # Throughput comparison
        if 'throughput' in optimized_results:
            baseline_throughput = sum(
                p.records_per_second 
                for p in baseline_profile.processor_profiles.values()
            ) / len(baseline_profile.processor_profiles)
            
            opt_throughput = optimized_results['throughput']
            throughput_improvement = (opt_throughput - baseline_throughput) / baseline_throughput * 100
            
            table.add_row(
                "Throughput",
                f"{baseline_throughput:.0f} rec/s",
                f"{opt_throughput:.0f} rec/s",
                f"{throughput_improvement:.1f}%"
            )
        
        console.print(table)
        
        # Create visualization
        self._create_comparison_chart(baseline_profile, optimized_results)
    
    def _create_test_data(self) -> pd.DataFrame:
        """Create test data for optimization testing."""
        import numpy as np
        
        # Generate realistic test data
        n_records = 50000
        
        dates = pd.date_range('2024-01-01', periods=90, freq='D')
        markets = ['Sana\'a', 'Aden', 'Taiz', 'Hodeidah', 'Ibb']
        commodities = ['wheat', 'rice', 'sugar', 'fuel', 'cooking_oil']
        
        data = []
        for date in dates:
            for market in markets:
                for commodity in commodities:
                    # Add some variation
                    base_price = np.random.uniform(100, 1000)
                    price = base_price * (1 + np.random.normal(0, 0.1))
                    quantity = np.random.uniform(10, 1000)
                    
                    data.append({
                        'date': date,
                        'market': market,
                        'commodity': commodity,
                        'price': price,
                        'quantity': quantity,
                        'governorate': market,
                        'district': f"{market}_district",
                        'currency': 'YER',
                        'unit': 'kg'
                    })
        
        df = pd.DataFrame(data)
        
        # Add some categorical columns
        df['quality'] = np.random.choice(['high', 'medium', 'low'], len(df))
        df['vendor_type'] = np.random.choice(['wholesale', 'retail'], len(df))
        
        return df
    
    def _create_comparison_chart(
        self,
        baseline_profile: ProfileResult,
        optimized_results: Dict[str, any]
    ):
        """Create performance comparison visualization."""
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        fig.suptitle('Pipeline Optimization Results', fontsize=16)
        
        # Duration comparison
        ax1 = axes[0, 0]
        categories = ['Baseline', 'Optimized']
        durations = [
            baseline_profile.total_duration,
            optimized_results.get('duration', baseline_profile.total_duration)
        ]
        ax1.bar(categories, durations, color=['coral', 'lightgreen'])
        ax1.set_title('Processing Duration')
        ax1.set_ylabel('Duration (seconds)')
        
        # Memory usage
        ax2 = axes[0, 1]
        if 'memory_optimization' in optimized_results:
            memory_data = optimized_results['memory_optimization']
            memories = [memory_data['original_mb'], memory_data['optimized_mb']]
            ax2.bar(categories, memories, color=['coral', 'lightgreen'])
            ax2.set_title('Memory Usage')
            ax2.set_ylabel('Memory (MB)')
        
        # Parallel speedup
        ax3 = axes[1, 0]
        if 'parallel_processing' in optimized_results:
            parallel_data = optimized_results['parallel_processing']
            speedup = parallel_data['speedup']
            ax3.bar(['Sequential', 'Parallel'], [1.0, speedup], color=['gray', 'gold'])
            ax3.set_title('Parallel Processing Speedup')
            ax3.set_ylabel('Speedup Factor')
            ax3.axhline(y=1.0, color='red', linestyle='--', alpha=0.5)
        
        # Optimization strategies impact
        ax4 = axes[1, 1]
        if baseline_profile.optimization_potential > 1:
            strategies = ['Current', 'Potential']
            performance = [1.0, baseline_profile.optimization_potential]
            ax4.bar(strategies, performance, color=['coral', 'lightblue'])
            ax4.set_title('Optimization Potential')
            ax4.set_ylabel('Performance Multiplier')
        
        plt.tight_layout()
        
        # Save figure
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        fig_path = self.output_dir / f"optimization_comparison_{timestamp}.png"
        plt.savefig(fig_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        console.print(f"\n[green]Comparison chart saved to: {fig_path}[/green]")
    
    def save_optimization_report(
        self,
        baseline_profile: ProfileResult,
        optimization_config: OptimizationConfig,
        test_results: Dict[str, any],
        pipeline_results: Dict[str, any]
    ):
        """Save comprehensive optimization report."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_path = self.output_dir / f"optimization_report_{timestamp}.txt"
        
        with open(report_path, 'w') as f:
            f.write("Pipeline Optimization Report\n")
            f.write("=" * 80 + "\n\n")
            f.write(f"Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            # Baseline performance
            f.write("BASELINE PERFORMANCE\n")
            f.write("-" * 40 + "\n")
            f.write(f"Total Duration: {baseline_profile.total_duration:.2f}s\n")
            f.write(f"Peak Memory: {baseline_profile.memory_usage_summary['total_peak_mb']:.0f}MB\n")
            f.write(f"Parallel Efficiency: {baseline_profile.parallel_efficiency:.1%}\n")
            f.write(f"Optimization Potential: {baseline_profile.optimization_potential:.1f}x\n\n")
            
            # Optimization strategies
            f.write("OPTIMIZATION STRATEGIES APPLIED\n")
            f.write("-" * 40 + "\n")
            for strategy in optimization_config.strategies:
                f.write(f"- {strategy.value}\n")
            f.write(f"\nPartition Strategy: {optimization_config.partition_strategy.value}\n")
            f.write(f"Max Workers: {optimization_config.parallelization.max_workers}\n")
            f.write(f"Chunk Size: {optimization_config.parallelization.chunk_size:,}\n\n")
            
            # Test results
            f.write("OPTIMIZATION TEST RESULTS\n")
            f.write("-" * 40 + "\n")
            
            if 'memory_optimization' in test_results:
                mem_opt = test_results['memory_optimization']
                f.write(f"Memory Optimization:\n")
                f.write(f"  Original: {mem_opt['original_mb']:.1f}MB\n")
                f.write(f"  Optimized: {mem_opt['optimized_mb']:.1f}MB\n")
                f.write(f"  Reduction: {mem_opt['reduction_pct']:.1f}%\n\n")
            
            if 'parallel_processing' in test_results:
                par_proc = test_results['parallel_processing']
                f.write(f"Parallel Processing:\n")
                f.write(f"  Sequential: {par_proc['sequential_duration']:.2f}s\n")
                f.write(f"  Parallel: {par_proc['parallel_duration']:.2f}s\n")
                f.write(f"  Speedup: {par_proc['speedup']:.2f}x\n\n")
            
            # Pipeline results
            f.write("OPTIMIZED PIPELINE RESULTS\n")
            f.write("-" * 40 + "\n")
            f.write(f"Duration: {pipeline_results.get('duration', 0):.1f}s\n")
            f.write(f"Success: {pipeline_results.get('success', False)}\n")
            f.write(f"Records: {pipeline_results.get('total_records', 0):,}\n")
            f.write(f"Throughput: {pipeline_results.get('throughput', 0):.0f} records/sec\n\n")
            
            # Recommendations
            f.write("RECOMMENDATIONS\n")
            f.write("-" * 40 + "\n")
            f.write("1. Continue monitoring performance metrics in production\n")
            f.write("2. Consider implementing remaining optimization strategies\n")
            f.write("3. Test with larger datasets to validate scalability\n")
            f.write("4. Profile specific bottlenecks during peak usage\n")
            f.write("5. Implement adaptive optimization based on workload\n")
        
        console.print(f"\n[green]Optimization report saved to: {report_path}[/green]")


async def main():
    """Run pipeline optimization process."""
    runner = PipelineOptimizationRunner()
    
    try:
        # Step 1: Profile current pipeline
        baseline_profile = await runner.profile_current_pipeline()
        
        # Step 2: Generate optimization plan
        optimization_config = await runner.optimize_pipeline(baseline_profile)
        
        # Step 3: Test optimizations
        test_results = await runner.test_optimizations(optimization_config)
        
        # Step 4: Run optimized pipeline
        pipeline_results = await runner.run_optimized_pipeline(optimization_config)
        
        # Step 5: Compare performance
        await runner.compare_performance(baseline_profile, {**test_results, **pipeline_results})
        
        # Save comprehensive report
        runner.save_optimization_report(
            baseline_profile,
            optimization_config,
            test_results,
            pipeline_results
        )
        
        console.print("\n[bold green]✓ Pipeline optimization complete![/bold green]")
        
    except Exception as e:
        console.print(f"\n[bold red]✗ Optimization failed: {e}[/bold red]")
        logger.error(f"Pipeline optimization error: {e}", exc_info=True)


if __name__ == "__main__":
    asyncio.run(main())