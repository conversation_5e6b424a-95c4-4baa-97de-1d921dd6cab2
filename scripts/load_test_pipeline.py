#!/usr/bin/env python3
"""
Load test the data pipeline with full historical data.

Tests:
- Full historical data processing (2019-2025)
- Concurrent pipeline runs
- Resource limit testing
- Network degradation simulation
- Failure recovery
"""
import asyncio
import time
import psutil
import argparse
from pathlib import Path
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
import structlog
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TimeElapsedColumn
from rich.table import Table
from rich.panel import Panel
import random
import json

# Add src to path
import sys
sys.path.append(str(Path(__file__).parent.parent))

from src.application.services.enhanced_data_pipeline_orchestrator import (
    EnhancedDataPipelineOrchestrator
)
from src.infrastructure.performance.profiler import PipelineProfiler
from src.infrastructure.performance.optimizer import (
    PipelineOptimizer,
    OptimizationStrategy,
    OptimizationConfig
)
from src.core.domain.pipeline.entities import PipelineConfig

logger = structlog.get_logger()
console = Console()


class LoadTestScenario:
    """Base class for load test scenarios."""
    
    def __init__(self, name: str, description: str):
        self.name = name
        self.description = description
        self.results: Dict[str, any] = {}
        
    async def setup(self):
        """Setup scenario."""
        pass
        
    async def run(self, orchestrator: EnhancedDataPipelineOrchestrator) -> Dict[str, any]:
        """Run the scenario."""
        raise NotImplementedError
        
    async def teardown(self):
        """Cleanup after scenario."""
        pass


class FullHistoricalDataScenario(LoadTestScenario):
    """Test with full historical data (2019-2025)."""
    
    def __init__(self):
        super().__init__(
            "Full Historical Data",
            "Process all data from 2019 to 2025"
        )
        
    async def run(self, orchestrator: EnhancedDataPipelineOrchestrator) -> Dict[str, any]:
        """Run full historical data test."""
        start_time = time.time()
        
        # Configure for full historical data
        config = PipelineConfig(
            start_date=datetime(2019, 1, 1),
            end_date=datetime(2025, 6, 1),
            sources=["wfp", "acled", "acaps", "climate", "population"],
            parallel=True,
            checkpoint_enabled=True
        )
        
        # Profile the run
        profiler = PipelineProfiler()
        
        try:
            # Run pipeline
            panel_data = await profiler.profile_processor(
                "full_historical_pipeline",
                orchestrator.run_pipeline,
                config
            )
            
            duration = time.time() - start_time
            
            # Get metrics
            metrics = orchestrator.export_metrics()
            health = orchestrator.get_health_status()
            
            return {
                "success": True,
                "duration": duration,
                "records_processed": len(panel_data) if panel_data is not None else 0,
                "metrics": metrics,
                "health": health,
                "profile": profiler.profiles.get("full_historical_pipeline")
            }
            
        except Exception as e:
            logger.error("Full historical test failed", error=str(e))
            return {
                "success": False,
                "duration": time.time() - start_time,
                "error": str(e)
            }


class ConcurrentPipelinesScenario(LoadTestScenario):
    """Test concurrent pipeline runs."""
    
    def __init__(self, concurrent_runs: int = 3):
        super().__init__(
            "Concurrent Pipelines",
            f"Run {concurrent_runs} pipelines concurrently"
        )
        self.concurrent_runs = concurrent_runs
        
    async def run(self, orchestrator: EnhancedDataPipelineOrchestrator) -> Dict[str, any]:
        """Run concurrent pipelines test."""
        start_time = time.time()
        
        # Create different date ranges for each pipeline
        date_ranges = []
        base_date = datetime(2019, 1, 1)
        for i in range(self.concurrent_runs):
            start = base_date + timedelta(days=i * 365)
            end = start + timedelta(days=180)  # 6 months each
            date_ranges.append((start, end))
        
        # Create tasks for concurrent execution
        tasks = []
        for i, (start_date, end_date) in enumerate(date_ranges):
            config = PipelineConfig(
                start_date=start_date,
                end_date=end_date,
                sources=["wfp", "acled"],  # Lighter load for concurrent test
                parallel=True
            )
            
            # Create separate orchestrator for each run
            task_orchestrator = EnhancedDataPipelineOrchestrator()
            task = asyncio.create_task(
                self._run_single_pipeline(task_orchestrator, config, i)
            )
            tasks.append(task)
        
        # Wait for all to complete
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        duration = time.time() - start_time
        
        # Analyze results
        successful = sum(1 for r in results if isinstance(r, dict) and r.get("success"))
        
        return {
            "success": successful == self.concurrent_runs,
            "duration": duration,
            "concurrent_runs": self.concurrent_runs,
            "successful_runs": successful,
            "individual_results": results
        }
    
    async def _run_single_pipeline(
        self,
        orchestrator: EnhancedDataPipelineOrchestrator,
        config: PipelineConfig,
        index: int
    ) -> Dict[str, any]:
        """Run a single pipeline instance."""
        try:
            start = time.time()
            result = await orchestrator.run_pipeline(config)
            return {
                "success": True,
                "index": index,
                "duration": time.time() - start,
                "records": len(result) if result is not None else 0
            }
        except Exception as e:
            return {
                "success": False,
                "index": index,
                "error": str(e)
            }


class ResourceLimitScenario(LoadTestScenario):
    """Test behavior under resource constraints."""
    
    def __init__(self, memory_limit_gb: float = 2.0):
        super().__init__(
            "Resource Limits",
            f"Test with {memory_limit_gb}GB memory limit"
        )
        self.memory_limit_gb = memory_limit_gb
        
    async def run(self, orchestrator: EnhancedDataPipelineOrchestrator) -> Dict[str, any]:
        """Run resource limit test."""
        start_time = time.time()
        
        # Configure optimizer for memory constraints
        optimizer = PipelineOptimizer(
            OptimizationConfig(
                max_memory_mb=int(self.memory_limit_gb * 1024),
                chunk_size=5000,  # Smaller chunks
                max_workers=2  # Fewer workers
            )
        )
        
        # Configure pipeline
        config = PipelineConfig(
            start_date=datetime(2023, 1, 1),
            end_date=datetime(2023, 12, 31),
            sources=["wfp", "acled", "climate"],
            parallel=False  # Sequential to control memory
        )
        
        # Monitor memory usage
        process = psutil.Process()
        memory_samples = []
        
        async def monitor_memory():
            while True:
                memory_mb = process.memory_info().rss / 1024 / 1024
                memory_samples.append(memory_mb)
                if memory_mb > self.memory_limit_gb * 1024:
                    logger.warning(
                        "Memory limit exceeded",
                        current_mb=memory_mb,
                        limit_mb=self.memory_limit_gb * 1024
                    )
                await asyncio.sleep(1)
        
        # Start memory monitoring
        monitor_task = asyncio.create_task(monitor_memory())
        
        try:
            # Run pipeline with memory optimization
            result = await orchestrator.run_pipeline(config)
            
            # Stop monitoring
            monitor_task.cancel()
            try:
                await monitor_task
            except asyncio.CancelledError:
                pass
            
            duration = time.time() - start_time
            
            return {
                "success": True,
                "duration": duration,
                "memory_limit_mb": self.memory_limit_gb * 1024,
                "peak_memory_mb": max(memory_samples) if memory_samples else 0,
                "avg_memory_mb": np.mean(memory_samples) if memory_samples else 0,
                "memory_violations": sum(
                    1 for m in memory_samples 
                    if m > self.memory_limit_gb * 1024
                )
            }
            
        except Exception as e:
            monitor_task.cancel()
            return {
                "success": False,
                "duration": time.time() - start_time,
                "error": str(e)
            }


class NetworkDegradationScenario(LoadTestScenario):
    """Test with simulated network issues."""
    
    def __init__(self, latency_ms: int = 500, packet_loss: float = 0.1):
        super().__init__(
            "Network Degradation",
            f"Test with {latency_ms}ms latency, {packet_loss*100}% loss"
        )
        self.latency_ms = latency_ms
        self.packet_loss = packet_loss
        
    async def run(self, orchestrator: EnhancedDataPipelineOrchestrator) -> Dict[str, any]:
        """Run network degradation test."""
        start_time = time.time()
        
        # Monkey patch network calls to simulate degradation
        original_hdx_fetch = orchestrator.ingestion_service.hdx_client.fetch_dataset
        
        async def degraded_fetch(*args, **kwargs):
            # Simulate packet loss
            if random.random() < self.packet_loss:
                raise ConnectionError("Simulated packet loss")
            
            # Simulate latency
            await asyncio.sleep(self.latency_ms / 1000)
            
            return await original_hdx_fetch(*args, **kwargs)
        
        # Apply degradation
        orchestrator.ingestion_service.hdx_client.fetch_dataset = degraded_fetch
        
        # Configure pipeline with retries
        config = PipelineConfig(
            start_date=datetime(2024, 1, 1),
            end_date=datetime(2024, 3, 31),
            sources=["wfp"],  # Single source for focused test
            parallel=False
        )
        
        retry_count = 0
        error_count = 0
        
        # Track retries
        def count_retries(record, exc_info):
            nonlocal retry_count, error_count
            if "retry" in record.get("event", "").lower():
                retry_count += 1
            if "error" in record.get("event", "").lower():
                error_count += 1
        
        structlog.testing.CapturingLogger.add_processor(count_retries)
        
        try:
            result = await orchestrator.run_pipeline(config)
            
            duration = time.time() - start_time
            
            return {
                "success": True,
                "duration": duration,
                "latency_ms": self.latency_ms,
                "packet_loss": self.packet_loss,
                "retry_count": retry_count,
                "error_count": error_count,
                "effective_throughput": len(result) / duration if result else 0
            }
            
        except Exception as e:
            return {
                "success": False,
                "duration": time.time() - start_time,
                "error": str(e),
                "retry_count": retry_count,
                "error_count": error_count
            }
        finally:
            # Restore original
            orchestrator.ingestion_service.hdx_client.fetch_dataset = original_hdx_fetch


class FailureRecoveryScenario(LoadTestScenario):
    """Test failure recovery mechanisms."""
    
    def __init__(self, failure_after_seconds: int = 30):
        super().__init__(
            "Failure Recovery",
            f"Simulate failure after {failure_after_seconds}s"
        )
        self.failure_after_seconds = failure_after_seconds
        
    async def run(self, orchestrator: EnhancedDataPipelineOrchestrator) -> Dict[str, any]:
        """Run failure recovery test."""
        start_time = time.time()
        
        # Configure pipeline with checkpointing
        config = PipelineConfig(
            start_date=datetime(2024, 1, 1),
            end_date=datetime(2024, 6, 30),
            sources=["wfp", "acled", "climate"],
            parallel=True,
            checkpoint_enabled=True,
            checkpoint_interval=10  # Frequent checkpoints
        )
        
        # Schedule failure
        async def cause_failure():
            await asyncio.sleep(self.failure_after_seconds)
            # Simulate critical failure
            raise RuntimeError("Simulated critical failure")
        
        failure_task = asyncio.create_task(cause_failure())
        
        first_attempt_records = 0
        checkpoint_id = None
        
        try:
            # First attempt (will fail)
            result = await asyncio.gather(
                orchestrator.run_pipeline(config),
                failure_task,
                return_exceptions=True
            )
            
            # Check what happened
            if isinstance(result[0], Exception):
                # Pipeline failed, get checkpoint
                checkpoint_id = orchestrator.state_machine.last_checkpoint_id
                first_attempt_records = orchestrator.state_machine.records_processed
            
        except Exception:
            # Expected failure
            checkpoint_id = orchestrator.state_machine.last_checkpoint_id
            first_attempt_records = orchestrator.state_machine.records_processed
        
        # Now test recovery
        recovery_start = time.time()
        
        if checkpoint_id:
            # Resume from checkpoint
            config.resume_from_checkpoint = checkpoint_id
            
            try:
                result = await orchestrator.run_pipeline(config)
                
                recovery_duration = time.time() - recovery_start
                total_duration = time.time() - start_time
                
                return {
                    "success": True,
                    "duration": total_duration,
                    "recovery_duration": recovery_duration,
                    "first_attempt_records": first_attempt_records,
                    "total_records": len(result) if result else 0,
                    "checkpoint_used": checkpoint_id is not None,
                    "failure_after_seconds": self.failure_after_seconds
                }
                
            except Exception as e:
                return {
                    "success": False,
                    "duration": time.time() - start_time,
                    "error": f"Recovery failed: {str(e)}"
                }
        else:
            return {
                "success": False,
                "duration": time.time() - start_time,
                "error": "No checkpoint available for recovery"
            }


async def run_load_tests(scenarios: List[str] = None):
    """Run load test scenarios."""
    # Define all scenarios
    all_scenarios = {
        "historical": FullHistoricalDataScenario(),
        "concurrent": ConcurrentPipelinesScenario(concurrent_runs=3),
        "memory": ResourceLimitScenario(memory_limit_gb=2.0),
        "network": NetworkDegradationScenario(latency_ms=500, packet_loss=0.1),
        "recovery": FailureRecoveryScenario(failure_after_seconds=30)
    }
    
    # Filter scenarios if specified
    if scenarios:
        selected = {k: v for k, v in all_scenarios.items() if k in scenarios}
    else:
        selected = all_scenarios
    
    # Results storage
    results = {}
    
    # Create orchestrator
    orchestrator = EnhancedDataPipelineOrchestrator()
    
    # Run each scenario
    with Progress(
        SpinnerColumn(),
        *Progress.get_default_columns(),
        TimeElapsedColumn(),
        console=console
    ) as progress:
        
        for name, scenario in selected.items():
            task = progress.add_task(
                f"[cyan]Running {scenario.name}...", 
                total=None
            )
            
            console.print(f"\n[bold blue]Starting scenario: {scenario.name}[/bold blue]")
            console.print(f"[dim]{scenario.description}[/dim]\n")
            
            try:
                # Setup
                await scenario.setup()
                
                # Run
                result = await scenario.run(orchestrator)
                results[name] = result
                
                # Teardown
                await scenario.teardown()
                
                # Report result
                if result.get("success"):
                    console.print(f"[green]✓ {scenario.name} completed successfully[/green]")
                else:
                    console.print(f"[red]✗ {scenario.name} failed: {result.get('error')}[/red]")
                    
            except Exception as e:
                console.print(f"[red]✗ {scenario.name} crashed: {str(e)}[/red]")
                results[name] = {"success": False, "error": str(e)}
            
            progress.update(task, completed=True)
    
    # Generate report
    generate_load_test_report(results)
    
    return results


def generate_load_test_report(results: Dict[str, Dict[str, any]]):
    """Generate load test report."""
    console.print("\n[bold]Load Test Results Summary[/bold]\n")
    
    # Summary table
    table = Table(title="Scenario Results")
    table.add_column("Scenario", style="cyan")
    table.add_column("Status", justify="center")
    table.add_column("Duration", justify="right")
    table.add_column("Key Metrics")
    
    for name, result in results.items():
        status = "✅" if result.get("success") else "❌"
        duration = f"{result.get('duration', 0):.1f}s"
        
        # Extract key metrics
        metrics = []
        if "records_processed" in result:
            metrics.append(f"Records: {result['records_processed']:,}")
        if "memory_peak_mb" in result:
            metrics.append(f"Peak Memory: {result['memory_peak_mb']:.0f}MB")
        if "retry_count" in result:
            metrics.append(f"Retries: {result['retry_count']}")
        if "successful_runs" in result:
            metrics.append(f"Success: {result['successful_runs']}/{result['concurrent_runs']}")
        
        table.add_row(
            name.title(),
            status,
            duration,
            "\n".join(metrics)
        )
    
    console.print(table)
    
    # Detailed results
    for name, result in results.items():
        if result.get("success"):
            console.print(f"\n[green]✓ {name.title()} Details:[/green]")
            
            # Scenario-specific details
            if name == "historical" and "profile" in result:
                profile = result["profile"]
                if profile:
                    console.print(f"  • Throughput: {profile.records_per_second:.0f} records/sec")
                    console.print(f"  • Memory Usage: {profile.memory_peak_mb:.0f}MB")
                    console.print(f"  • CPU Average: {profile.cpu_percent_avg:.1f}%")
            
            elif name == "concurrent":
                console.print(f"  • Concurrent Runs: {result['concurrent_runs']}")
                console.print(f"  • Success Rate: {result['successful_runs']}/{result['concurrent_runs']}")
            
            elif name == "memory":
                console.print(f"  • Memory Limit: {result['memory_limit_mb']:.0f}MB")
                console.print(f"  • Peak Usage: {result['peak_memory_mb']:.0f}MB")
                console.print(f"  • Violations: {result['memory_violations']}")
            
            elif name == "network":
                console.print(f"  • Network Latency: {result['latency_ms']}ms")
                console.print(f"  • Packet Loss: {result['packet_loss']*100:.0f}%")
                console.print(f"  • Retries: {result['retry_count']}")
            
            elif name == "recovery":
                console.print(f"  • Failure After: {result['failure_after_seconds']}s")
                console.print(f"  • Recovery Time: {result['recovery_duration']:.1f}s")
                console.print(f"  • Checkpoint Used: {result['checkpoint_used']}")
    
    # Recommendations
    recommendations = []
    
    # Check historical performance
    if "historical" in results and results["historical"].get("success"):
        duration = results["historical"]["duration"]
        if duration > 1800:  # > 30 minutes
            recommendations.append("Consider increasing parallelization for historical data processing")
    
    # Check concurrent capability
    if "concurrent" in results:
        success_rate = results["concurrent"].get("successful_runs", 0) / results["concurrent"].get("concurrent_runs", 1)
        if success_rate < 1.0:
            recommendations.append("Improve concurrent execution handling and resource isolation")
    
    # Check memory efficiency
    if "memory" in results and results["memory"].get("memory_violations", 0) > 0:
        recommendations.append("Implement more aggressive memory optimization strategies")
    
    # Check network resilience
    if "network" in results and results["network"].get("retry_count", 0) > 10:
        recommendations.append("Enhance retry logic and implement circuit breakers")
    
    # Check recovery capability
    if "recovery" in results and not results["recovery"].get("success"):
        recommendations.append("Improve checkpoint mechanism and recovery procedures")
    
    if recommendations:
        console.print(Panel(
            "\n".join(f"• {rec}" for rec in recommendations),
            title="[yellow]Optimization Recommendations[/yellow]",
            border_style="yellow"
        ))
    
    # Save detailed report
    report_path = Path("load_test_results") / f"report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    report_path.parent.mkdir(exist_ok=True)
    
    with open(report_path, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    console.print(f"\n[dim]Detailed report saved to: {report_path}[/dim]")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Load test the data pipeline")
    parser.add_argument(
        "--scenarios",
        nargs="+",
        choices=["historical", "concurrent", "memory", "network", "recovery"],
        help="Scenarios to run (default: all)"
    )
    parser.add_argument(
        "--concurrent-runs",
        type=int,
        default=3,
        help="Number of concurrent pipelines for concurrent test"
    )
    parser.add_argument(
        "--memory-limit",
        type=float,
        default=2.0,
        help="Memory limit in GB for resource test"
    )
    
    args = parser.parse_args()
    
    # Run tests
    asyncio.run(run_load_tests(args.scenarios))


if __name__ == "__main__":
    main()